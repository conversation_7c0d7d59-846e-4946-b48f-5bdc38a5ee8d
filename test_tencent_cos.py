#!/usr/bin/env python3
"""
腾讯云COS连接测试脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from storage_abstraction import S3StorageConfig, StorageType
from s3_storage_adapter import S3StorageAdapter

def test_tencent_cos():
    """测试腾讯云COS连接"""
    print("🔍 腾讯云COS连接测试")
    print("=" * 50)
    
    # 腾讯云COS配置 - 请替换为实际配置
    config = S3StorageConfig(
        storage_type=StorageType.S3,
        name="腾讯云COS测试",
        endpoint="https://cos.ap-shanghai.myqcloud.com",  # 替换为正确的区域
        access_key="your-secret-id",  # 替换为SecretId
        secret_key="your-secret-key",  # 替换为SecretKey
        bucket="",  # 先不指定bucket来列出所有bucket
        region="ap-shanghai"  # 替换为正确的区域
    )
    
    print(f"📋 配置信息:")
    print(f"   端点: {config.endpoint}")
    print(f"   区域: {config.region}")
    print(f"   Secret ID: {config.access_key[:10]}...")
    
    adapter = S3StorageAdapter(config)
    
    # 测试1: 列出存储桶
    print("\n📁 测试1: 列出存储桶...")
    try:
        buckets = adapter.list_buckets()
        print(f"✅ 成功列出 {len(buckets)} 个存储桶:")
        for i, bucket in enumerate(buckets, 1):
            print(f"   {i}. {bucket}")
        
        if buckets:
            # 测试2: 连接到第一个存储桶
            test_bucket = buckets[0]
            print(f"\n📦 测试2: 连接到存储桶 '{test_bucket}'...")
            
            # 更新配置以包含存储桶
            config.bucket = test_bucket
            test_adapter = S3StorageAdapter(config)
            
            try:
                result = test_adapter.list_files(max_keys=10)
                print(f"✅ 成功连接存储桶，找到 {len(result.files)} 个文件")
                
                if result.files:
                    print("📄 文件列表:")
                    for file_meta in result.files[:5]:  # 只显示前5个文件
                        print(f"   • {file_meta.key} ({file_meta.size} bytes)")
                
            except Exception as e:
                print(f"❌ 连接存储桶失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 列出存储桶失败: {e}")
        
        # 详细错误分析
        print("\n🔧 错误分析:")
        error_str = str(e).lower()
        
        if "403" in error_str:
            print("   • HTTP 403: 权限不足")
            print("   • 检查SecretId和SecretKey是否正确")
            print("   • 检查RAM用户是否有ListBucket权限")
        elif "404" in error_str:
            print("   • HTTP 404: 资源不存在")
            print("   • 检查端点URL是否正确")
            print("   • 检查区域配置是否正确")
        elif "signature" in error_str:
            print("   • 签名错误")
            print("   • 检查SecretKey是否正确")
            print("   • 检查时间同步")
        else:
            print(f"   • 其他错误: {e}")
        
        return False

def test_specific_bucket():
    """测试特定存储桶"""
    print("\n🎯 测试特定存储桶")
    print("=" * 30)
    
    # 测试问题中的存储桶
    config = S3StorageConfig(
        storage_type=StorageType.S3,
        name="腾讯云COS测试",
        endpoint="https://cos.ap-shanghai.myqcloud.com",  # 可能需要调整区域
        access_key="your-secret-id",  # 替换为实际值
        secret_key="your-secret-key",  # 替换为实际值
        bucket="h3c-deom-1319219496",  # 问题中的存储桶
        region="ap-shanghai"  # 可能需要调整
    )
    
    print(f"📦 测试存储桶: {config.bucket}")
    print(f"📍 区域: {config.region}")
    print(f"🌐 端点: {config.endpoint}")
    
    adapter = S3StorageAdapter(config)
    
    try:
        result = adapter.list_files(max_keys=10)
        print(f"✅ 存储桶连接成功，找到 {len(result.files)} 个文件")
        return True
    except Exception as e:
        print(f"❌ 存储桶连接失败: {e}")
        
        # 尝试不同的区域
        regions_to_try = [
            "ap-beijing", "ap-shanghai", "ap-guangzhou", 
            "ap-chengdu", "ap-nanjing", "ap-chongqing"
        ]
        
        print(f"\n🔄 尝试不同区域...")
        for region in regions_to_try:
            if region == config.region:
                continue  # 跳过已经测试过的区域
                
            print(f"   尝试区域: {region}")
            config.region = region
            config.endpoint = f"https://cos.{region}.myqcloud.com"
            
            try:
                test_adapter = S3StorageAdapter(config)
                result = test_adapter.list_files(max_keys=1)
                print(f"   ✅ 区域 {region} 连接成功！")
                return True
            except Exception as region_error:
                print(f"   ❌ 区域 {region} 失败: {str(region_error)[:50]}...")
        
        return False

if __name__ == "__main__":
    print("🚀 腾讯云COS诊断工具")
    print("⚠️ 注意: 请在运行前修改配置中的SecretId和SecretKey")
    print()
    
    # 基本连接测试
    success1 = test_tencent_cos()
    
    # 特定存储桶测试
    success2 = test_specific_bucket()
    
    print("\n📊 测试结果:")
    print(f"   基本连接: {'✅ 成功' if success1 else '❌ 失败'}")
    print(f"   特定存储桶: {'✅ 成功' if success2 else '❌ 失败'}")
    
    if not success1 and not success2:
        print("\n💡 建议:")
        print("   1. 检查SecretId和SecretKey是否正确")
        print("   2. 检查存储桶名称是否完整（包含APPID）")
        print("   3. 检查区域配置是否正确")
        print("   4. 在腾讯云控制台确认存储桶是否存在")
        print("   5. 检查RAM用户权限设置")
