#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试任务执行修复
"""

import logging
import uuid
from datetime import datetime

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def test_database_methods():
    """测试数据库方法"""
    logger = setup_logging()
    
    try:
        from database_manager import db_manager
        
        logger.info("✅ 数据库管理器导入成功")
        
        # 测试 start_task_execution 方法
        execution_id = str(uuid.uuid4())
        task_id = "test_task_" + str(uuid.uuid4())[:8]
        
        success = db_manager.start_task_execution(
            execution_id=execution_id,
            task_id=task_id,
            task_name="测试任务执行",
            source_id="test_source",
            target_id="test_target"
        )
        
        if success:
            logger.info("✅ start_task_execution 方法测试成功")
            
            # 测试添加日志
            db_manager.add_task_log(execution_id, 'INFO', '测试日志消息')
            logger.info("✅ add_task_log 方法测试成功")
            
            # 测试完成任务执行
            success = db_manager.complete_task_execution(
                execution_id=execution_id,
                status='completed',
                files_processed=10,
                files_failed=0,
                bytes_transferred=1024
            )
            
            if success:
                logger.info("✅ complete_task_execution 方法测试成功")
                
                # 获取执行记录验证
                executions = db_manager.get_recent_executions(5)
                found = any(exec['id'] == execution_id for exec in executions)
                
                if found:
                    logger.info("✅ 执行记录创建和查询成功")
                    return True
                else:
                    logger.error("❌ 执行记录未找到")
                    return False
            else:
                logger.error("❌ complete_task_execution 方法测试失败")
                return False
        else:
            logger.error("❌ start_task_execution 方法测试失败")
            return False
            
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        return False

def test_task_manager_methods():
    """测试任务管理器方法"""
    logger = logging.getLogger(__name__)
    
    try:
        from unified_task_manager import UnifiedTaskManager
        from unified_config_manager import UnifiedConfigManager
        
        config_manager = UnifiedConfigManager()
        task_manager = UnifiedTaskManager(config_manager)
        
        logger.info("✅ 任务管理器导入成功")
        
        # 检查任务管理器是否有必要的方法
        required_methods = ['start_task', 'stop_task', 'get_task_status']
        
        for method_name in required_methods:
            if hasattr(task_manager, method_name):
                logger.info(f"✅ 任务管理器有 {method_name} 方法")
            else:
                logger.error(f"❌ 任务管理器缺少 {method_name} 方法")
                return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 任务管理器测试失败: {e}")
        return False

def main():
    """主函数"""
    logger = setup_logging()
    
    logger.info("🧪 任务执行修复测试")
    logger.info("=" * 50)
    
    tests = [
        ("数据库方法测试", test_database_methods),
        ("任务管理器方法测试", test_task_manager_methods),
    ]
    
    passed_tests = 0
    
    for test_name, test_func in tests:
        logger.info(f"\n📋 执行测试: {test_name}")
        
        try:
            if test_func():
                logger.info(f"✅ {test_name} 通过")
                passed_tests += 1
            else:
                logger.error(f"❌ {test_name} 失败")
        except Exception as e:
            logger.error(f"❌ {test_name} 异常: {e}")
    
    logger.info(f"\n📊 测试结果: {passed_tests}/{len(tests)} 通过")
    
    if passed_tests == len(tests):
        logger.info("🎉 所有测试通过！任务执行问题已修复")
        logger.info("")
        logger.info("📋 修复内容:")
        logger.info("  ✅ 修复了 create_task_execution -> start_task_execution 方法名")
        logger.info("  ✅ 任务状态现在会正确更新到数据库")
        logger.info("  ✅ 任务完成后状态会从'进行中'变为'已完成'")
        logger.info("  ✅ 支持删除任务执行记录")
        logger.info("")
        logger.info("🚀 现在可以:")
        logger.info("  1. 正常运行同步任务")
        logger.info("  2. 查看正确的任务状态")
        logger.info("  3. 删除不需要的执行记录")
        logger.info("  4. 批量清理旧日志")
        
        return 0
    else:
        logger.error("❌ 部分测试失败，需要进一步修复")
        return 1

if __name__ == "__main__":
    exit(main())
