#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试子进程SMB修复方案
"""

import smb_storage_adapter
from storage_abstraction import StorageFactory, SMBStorageConfig, StorageType
import subprocess
import json
import base64
import os

def test_subprocess_smb_downloader():
    """测试子进程SMB下载器"""
    print("🧪 测试子进程SMB下载器")
    
    # 创建SMB配置
    config_data = {
        'hostname': 'Jay<PERSON>',
        'port': 445,
        'username': 'smb',
        'password': 'smbsmb',
        'domain': 'WORKGROUP',
        'share_name': 'hlmj',
        'root_path': ''
    }
    
    # 测试文件
    test_files = ['398.xml', '398_MonitorData.ini', 'BugCrashReporter.exe']
    
    print("📋 测试子进程下载:")
    success_count = 0
    
    for i, file_key in enumerate(test_files):
        print(f"   下载 {i+1}/{len(test_files)}: {file_key}")
        
        temp_file = None
        try:
            # 创建临时文件
            import tempfile
            with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False, encoding='utf-8') as f:
                json.dump({
                    'config': config_data,
                    'file_key': file_key
                }, f)
                temp_file = f.name

            # 准备命令
            script_path = 'smb_downloader.py'
            cmd = ['python', script_path, temp_file]

            # 执行子进程
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=30,
                cwd=os.getcwd(),
                encoding='utf-8',
                errors='ignore'
            )
            
            if result.returncode == 0:
                output = result.stdout.strip()
                # 查找SUCCESS行
                success_line = None
                for line in output.split('\n'):
                    if line.startswith("SUCCESS:"):
                        success_line = line
                        break

                if success_line:
                    # 解析成功结果：SUCCESS:size:output_file
                    parts = success_line.split(':', 2)
                    if len(parts) == 3:
                        size = int(parts[1])
                        output_file = parts[2]

                        # 从输出文件读取数据
                        try:
                            with open(output_file, 'rb') as f:
                                data = f.read()
                            # 清理输出文件
                            os.unlink(output_file)
                            print(f"     ✅ 成功: {len(data)} bytes")
                            success_count += 1
                        except Exception as e:
                            print(f"     ❌ 读取输出文件失败: {e}")
                    else:
                        print(f"     ❌ 输出格式错误: {output}")
                else:
                    # 查找FAILED或ERROR行
                    for line in output.split('\n'):
                        if line.startswith("FAILED:"):
                            print(f"     ❌ 下载失败")
                            break
                        elif line.startswith("ERROR:"):
                            error_msg = line[6:]
                            print(f"     ❌ 错误: {error_msg}")
                            break
                    else:
                        print(f"     ❌ 未知输出: {output}")
            else:
                print(f"     ❌ 子进程失败 (返回码: {result.returncode}): {result.stderr}")
                
        except subprocess.TimeoutExpired:
            print(f"     ❌ 超时")
        except Exception as e:
            print(f"     ❌ 异常: {e}")
        finally:
            # 清理临时文件
            if temp_file and os.path.exists(temp_file):
                try:
                    os.unlink(temp_file)
                except:
                    pass
    
    print(f"\n📊 子进程下载结果: {success_count}/{len(test_files)} 成功")
    return success_count >= len(test_files) * 0.8

def test_task_manager_subprocess_integration():
    """测试任务管理器子进程集成"""
    print("\n🧪 测试任务管理器子进程集成")
    
    # 创建SMB配置
    smb_config = SMBStorageConfig(
        storage_type=StorageType.SMB,
        hostname='Jayce',
        port=445,
        username='smb',
        password='smbsmb',
        domain='WORKGROUP',
        share_name='hlmj',
        root_path=''
    )
    
    # 模拟任务管理器的子进程下载方法
    def download_smb_file_with_subprocess(smb_config, file_key):
        """模拟任务管理器的子进程下载方法"""
        try:
            # 准备配置数据
            config_data = {
                'hostname': smb_config.hostname,
                'port': smb_config.port,
                'username': smb_config.username,
                'password': smb_config.password,
                'domain': smb_config.domain or '',
                'share_name': smb_config.share_name,
                'root_path': smb_config.root_path or ''
            }
            
            # 转换为JSON字符串
            config_json = json.dumps(config_data)
            
            # 创建临时文件
            import tempfile
            with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False, encoding='utf-8') as f:
                json.dump({
                    'config': config_data,
                    'file_key': file_key
                }, f)
                temp_file = f.name

            # 构建命令
            script_path = 'smb_downloader.py'
            cmd = ['python', script_path, temp_file]

            # 执行子进程
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=30,
                cwd=os.getcwd(),
                encoding='utf-8',
                errors='ignore'
            )

            # 清理临时文件
            try:
                os.unlink(temp_file)
            except:
                pass
            
            if result.returncode == 0:
                output = result.stdout.strip()
                # 查找SUCCESS行
                success_line = None
                for line in output.split('\n'):
                    if line.startswith("SUCCESS:"):
                        success_line = line
                        break

                if success_line:
                    # 解析成功结果：SUCCESS:size:output_file
                    parts = success_line.split(':', 2)
                    if len(parts) == 3:
                        size = int(parts[1])
                        output_file = parts[2]

                        # 从输出文件读取数据
                        try:
                            with open(output_file, 'rb') as f:
                                data = f.read()
                            # 清理输出文件
                            os.unlink(output_file)
                            return data
                        except Exception as e:
                            print(f"读取输出文件失败: {e}")
                            return None
                return None
            else:
                return None
                
        except Exception as e:
            return None
    
    print("📋 模拟任务管理器同步流程:")
    
    try:
        # 创建源适配器
        source_adapter = StorageFactory.create_adapter(smb_config)
        
        # 1. 扫描源文件
        print("1. 扫描源文件...")
        source_files = []
        result = source_adapter.list_files("", max_keys=3)
        source_files.extend(result.files)
        print(f"   ✅ 成功: 找到 {len(source_files)} 个文件")
        
        if len(source_files) < 2:
            print("   ❌ 文件数量不足")
            return False
        
        # 2. 使用子进程下载文件
        print("\n2. 使用子进程下载文件...")
        success_count = 0
        total_files = min(2, len(source_files))
        
        for i, file_meta in enumerate(source_files[:total_files]):
            print(f"   同步 {i+1}/{total_files}: {file_meta.key}")
            
            # 使用子进程下载
            try:
                file_data = download_smb_file_with_subprocess(smb_config, file_meta.key)

                if file_data is None:
                    print(f"     ❌ 下载失败")
                else:
                    print(f"     ✅ 下载成功: {len(file_data)} bytes")
                    success_count += 1

                    # 验证数据完整性
                    if len(file_data) == file_meta.size:
                        print(f"     ✅ 大小匹配: {len(file_data)} == {file_meta.size}")
                    else:
                        print(f"     ⚠️ 大小不匹配: {len(file_data)} != {file_meta.size}")
            except Exception as e:
                print(f"     ❌ 下载异常: {e}")
                

        
        print(f"\n📊 集成测试结果: {success_count}/{total_files} 成功")
        return success_count >= total_files * 0.8
        
    except Exception as e:
        print(f"❌ 集成测试异常: {e}")
        return False

def main():
    """主函数"""
    print("🧪 子进程SMB修复方案测试")
    print("=" * 60)
    
    tests = [
        ("子进程下载器测试", test_subprocess_smb_downloader),
        ("任务管理器集成测试", test_task_manager_subprocess_integration),
    ]
    
    passed_tests = 0
    
    for test_name, test_func in tests:
        print(f"\n📋 执行测试: {test_name}")
        
        try:
            if test_func():
                print(f"✅ {test_name} 通过")
                passed_tests += 1
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
            import traceback
            traceback.print_exc()
    
    print(f"\n📊 测试结果: {passed_tests}/{len(tests)} 通过")
    
    if passed_tests >= len(tests):
        print("🎉 子进程SMB修复方案完全成功！")
        print("")
        print("🚀 现在可以重新运行同步任务，应该能够:")
        print("  ✅ 成功扫描SMB源文件")
        print("  ✅ 稳定下载所有SMB文件")
        print("  ✅ 完全避免状态污染")
        print("  ✅ 彻底消除 STATUS_OBJECT_NAME_INVALID 错误")
        print("  ✅ 每个文件在独立进程中下载")
        print("")
        print("📋 修复原理:")
        print("  1. 每个SMB文件下载在独立的Python子进程中执行")
        print("  2. 子进程结束后，所有状态完全清除")
        print("  3. 不存在任何进程级别的状态共享或污染")
        print("  4. 通过base64编码安全传输二进制数据")
        
        return 0
    else:
        print("❌ 子进程SMB修复方案仍有问题")
        return 1

if __name__ == "__main__":
    exit(main())
