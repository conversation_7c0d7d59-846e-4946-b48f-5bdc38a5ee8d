#!/usr/bin/env python3
"""
全面修复JavaScript与Python format()冲突的问题
使用更安全的方法处理所有JavaScript代码
"""

import re
import shutil
from pathlib import Path

def fix_all_javascript_conflicts(file_path):
    """全面修复JavaScript与Python format()冲突"""
    
    # 备份原文件
    backup_path = str(file_path) + '.final_fix_backup'
    shutil.copy2(str(file_path), backup_path)
    print(f"已备份原文件到: {backup_path}")
    
    # 读取文件内容
    with open(str(file_path), 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 找到所有JavaScript代码块（在<script>标签内或JavaScript函数内）
    # 我们需要将所有在JavaScript上下文中的 ${...} 转换为 ${{...}}
    
    # 方法1: 处理所有在JavaScript字符串模板中的变量
    # 匹配模式: 在反引号字符串内的 ${...}
    def fix_template_literals(match):
        template_content = match.group(1)
        # 将所有 ${...} 转换为 ${{...}}，但要避免已经转换过的
        fixed_content = re.sub(r'\$\{([^}]+)\}', r'${{\1}}', template_content)
        return f'`{fixed_content}`'
    
    # 处理反引号模板字符串
    content = re.sub(r'`([^`]*\$\{[^`]*)`', fix_template_literals, content, flags=re.DOTALL)
    
    # 方法2: 处理所有剩余的JavaScript变量引用
    # 这些通常出现在JavaScript代码中但不在反引号字符串内
    
    # 查找所有可能的JavaScript变量模式
    js_var_patterns = [
        r'\$\{([a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)*)\}',  # 简单变量和属性访问
        r'\$\{([a-zA-Z_][a-zA-Z0-9_]*(?:\[[^\]]+\])*)\}',  # 数组访问
        r'\$\{([^}]*\([^}]*\)[^}]*)\}',  # 函数调用
        r'\$\{([^}]*\?\s*[^}]*:\s*[^}]*)\}',  # 三元运算符
        r'\$\{([^}]*===\s*[^}]*)\}',  # 比较运算符
        r'\$\{([^}]*!==\s*[^}]*)\}',  # 不等比较
        r'\$\{([^}]*\|\|\s*[^}]*)\}',  # 逻辑或
        r'\$\{([^}]*&&\s*[^}]*)\}',  # 逻辑与
        r'\$\{([^}]*\+\s*[^}]*)\}',  # 字符串连接
        r'\$\{([^}]*-\s*[^}]*)\}',  # 减法
        r'\$\{([^}]*\*\s*[^}]*)\}',  # 乘法
        r'\$\{([^}]*/\s*[^}]*)\}',  # 除法
        r'\$\{([^}]*%\s*[^}]*)\}',  # 取模
    ]
    
    total_fixes = 0
    
    # 应用所有修复模式
    for pattern in js_var_patterns:
        matches = re.findall(pattern, content)
        if matches:
            print(f"修复JavaScript表达式: 找到 {len(matches)} 个匹配")
            total_fixes += len(matches)
            content = re.sub(pattern, r'${{\1}}', content)
    
    # 方法3: 处理特殊情况 - 已经部分转义的情况
    # 修复 ${{...}} 中仍然包含单个 ${...} 的情况
    def fix_nested_braces(match):
        inner_content = match.group(1)
        # 如果内容中还有 ${...}，将其转换为 ${{...}}
        fixed_inner = re.sub(r'\$\{([^}]+)\}', r'${{\1}}', inner_content)
        return f'${{{{{fixed_inner}}}}}'
    
    # 处理嵌套的大括号情况
    nested_matches = re.findall(r'\$\{\{([^}]*\$\{[^}]*)\}\}', content)
    if nested_matches:
        print(f"修复嵌套大括号: 找到 {len(nested_matches)} 个匹配")
        total_fixes += len(nested_matches)
        content = re.sub(r'\$\{\{([^}]*\$\{[^}]*)\}\}', fix_nested_braces, content)
    
    # 方法4: 最后的安全检查 - 确保所有JavaScript上下文中的变量都被正确转义
    # 查找所有在JavaScript函数内的 ${...} 模式
    
    # 找到所有JavaScript函数
    js_function_pattern = r'function\s+\w+\s*\([^)]*\)\s*\{[^}]*\$\{[^}]*\}[^}]*\}'
    js_functions = re.findall(js_function_pattern, content, flags=re.DOTALL)
    
    if js_functions:
        print(f"在JavaScript函数中发现变量引用: {len(js_functions)} 个函数")
        # 对每个函数内的变量进行转义
        def fix_function_vars(match):
            func_content = match.group(0)
            fixed_func = re.sub(r'\$\{([^}]+)\}', r'${{\1}}', func_content)
            return fixed_func
        
        content = re.sub(js_function_pattern, fix_function_vars, content, flags=re.DOTALL)
        total_fixes += len(js_functions)
    
    # 写入修复后的内容
    with open(str(file_path), 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"修复完成:")
    print(f"  - 总修复数量: {total_fixes}")
    
    # 验证修复结果
    remaining_issues = re.findall(r'\$\{[^}]*[a-zA-Z][^}]*\}', content)
    remaining_issues = [issue for issue in remaining_issues if not issue.startswith('${{')]
    
    if remaining_issues:
        print(f"⚠️ 仍有 {len(remaining_issues)} 个可能的问题:")
        for issue in remaining_issues[:5]:  # 只显示前5个
            print(f"    {issue}")
        if len(remaining_issues) > 5:
            print(f"    ... 还有 {len(remaining_issues) - 5} 个")
    else:
        print("✅ 所有JavaScript变量冲突已修复!")
    
    return total_fixes, len(remaining_issues)

def main():
    """主函数"""
    file_path = Path("complete_web_interface.py")
    
    if not file_path.exists():
        print(f"❌ 文件不存在: {file_path}")
        return
    
    print("🔧 开始全面修复JavaScript与Python format()冲突...")
    print("=" * 70)
    
    try:
        fixed_count, remaining_count = fix_all_javascript_conflicts(file_path)
        
        print("\n" + "=" * 70)
        print("🎯 修复总结:")
        print(f"  - 文件: {file_path}")
        print(f"  - 修复数量: {fixed_count}")
        print(f"  - 剩余问题: {remaining_count}")
        
        if remaining_count == 0:
            print("\n🎉 修复成功! 所有JavaScript变量冲突已解决")
            print("💡 请重启Web服务器并测试功能")
        else:
            print(f"\n⚠️ 仍有 {remaining_count} 个问题需要手动检查")
            print("💡 建议检查剩余的问题并手动修复")
            
    except Exception as e:
        print(f"❌ 修复过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
