#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试SMB适配器注册
"""

import logging

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def test_smb_import():
    """测试SMB模块导入"""
    logger = setup_logging()
    
    try:
        # 测试smbprotocol库
        import smbprotocol
        logger.info("✅ smbprotocol库导入成功")

        # 测试SMB适配器导入
        import smb_storage_adapter
        logger.info("✅ SMB存储适配器导入成功")

        return True

    except ImportError as e:
        logger.error(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ 其他错误: {e}")
        return False

def test_storage_factory():
    """测试存储工厂注册"""
    logger = logging.getLogger(__name__)
    
    try:
        from storage_abstraction import StorageFactory, StorageType
        
        # 获取支持的存储类型
        supported_types = StorageFactory.get_supported_types()
        logger.info(f"支持的存储类型: {supported_types}")
        
        # 检查SMB是否在支持列表中
        if StorageType.SMB in supported_types:
            logger.info("✅ SMB存储类型已注册")
            return True
        else:
            logger.error("❌ SMB存储类型未注册")
            return False
            
    except Exception as e:
        logger.error(f"❌ 测试存储工厂失败: {e}")
        return False

def test_smb_config():
    """测试SMB配置"""
    logger = logging.getLogger(__name__)
    
    try:
        from storage_abstraction import SMBStorageConfig, StorageType
        
        # 创建SMB配置
        config = SMBStorageConfig(
            storage_type=StorageType.SMB,
            hostname="Jayce",
            port=445,
            username="smb",
            password="smbsmb",
            domain="WORKGROUP",
            share_name="hlmj",
            root_path="/"
        )
        
        logger.info("✅ SMB配置创建成功")
        logger.info(f"  主机: {config.hostname}")
        logger.info(f"  端口: {config.port}")
        logger.info(f"  用户: {config.username}")
        logger.info(f"  域: {config.domain}")
        logger.info(f"  共享: {config.share_name}")
        logger.info(f"  根路径: {config.root_path}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试SMB配置失败: {e}")
        return False

def test_smb_adapter_creation():
    """测试SMB适配器创建"""
    logger = logging.getLogger(__name__)
    
    try:
        from storage_abstraction import StorageFactory, SMBStorageConfig, StorageType
        
        # 创建SMB配置
        config = SMBStorageConfig(
            storage_type=StorageType.SMB,
            hostname="Jayce",
            port=445,
            username="smb",
            password="smbsmb",
            domain="WORKGROUP",
            share_name="hlmj",
            root_path="/"
        )
        
        # 创建SMB适配器
        adapter = StorageFactory.create_adapter(config)
        logger.info("✅ SMB适配器创建成功")
        logger.info(f"  适配器类型: {type(adapter).__name__}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试SMB适配器创建失败: {e}")
        return False

def test_smb_connection():
    """测试SMB连接"""
    logger = logging.getLogger(__name__)
    
    try:
        from storage_abstraction import StorageFactory, SMBStorageConfig, StorageType
        
        # 创建SMB配置
        config = SMBStorageConfig(
            storage_type=StorageType.SMB,
            hostname="Jayce",
            port=445,
            username="smb",
            password="smbsmb",
            domain="WORKGROUP",
            share_name="hlmj",
            root_path="/"
        )
        
        # 创建SMB适配器
        adapter = StorageFactory.create_adapter(config)
        
        # 测试连接
        logger.info("🔗 测试SMB连接...")
        success, message = adapter.test_connection()
        
        if success:
            logger.info(f"✅ SMB连接成功: {message}")
            return True
        else:
            logger.error(f"❌ SMB连接失败: {message}")
            return False
        
    except Exception as e:
        logger.error(f"❌ 测试SMB连接异常: {e}")
        return False

def test_smb_list_files():
    """测试SMB文件列表"""
    logger = logging.getLogger(__name__)
    
    try:
        from storage_abstraction import StorageFactory, SMBStorageConfig, StorageType
        
        # 创建SMB配置
        config = SMBStorageConfig(
            storage_type=StorageType.SMB,
            hostname="Jayce",
            port=445,
            username="smb",
            password="smbsmb",
            domain="WORKGROUP",
            share_name="hlmj",
            root_path="/"
        )
        
        # 创建SMB适配器
        adapter = StorageFactory.create_adapter(config)
        
        # 列出文件
        logger.info("📁 列出SMB文件...")
        result = adapter.list_files("", max_keys=10)
        
        logger.info(f"✅ 找到 {len(result.files)} 个文件")
        for i, file_meta in enumerate(result.files[:5]):  # 只显示前5个
            logger.info(f"  {i+1}. {file_meta.key} ({file_meta.size} bytes)")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试SMB文件列表失败: {e}")
        return False

def main():
    """主函数"""
    logger = setup_logging()
    
    logger.info("🧪 SMB适配器注册测试")
    logger.info("=" * 60)
    
    tests = [
        ("SMB模块导入测试", test_smb_import),
        ("存储工厂注册测试", test_storage_factory),
        ("SMB配置测试", test_smb_config),
        ("SMB适配器创建测试", test_smb_adapter_creation),
        ("SMB连接测试", test_smb_connection),
        ("SMB文件列表测试", test_smb_list_files),
    ]
    
    passed_tests = 0
    
    for test_name, test_func in tests:
        logger.info(f"\n📋 执行测试: {test_name}")
        
        try:
            if test_func():
                logger.info(f"✅ {test_name} 通过")
                passed_tests += 1
            else:
                logger.error(f"❌ {test_name} 失败")
                # 如果连接测试失败，跳过后续测试
                if "连接" in test_name:
                    logger.warning("⚠️ 连接失败，跳过后续测试")
                    break
        except Exception as e:
            logger.error(f"❌ {test_name} 异常: {e}")
    
    logger.info(f"\n📊 测试结果: {passed_tests}/{len(tests)} 通过")
    
    if passed_tests >= 4:  # 前4个测试通过就算成功
        logger.info("🎉 SMB适配器注册成功！")
        logger.info("")
        logger.info("📋 现在您可以:")
        logger.info("  1. 在网页界面中添加SMB数据源")
        logger.info("  2. 配置SMB连接参数")
        logger.info("  3. 测试SMB连接")
        logger.info("  4. 创建使用SMB的同步任务")
        
        return 0
    else:
        logger.error("❌ SMB适配器注册失败，需要修复问题")
        return 1

if __name__ == "__main__":
    exit(main())
