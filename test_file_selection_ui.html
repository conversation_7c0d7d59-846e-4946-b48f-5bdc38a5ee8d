<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件选择功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        
        .file-item {
            display: flex;
            align-items: center;
            padding: 10px;
            border: 1px solid #ddd;
            margin: 5px 0;
            border-radius: 4px;
            background: #f9f9f9;
        }
        
        .file-checkbox {
            margin-right: 15px;
            flex-shrink: 0;
        }
        
        .file-content {
            display: flex;
            align-items: center;
            flex: 1;
            cursor: pointer;
        }
        
        .file-icon {
            font-size: 24px;
            margin-right: 15px;
            min-width: 30px;
            text-align: center;
        }
        
        .file-info {
            flex: 1;
        }
        
        .file-name {
            font-weight: bold;
            margin-bottom: 4px;
        }
        
        .file-details {
            font-size: 12px;
            color: #666;
        }
        
        .checkbox-container {
            display: flex;
            align-items: center;
            position: relative;
            padding-left: 25px;
            cursor: pointer;
            font-size: 14px;
            user-select: none;
        }
        
        .checkbox-container input {
            position: absolute;
            opacity: 0;
            cursor: pointer;
            height: 0;
            width: 0;
        }
        
        .checkmark {
            position: absolute;
            top: 0;
            left: 0;
            height: 18px;
            width: 18px;
            background-color: #eee;
            border-radius: 3px;
            border: 1px solid #ddd;
            transition: all 0.2s;
        }
        
        .checkbox-container:hover input ~ .checkmark {
            background-color: #ccc;
        }
        
        .checkbox-container input:checked ~ .checkmark {
            background-color: #ff6b35;
            border-color: #ff6b35;
        }
        
        .checkmark:after {
            content: "";
            position: absolute;
            display: none;
        }
        
        .checkbox-container input:checked ~ .checkmark:after {
            display: block;
        }
        
        .checkbox-container .checkmark:after {
            left: 6px;
            top: 2px;
            width: 5px;
            height: 10px;
            border: solid white;
            border-width: 0 3px 3px 0;
            transform: rotate(45deg);
        }
        
        .directory-item {
            background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
        }
        
        .directory-item:hover {
            background: linear-gradient(135deg, #bbdefb 0%, #e1bee7 100%);
        }
        
        .controls {
            margin: 20px 0;
            padding: 15px;
            background: #f0f0f0;
            border-radius: 4px;
        }
        
        .btn {
            padding: 8px 16px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 文件选择功能测试</h1>
        
        <div class="controls">
            <label class="checkbox-container">
                <input type="checkbox" id="selectAll" onchange="toggleAll(this)">
                <span class="checkmark"></span>
                全选
            </label>
            
            <button class="btn btn-success" id="downloadBtn" onclick="downloadSelected()" disabled>
                📦 下载选中文件 (0)
            </button>
            
            <button class="btn btn-primary" onclick="addTestFile()">
                ➕ 添加测试文件
            </button>
        </div>
        
        <div class="status" id="status">
            准备就绪 - 请测试文件选择功能
        </div>
        
        <div id="fileList">
            <!-- 测试文件项 -->
            <div class="file-item">
                <div class="file-checkbox" onclick="event.stopPropagation();">
                    <label class="checkbox-container">
                        <input type="checkbox" class="file-checkbox-input" value="test1.txt" onchange="updateDownloadButton()">
                        <span class="checkmark"></span>
                    </label>
                </div>
                <div class="file-content">
                    <div class="file-icon">📄</div>
                    <div class="file-info">
                        <div class="file-name">test1.txt</div>
                        <div class="file-details">大小: 1.2 KB | 修改时间: 2023-12-01 10:30:00</div>
                    </div>
                </div>
            </div>
            
            <div class="file-item directory-item">
                <div class="file-checkbox" onclick="event.stopPropagation();">
                    <label class="checkbox-container">
                        <input type="checkbox" class="file-checkbox-input" value="folder1" disabled>
                        <span class="checkmark"></span>
                    </label>
                </div>
                <div class="file-content" onclick="navigateToFolder('folder1')">
                    <div class="file-icon">📁</div>
                    <div class="file-info">
                        <div class="file-name">folder1</div>
                        <div class="file-details">类型: 目录</div>
                    </div>
                </div>
            </div>
            
            <div class="file-item">
                <div class="file-checkbox" onclick="event.stopPropagation();">
                    <label class="checkbox-container">
                        <input type="checkbox" class="file-checkbox-input" value="image.png" onchange="updateDownloadButton()">
                        <span class="checkmark"></span>
                    </label>
                </div>
                <div class="file-content">
                    <div class="file-icon">🖼️</div>
                    <div class="file-info">
                        <div class="file-name">image.png</div>
                        <div class="file-details">大小: 256 KB | 修改时间: 2023-12-01 11:15:00</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function updateDownloadButton() {
            const selectedFiles = document.querySelectorAll('.file-checkbox-input:checked');
            const downloadBtn = document.getElementById('downloadBtn');
            const status = document.getElementById('status');
            
            downloadBtn.disabled = selectedFiles.length === 0;
            downloadBtn.textContent = selectedFiles.length > 0 ?
                `📦 下载选中文件 (${selectedFiles.length})` : '📦 下载选中文件 (0)';
            
            const fileNames = Array.from(selectedFiles).map(cb => cb.value);
            status.textContent = selectedFiles.length > 0 ?
                `已选择 ${selectedFiles.length} 个文件: ${fileNames.join(', ')}` :
                '准备就绪 - 请测试文件选择功能';
        }
        
        function toggleAll(checkbox) {
            const fileCheckboxes = document.querySelectorAll('.file-checkbox-input:not(:disabled)');
            fileCheckboxes.forEach(cb => {
                cb.checked = checkbox.checked;
            });
            updateDownloadButton();
        }
        
        function downloadSelected() {
            const selectedFiles = Array.from(document.querySelectorAll('.file-checkbox-input:checked'))
                                       .map(cb => cb.value);
            
            if (selectedFiles.length === 0) {
                alert('请选择要下载的文件');
                return;
            }
            
            alert(`模拟下载文件: ${selectedFiles.join(', ')}`);
        }
        
        function navigateToFolder(folderName) {
            alert(`模拟进入文件夹: ${folderName}`);
        }
        
        function addTestFile() {
            const fileList = document.getElementById('fileList');
            const fileCount = fileList.children.length + 1;
            
            const newFile = document.createElement('div');
            newFile.className = 'file-item';
            newFile.innerHTML = `
                <div class="file-checkbox" onclick="event.stopPropagation();">
                    <label class="checkbox-container">
                        <input type="checkbox" class="file-checkbox-input" value="test${fileCount}.txt" onchange="updateDownloadButton()">
                        <span class="checkmark"></span>
                    </label>
                </div>
                <div class="file-content">
                    <div class="file-icon">📄</div>
                    <div class="file-info">
                        <div class="file-name">test${fileCount}.txt</div>
                        <div class="file-details">大小: ${Math.floor(Math.random() * 100)} KB | 修改时间: ${new Date().toLocaleString()}</div>
                    </div>
                </div>
            `;
            
            fileList.appendChild(newFile);
        }
        
        // 初始化
        updateDownloadButton();
    </script>
</body>
</html>
