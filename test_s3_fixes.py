#!/usr/bin/env python3
"""
测试S3适配器修复的脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from storage_abstraction import S3StorageConfig, StorageType
from s3_storage_adapter import S3StorageAdapter

def test_aliyun_oss():
    """测试阿里云OSS连接和列出桶功能"""
    print("🔍 测试阿里云OSS连接...")
    
    # 示例配置 - 请替换为实际的配置
    config = S3StorageConfig(
        storage_type=StorageType.S3,
        name="阿里云OSS测试",
        endpoint="https://oss-cn-shanghai.aliyuncs.com",  # 替换为你的区域
        access_key="your-access-key",  # 替换为你的Access Key
        secret_key="your-secret-key",  # 替换为你的Secret Key
        bucket="",  # 空bucket用于列出所有bucket
        region="cn-shanghai"  # 替换为你的区域
    )
    
    adapter = S3StorageAdapter(config)
    
    print(f"📋 配置信息:")
    print(f"   端点: {config.endpoint}")
    print(f"   区域: {config.region}")
    
    # 测试列出存储桶
    print("\n📁 测试列出存储桶...")
    try:
        buckets = adapter.list_buckets()
        print(f"✅ 成功列出 {len(buckets)} 个存储桶:")
        for bucket in buckets:
            print(f"   - {bucket}")
        
        if buckets:
            # 测试连接到第一个bucket
            print(f"\n🔗 测试连接到存储桶 '{buckets[0]}'...")
            config.bucket = buckets[0]
            adapter = S3StorageAdapter(config)
            
            success, message = adapter.test_connection()
            if success:
                print(f"✅ {message}")
                
                # 尝试列出文件
                print("\n📄 尝试列出文件...")
                try:
                    result = adapter.list_files(max_keys=5)
                    print(f"✅ 成功列出 {len(result.files)} 个文件")
                    for file in result.files[:3]:  # 显示前3个文件
                        print(f"   - {file.key} ({file.size} bytes)")
                except Exception as e:
                    print(f"❌ 列出文件失败: {e}")
            else:
                print(f"❌ {message}")
        
    except Exception as e:
        print(f"❌ 列出存储桶失败: {e}")
        
        # 提供调试信息
        print("\n🔧 调试信息:")
        print("   请检查以下配置:")
        print("   1. Access Key ID 是否正确")
        print("   2. Secret Access Key 是否正确")
        print("   3. 区域设置是否正确")
        print("   4. 网络连接是否正常")

def test_standard_s3():
    """测试标准S3连接"""
    print("\n🔍 测试标准S3连接...")
    
    # 示例配置 - 请替换为实际的配置
    config = S3StorageConfig(
        storage_type=StorageType.S3,
        name="标准S3测试",
        endpoint="https://s3.amazonaws.com",
        access_key="your-access-key",  # 替换为你的Access Key
        secret_key="your-secret-key",  # 替换为你的Secret Key
        bucket="",  # 空bucket用于列出所有bucket
        region="us-east-1"
    )
    
    adapter = S3StorageAdapter(config)
    
    print(f"📋 配置信息:")
    print(f"   端点: {config.endpoint}")
    print(f"   区域: {config.region}")
    
    # 测试列出存储桶
    print("\n📁 测试列出存储桶...")
    try:
        buckets = adapter.list_buckets()
        print(f"✅ 成功列出 {len(buckets)} 个存储桶:")
        for bucket in buckets:
            print(f"   - {bucket}")
    except Exception as e:
        print(f"❌ 列出存储桶失败: {e}")

if __name__ == "__main__":
    print("🚀 S3适配器修复测试")
    print("=" * 50)
    
    print("⚠️ 注意: 请在运行前修改配置中的Access Key和Secret Key")
    print()
    
    # 测试阿里云OSS
    test_aliyun_oss()
    
    # 测试标准S3
    test_standard_s3()
    
    print("\n✅ 测试完成!")
    print("\n💡 如果测试失败，请检查:")
    print("   1. 网络连接")
    print("   2. Access Key和Secret Key是否正确")
    print("   3. 区域设置是否正确")
    print("   4. 存储桶权限设置")
