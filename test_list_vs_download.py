#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试list_files对下载的影响
"""

import smb_storage_adapter
from storage_abstraction import StorageFactory, SMBStorageConfig, StorageType

def test_list_vs_download():
    """测试list_files对下载的影响"""
    print("🧪 测试list_files对下载的影响")
    
    config = SMBStorageConfig(
        storage_type=StorageType.SMB,
        hostname='<PERSON><PERSON>',
        port=445,
        username='smb',
        password='smbsmb',
        domain='WORKGROUP',
        share_name='hlmj',
        root_path=''
    )
    
    adapter = StorageFactory.create_adapter(config)
    
    # 先下载文件
    print('1. 先下载 398.xml...')
    data1 = adapter.get_file('398.xml')
    if data1:
        print(f'   ✅ 成功: {len(data1)} bytes')
    else:
        print('   ❌ 失败')
    
    # 然后列出文件
    print('2. 列出文件...')
    try:
        result = adapter.list_files('', max_keys=3)
        print(f'   ✅ 成功: 找到 {len(result.files)} 个文件')
    except Exception as e:
        print(f'   ❌ 失败: {e}')
    
    # 再次下载文件
    print('3. 再次下载 398.xml...')
    data2 = adapter.get_file('398.xml')
    if data2:
        print(f'   ✅ 成功: {len(data2)} bytes')
    else:
        print('   ❌ 失败')
    
    # 分析结果
    if data1 and data2:
        print("\n📊 结论: list_files 不影响下载功能")
        return True
    elif data1 and not data2:
        print("\n📊 结论: list_files 破坏了下载功能")
        return False
    elif not data1 and data2:
        print("\n📊 结论: 第一次下载失败，但第二次成功")
        return False
    else:
        print("\n📊 结论: 下载功能完全失败")
        return False

if __name__ == "__main__":
    test_list_vs_download()
