#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试修复是否工作
"""

import logging
import sys

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_smb_condition():
    """测试SMB条件判断"""
    print("🧪 测试SMB条件判断")
    
    try:
        import smb_storage_adapter
        from storage_abstraction import StorageFactory, SMBStorageConfig, StorageType
        
        # 创建SMB配置
        config = SMBStorageConfig(
            storage_type=StorageType.SMB,
            hostname='<PERSON><PERSON>',
            port=445,
            username='smb',
            password='smbsmb',
            domain='WORKGROUP',
            share_name='hlmj',
            root_path=''
        )
        
        # 创建适配器
        adapter = StorageFactory.create_adapter(config)
        
        print(f"适配器类型: {type(adapter)}")
        print(f"配置类型: {type(adapter.config)}")
        print(f"存储类型: {adapter.config.storage_type}")
        print(f"存储类型名称: {adapter.config.storage_type.name}")
        
        # 测试条件判断
        condition = hasattr(adapter.config, 'storage_type') and adapter.config.storage_type.name == 'SMB'
        print(f"条件判断结果: {condition}")
        
        if condition:
            print("✅ 条件判断正确，会使用子进程下载")
        else:
            print("❌ 条件判断错误，不会使用子进程下载")
            
        return condition
        
    except Exception as e:
        print(f"❌ 异常: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    if test_smb_condition():
        print("🎉 条件判断测试成功！")
        sys.exit(0)
    else:
        print("❌ 条件判断测试失败！")
        sys.exit(1)
