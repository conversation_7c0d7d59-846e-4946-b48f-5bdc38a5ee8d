# LightRek 统一存储同步工具 (便携版)

## 使用说明

### 启动程序
- Windows: 双击 `start.bat`
- Linux/Mac: 运行 `./start.sh` 或 `python3 main_simple.py`

### 功能特性
- ✅ 多种存储类型支持 (S3, SFTP, SMB, FTP, 本地)
- ✅ 统一配置管理
- ✅ 任务管理和监控
- ✅ Web管理界面 (需要Flask)

### 依赖要求
- Python 3.7+
- 可选: Flask (Web界面)
- 可选: paramiko (SFTP支持)
- 可选: smbprotocol (SMB支持)

### 安装依赖
```bash
pip install flask paramiko smbprotocol requests
```

### 访问地址
程序启动后访问: http://localhost:8001

### 注意事项
- 这是便携版，需要Python环境
- 部分功能需要安装对应的依赖包
- 查看 requirements_unified.txt 了解完整依赖列表
