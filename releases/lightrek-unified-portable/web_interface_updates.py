"""
Web界面更新示例 - 支持新的存储类型
这个文件展示了如何更新现有的Web界面以支持新的存储类型
"""

import json
from flask import Flask, request, jsonify, render_template_string
from unified_config_manager import UnifiedConfigManager
from unified_task_manager import UnifiedTaskManager

# 导入所有存储适配器
import s3_storage_adapter
import sftp_storage_adapter
import smb_storage_adapter
import ftp_storage_adapter
import local_storage_adapter

app = Flask(__name__)
config_manager = UnifiedConfigManager()
task_manager = UnifiedTaskManager(config_manager)

# 存储类型配置表单HTML模板
STORAGE_CONFIG_TEMPLATE = """
<!DOCTYPE html>
<html>
<head>
    <title>存储配置</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, select, textarea { width: 300px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        button { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .storage-type { margin-bottom: 20px; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .hidden { display: none; }
    </style>
</head>
<body>
    <h1>存储配置管理</h1>
    
    <div class="form-group">
        <label for="storage_type">存储类型:</label>
        <select id="storage_type" onchange="showStorageConfig()">
            <option value="">请选择存储类型</option>
            <option value="s3">S3对象存储</option>
            <option value="sftp">SFTP</option>
            <option value="smb">SMB/CIFS</option>
            <option value="ftp">FTP/FTPS</option>
            <option value="local">本地文件系统</option>
        </select>
    </div>
    
    <!-- S3配置 -->
    <div id="s3_config" class="storage-type hidden">
        <h3>S3对象存储配置</h3>
        <div class="form-group">
            <label>名称:</label>
            <input type="text" id="s3_name" placeholder="存储名称">
        </div>
        <div class="form-group">
            <label>描述:</label>
            <textarea id="s3_description" placeholder="存储描述"></textarea>
        </div>
        <div class="form-group">
            <label>访问密钥:</label>
            <input type="text" id="s3_access_key" placeholder="Access Key">
        </div>
        <div class="form-group">
            <label>密钥:</label>
            <input type="password" id="s3_secret_key" placeholder="Secret Key">
        </div>
        <div class="form-group">
            <label>端点URL:</label>
            <input type="text" id="s3_endpoint" placeholder="https://s3.amazonaws.com">
        </div>
        <div class="form-group">
            <label>区域:</label>
            <input type="text" id="s3_region" placeholder="us-east-1">
        </div>
        <div class="form-group">
            <label>存储桶:</label>
            <input type="text" id="s3_bucket" placeholder="bucket-name">
        </div>
    </div>
    
    <!-- SFTP配置 -->
    <div id="sftp_config" class="storage-type hidden">
        <h3>SFTP配置</h3>
        <div class="form-group">
            <label>名称:</label>
            <input type="text" id="sftp_name" placeholder="存储名称">
        </div>
        <div class="form-group">
            <label>描述:</label>
            <textarea id="sftp_description" placeholder="存储描述"></textarea>
        </div>
        <div class="form-group">
            <label>主机名:</label>
            <input type="text" id="sftp_hostname" placeholder="sftp.example.com">
        </div>
        <div class="form-group">
            <label>端口:</label>
            <input type="number" id="sftp_port" value="22">
        </div>
        <div class="form-group">
            <label>用户名:</label>
            <input type="text" id="sftp_username" placeholder="username">
        </div>
        <div class="form-group">
            <label>密码:</label>
            <input type="password" id="sftp_password" placeholder="password">
        </div>
        <div class="form-group">
            <label>私钥文件路径:</label>
            <input type="text" id="sftp_private_key_path" placeholder="/path/to/private_key">
        </div>
        <div class="form-group">
            <label>私钥密码:</label>
            <input type="password" id="sftp_private_key_passphrase" placeholder="key passphrase">
        </div>
        <div class="form-group">
            <label>根路径:</label>
            <input type="text" id="sftp_root_path" value="/" placeholder="/data">
        </div>
    </div>
    
    <!-- SMB配置 -->
    <div id="smb_config" class="storage-type hidden">
        <h3>SMB/CIFS配置</h3>
        <div class="form-group">
            <label>名称:</label>
            <input type="text" id="smb_name" placeholder="存储名称">
        </div>
        <div class="form-group">
            <label>描述:</label>
            <textarea id="smb_description" placeholder="存储描述"></textarea>
        </div>
        <div class="form-group">
            <label>主机名:</label>
            <input type="text" id="smb_hostname" placeholder="*************">
        </div>
        <div class="form-group">
            <label>端口:</label>
            <input type="number" id="smb_port" value="445">
        </div>
        <div class="form-group">
            <label>用户名:</label>
            <input type="text" id="smb_username" placeholder="domain\\username">
        </div>
        <div class="form-group">
            <label>密码:</label>
            <input type="password" id="smb_password" placeholder="password">
        </div>
        <div class="form-group">
            <label>域:</label>
            <input type="text" id="smb_domain" placeholder="DOMAIN">
        </div>
        <div class="form-group">
            <label>共享名:</label>
            <input type="text" id="smb_share_name" placeholder="SharedFolder">
        </div>
        <div class="form-group">
            <label>根路径:</label>
            <input type="text" id="smb_root_path" value="/" placeholder="/data">
        </div>
    </div>
    
    <!-- FTP配置 -->
    <div id="ftp_config" class="storage-type hidden">
        <h3>FTP/FTPS配置</h3>
        <div class="form-group">
            <label>名称:</label>
            <input type="text" id="ftp_name" placeholder="存储名称">
        </div>
        <div class="form-group">
            <label>描述:</label>
            <textarea id="ftp_description" placeholder="存储描述"></textarea>
        </div>
        <div class="form-group">
            <label>主机名:</label>
            <input type="text" id="ftp_hostname" placeholder="ftp.example.com">
        </div>
        <div class="form-group">
            <label>端口:</label>
            <input type="number" id="ftp_port" value="21">
        </div>
        <div class="form-group">
            <label>用户名:</label>
            <input type="text" id="ftp_username" placeholder="ftpuser">
        </div>
        <div class="form-group">
            <label>密码:</label>
            <input type="password" id="ftp_password" placeholder="password">
        </div>
        <div class="form-group">
            <label>使用TLS加密:</label>
            <input type="checkbox" id="ftp_use_tls">
        </div>
        <div class="form-group">
            <label>被动模式:</label>
            <input type="checkbox" id="ftp_passive_mode" checked>
        </div>
        <div class="form-group">
            <label>根路径:</label>
            <input type="text" id="ftp_root_path" value="/" placeholder="/uploads">
        </div>
    </div>
    
    <!-- 本地存储配置 -->
    <div id="local_config" class="storage-type hidden">
        <h3>本地文件系统配置</h3>
        <div class="form-group">
            <label>名称:</label>
            <input type="text" id="local_name" placeholder="存储名称">
        </div>
        <div class="form-group">
            <label>描述:</label>
            <textarea id="local_description" placeholder="存储描述"></textarea>
        </div>
        <div class="form-group">
            <label>根路径:</label>
            <input type="text" id="local_root_path" placeholder="/data/storage">
        </div>
    </div>
    
    <div class="form-group">
        <button onclick="testConnection()">测试连接</button>
        <button onclick="saveConfig()">保存配置</button>
    </div>
    
    <div id="result"></div>
    
    <script>
        function showStorageConfig() {
            const storageType = document.getElementById('storage_type').value;
            const configs = ['s3_config', 'sftp_config', 'smb_config', 'ftp_config', 'local_config'];
            
            configs.forEach(config => {
                document.getElementById(config).classList.add('hidden');
            });
            
            if (storageType) {
                document.getElementById(storageType + '_config').classList.remove('hidden');
            }
        }
        
        function getConfigData() {
            const storageType = document.getElementById('storage_type').value;
            const config = {};
            
            if (storageType === 's3') {
                config.name = document.getElementById('s3_name').value;
                config.description = document.getElementById('s3_description').value;
                config.access_key = document.getElementById('s3_access_key').value;
                config.secret_key = document.getElementById('s3_secret_key').value;
                config.endpoint = document.getElementById('s3_endpoint').value;
                config.region = document.getElementById('s3_region').value;
                config.bucket = document.getElementById('s3_bucket').value;
            } else if (storageType === 'sftp') {
                config.name = document.getElementById('sftp_name').value;
                config.description = document.getElementById('sftp_description').value;
                config.hostname = document.getElementById('sftp_hostname').value;
                config.port = parseInt(document.getElementById('sftp_port').value);
                config.username = document.getElementById('sftp_username').value;
                config.password = document.getElementById('sftp_password').value;
                config.private_key_path = document.getElementById('sftp_private_key_path').value;
                config.private_key_passphrase = document.getElementById('sftp_private_key_passphrase').value;
                config.root_path = document.getElementById('sftp_root_path').value;
            } else if (storageType === 'smb') {
                config.name = document.getElementById('smb_name').value;
                config.description = document.getElementById('smb_description').value;
                config.hostname = document.getElementById('smb_hostname').value;
                config.port = parseInt(document.getElementById('smb_port').value);
                config.username = document.getElementById('smb_username').value;
                config.password = document.getElementById('smb_password').value;
                config.domain = document.getElementById('smb_domain').value;
                config.share_name = document.getElementById('smb_share_name').value;
                config.root_path = document.getElementById('smb_root_path').value;
            } else if (storageType === 'ftp') {
                config.name = document.getElementById('ftp_name').value;
                config.description = document.getElementById('ftp_description').value;
                config.hostname = document.getElementById('ftp_hostname').value;
                config.port = parseInt(document.getElementById('ftp_port').value);
                config.username = document.getElementById('ftp_username').value;
                config.password = document.getElementById('ftp_password').value;
                config.use_tls = document.getElementById('ftp_use_tls').checked;
                config.passive_mode = document.getElementById('ftp_passive_mode').checked;
                config.root_path = document.getElementById('ftp_root_path').value;
            } else if (storageType === 'local') {
                config.name = document.getElementById('local_name').value;
                config.description = document.getElementById('local_description').value;
                config.root_path = document.getElementById('local_root_path').value;
            }
            
            return { storageType, config };
        }
        
        function testConnection() {
            const { storageType, config } = getConfigData();
            
            if (!storageType) {
                alert('请选择存储类型');
                return;
            }
            
            fetch('/api/test_connection', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ storage_type: storageType, config: config })
            })
            .then(response => response.json())
            .then(data => {
                const result = document.getElementById('result');
                if (data.success) {
                    result.innerHTML = '<p style="color: green;">✓ 连接测试成功: ' + data.message + '</p>';
                } else {
                    result.innerHTML = '<p style="color: red;">✗ 连接测试失败: ' + data.message + '</p>';
                }
            })
            .catch(error => {
                document.getElementById('result').innerHTML = '<p style="color: red;">请求失败: ' + error + '</p>';
            });
        }
        
        function saveConfig() {
            const { storageType, config } = getConfigData();
            
            if (!storageType) {
                alert('请选择存储类型');
                return;
            }
            
            const storageId = prompt('请输入存储ID:');
            if (!storageId) return;
            
            const isSource = confirm('是否作为数据源？(取消=目标存储)');
            
            fetch('/api/save_storage', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ 
                    storage_id: storageId,
                    storage_type: storageType, 
                    config: config,
                    is_source: isSource
                })
            })
            .then(response => response.json())
            .then(data => {
                const result = document.getElementById('result');
                if (data.success) {
                    result.innerHTML = '<p style="color: green;">✓ 配置保存成功</p>';
                } else {
                    result.innerHTML = '<p style="color: red;">✗ 配置保存失败: ' + data.message + '</p>';
                }
            })
            .catch(error => {
                document.getElementById('result').innerHTML = '<p style="color: red;">请求失败: ' + error + '</p>';
            });
        }
    </script>
</body>
</html>
"""

@app.route('/')
def index():
    """主页"""
    return render_template_string(STORAGE_CONFIG_TEMPLATE)

@app.route('/api/storage_types')
def get_storage_types():
    """获取支持的存储类型"""
    return jsonify(config_manager.get_supported_storage_types())

@app.route('/api/test_connection', methods=['POST'])
def test_connection():
    """测试存储连接"""
    try:
        data = request.json
        storage_type = data.get('storage_type')
        config_data = data.get('config')
        
        # 创建临时存储配置进行测试
        temp_id = 'temp_test'
        if config_manager.add_source(temp_id, storage_type, config_data):
            success, message = config_manager.test_storage_connection(temp_id, is_source=True)
            # 清理临时配置
            config_manager.remove_source(temp_id)
            return jsonify({'success': success, 'message': message})
        else:
            return jsonify({'success': False, 'message': '配置验证失败'})
    
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/save_storage', methods=['POST'])
def save_storage():
    """保存存储配置"""
    try:
        data = request.json
        storage_id = data.get('storage_id')
        storage_type = data.get('storage_type')
        config_data = data.get('config')
        is_source = data.get('is_source', True)
        
        if is_source:
            success = config_manager.add_source(storage_id, storage_type, config_data)
        else:
            success = config_manager.add_target(storage_id, storage_type, config_data)
        
        return jsonify({'success': success})
    
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/storages')
def get_storages():
    """获取所有存储配置"""
    return jsonify({
        'sources': config_manager.get_all_sources(),
        'targets': config_manager.get_all_targets()
    })

@app.route('/api/tasks')
def get_tasks():
    """获取所有任务"""
    tasks = {}
    for task_id, task in task_manager.get_all_tasks().items():
        tasks[task_id] = {
            'name': task.name,
            'description': task.description,
            'source_id': task.source_id,
            'target_id': task.target_id,
            'status': task.last_status,
            'last_run': task.last_run
        }
    return jsonify(tasks)

@app.route('/api/task_status/<task_id>')
def get_task_status(task_id):
    """获取任务状态"""
    status = task_manager.get_task_status(task_id)
    if status:
        return jsonify(status)
    else:
        return jsonify({'status': 'not_found'})

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=8001)
