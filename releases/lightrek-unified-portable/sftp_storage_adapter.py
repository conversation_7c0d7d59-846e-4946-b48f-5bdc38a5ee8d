"""
SFTP存储适配器 - 基于paramiko库实现
"""

from storage_abstraction import StorageAdapter, SFTPStorageConfig, FileMetadata, ListResult, StorageType, StorageFactory
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import os
import stat
import posixpath
try:
    import paramiko
    PARAMIKO_AVAILABLE = True
except ImportError:
    PARAMIKO_AVAILABLE = False
    paramiko = None


class SFTPStorageAdapter(StorageAdapter):
    """SFTP存储适配器"""
    
    def __init__(self, config: SFTPStorageConfig):
        if not PARAMIKO_AVAILABLE:
            raise ImportError("paramiko库未安装，请运行: pip install paramiko")
        
        super().__init__(config)
        self.config: SFTPStorageConfig = config
        self._ssh_client = None
        self._sftp_client = None
    
    def _connect(self) -> bool:
        """建立SFTP连接"""
        try:
            if self._ssh_client is not None:
                return True
            
            self._ssh_client = paramiko.SSHClient()
            self._ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            
            # 准备连接参数
            connect_kwargs = {
                'hostname': self.config.hostname,
                'port': self.config.port,
                'username': self.config.username,
                'timeout': 30
            }
            
            # 认证方式
            if self.config.private_key_path:
                # 使用私钥认证
                if os.path.exists(self.config.private_key_path):
                    private_key = paramiko.RSAKey.from_private_key_file(
                        self.config.private_key_path,
                        password=self.config.private_key_passphrase
                    )
                    connect_kwargs['pkey'] = private_key
                else:
                    raise FileNotFoundError(f"私钥文件不存在: {self.config.private_key_path}")
            elif self.config.password:
                # 使用密码认证
                connect_kwargs['password'] = self.config.password
            else:
                raise ValueError("必须提供密码或私钥文件")
            
            # 建立SSH连接
            self._ssh_client.connect(**connect_kwargs)
            
            # 建立SFTP连接
            self._sftp_client = self._ssh_client.open_sftp()
            
            return True
        except Exception as e:
            self._disconnect()
            raise Exception(f"SFTP连接失败: {str(e)}")
    
    def _disconnect(self):
        """断开SFTP连接"""
        if self._sftp_client:
            try:
                self._sftp_client.close()
            except:
                pass
            self._sftp_client = None
        
        if self._ssh_client:
            try:
                self._ssh_client.close()
            except:
                pass
            self._ssh_client = None
    
    def _normalize_path(self, path: str) -> str:
        """规范化路径"""
        if not path:
            return self.config.root_path
        
        # 使用POSIX路径（SFTP服务器通常是Unix/Linux）
        if not path.startswith('/'):
            path = posixpath.join(self.config.root_path, path)
        
        return posixpath.normpath(path)
    
    def _get_relative_path(self, full_path: str) -> str:
        """获取相对于根路径的路径"""
        root = self.config.root_path.rstrip('/')
        if full_path.startswith(root):
            relative = full_path[len(root):].lstrip('/')
            return relative if relative else ""
        return full_path
    
    def test_connection(self) -> Tuple[bool, str]:
        """测试连接"""
        try:
            self._connect()
            
            # 测试访问根目录
            root_path = self._normalize_path("")
            try:
                attrs = self._sftp_client.stat(root_path)
                if stat.S_ISDIR(attrs.st_mode):
                    return True, f"SFTP连接成功，根目录: {root_path}"
                else:
                    return False, f"根路径不是目录: {root_path}"
            except FileNotFoundError:
                return False, f"根目录不存在: {root_path}"
            except PermissionError:
                return False, f"无权限访问根目录: {root_path}"
        except Exception as e:
            return False, f"SFTP连接失败: {str(e)}"
        finally:
            self._disconnect()
    
    def list_files(self, prefix: str = "", max_keys: int = 1000, 
                   continuation_token: Optional[str] = None) -> ListResult:
        """列出文件"""
        try:
            self._connect()
            
            files = []
            start_path = self._normalize_path(prefix)
            
            # 如果指定了continuation_token，从该位置开始
            skip_until = continuation_token
            found_start = continuation_token is None
            
            def scan_directory(dir_path: str, current_prefix: str = ""):
                nonlocal files, found_start, skip_until
                
                if len(files) >= max_keys:
                    return
                
                try:
                    for item in self._sftp_client.listdir_attr(dir_path):
                        if len(files) >= max_keys:
                            break
                        
                        item_path = posixpath.join(dir_path, item.filename)
                        relative_path = self._get_relative_path(item_path)
                        
                        # 检查是否匹配前缀
                        if prefix and not relative_path.startswith(prefix):
                            continue
                        
                        # 处理分页
                        if not found_start:
                            if relative_path == skip_until:
                                found_start = True
                            continue
                        
                        if stat.S_ISREG(item.st_mode):
                            # 文件
                            last_modified = datetime.fromtimestamp(item.st_mtime) if item.st_mtime else datetime.now()
                            
                            file_meta = FileMetadata(
                                key=relative_path,
                                size=item.st_size or 0,
                                last_modified=last_modified,
                                etag=None,  # SFTP不支持ETag
                                content_type='binary/octet-stream'
                            )
                            files.append(file_meta)
                        
                        elif stat.S_ISDIR(item.st_mode):
                            # 递归扫描子目录
                            scan_directory(item_path, relative_path + "/")
                
                except PermissionError:
                    # 跳过无权限的目录
                    pass
                except Exception:
                    # 跳过其他错误的目录
                    pass
            
            # 开始扫描
            if os.path.isdir(start_path) if hasattr(os, 'path') else True:
                scan_directory(start_path)
            else:
                # 如果prefix指向具体文件
                try:
                    attrs = self._sftp_client.stat(start_path)
                    if stat.S_ISREG(attrs.st_mode):
                        relative_path = self._get_relative_path(start_path)
                        last_modified = datetime.fromtimestamp(attrs.st_mtime) if attrs.st_mtime else datetime.now()
                        
                        file_meta = FileMetadata(
                            key=relative_path,
                            size=attrs.st_size or 0,
                            last_modified=last_modified,
                            etag=None,
                            content_type='binary/octet-stream'
                        )
                        files.append(file_meta)
                except:
                    pass
            
            # 确定是否还有更多文件
            is_truncated = len(files) >= max_keys
            next_token = files[-1].key if is_truncated and files else None
            
            return ListResult(
                files=files,
                is_truncated=is_truncated,
                next_token=next_token
            )
        
        except Exception as e:
            raise Exception(f"列出文件失败: {str(e)}")
        finally:
            self._disconnect()
    
    def get_file(self, key: str) -> Optional[bytes]:
        """下载文件"""
        try:
            self._connect()
            
            file_path = self._normalize_path(key)
            
            # 使用BytesIO来读取文件内容
            from io import BytesIO
            file_obj = BytesIO()
            
            self._sftp_client.getfo(file_path, file_obj)
            file_obj.seek(0)
            return file_obj.read()
        
        except Exception:
            return None
        finally:
            self._disconnect()
    
    def put_file(self, key: str, data: bytes, 
                 content_type: str = 'binary/octet-stream',
                 metadata: Optional[Dict[str, str]] = None) -> bool:
        """上传文件"""
        try:
            self._connect()
            
            file_path = self._normalize_path(key)
            
            # 确保目录存在
            dir_path = posixpath.dirname(file_path)
            if dir_path and dir_path != '/':
                self._ensure_directory_exists(dir_path)
            
            # 使用BytesIO来上传文件内容
            from io import BytesIO
            file_obj = BytesIO(data)
            
            self._sftp_client.putfo(file_obj, file_path)
            
            # SFTP不支持自定义元数据，但可以设置文件权限
            self._sftp_client.chmod(file_path, 0o644)
            
            return True
        
        except Exception:
            return False
        finally:
            self._disconnect()
    
    def _ensure_directory_exists(self, dir_path: str):
        """确保目录存在"""
        try:
            self._sftp_client.stat(dir_path)
        except FileNotFoundError:
            # 目录不存在，递归创建
            parent_dir = posixpath.dirname(dir_path)
            if parent_dir and parent_dir != '/' and parent_dir != dir_path:
                self._ensure_directory_exists(parent_dir)
            
            self._sftp_client.mkdir(dir_path)
    
    def delete_file(self, key: str) -> bool:
        """删除文件"""
        try:
            self._connect()
            
            file_path = self._normalize_path(key)
            self._sftp_client.remove(file_path)
            return True
        
        except Exception:
            return False
        finally:
            self._disconnect()
    
    def get_file_metadata(self, key: str) -> Optional[FileMetadata]:
        """获取文件元数据"""
        try:
            self._connect()
            
            file_path = self._normalize_path(key)
            attrs = self._sftp_client.stat(file_path)
            
            if stat.S_ISREG(attrs.st_mode):
                last_modified = datetime.fromtimestamp(attrs.st_mtime) if attrs.st_mtime else datetime.now()
                
                return FileMetadata(
                    key=key,
                    size=attrs.st_size or 0,
                    last_modified=last_modified,
                    etag=None,  # SFTP不支持ETag
                    content_type='binary/octet-stream'
                )
            return None
        
        except Exception:
            return None
        finally:
            self._disconnect()
    
    def file_exists(self, key: str) -> bool:
        """检查文件是否存在"""
        try:
            self._connect()
            
            file_path = self._normalize_path(key)
            attrs = self._sftp_client.stat(file_path)
            return stat.S_ISREG(attrs.st_mode)
        
        except Exception:
            return False
        finally:
            self._disconnect()


# 注册SFTP适配器
if PARAMIKO_AVAILABLE:
    StorageFactory.register_adapter(StorageType.SFTP, SFTPStorageAdapter)
