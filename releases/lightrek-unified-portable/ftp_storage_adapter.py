"""
FTP存储适配器 - 基于ftplib实现
"""

from storage_abstraction import StorageAdapter, FTPStorageConfig, FileMetadata, ListResult, StorageType, StorageFactory
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import ftplib
import ssl
import posixpath
from io import BytesIO
import re


class FTPStorageAdapter(StorageAdapter):
    """FTP存储适配器"""
    
    def __init__(self, config: FTPStorageConfig):
        super().__init__(config)
        self.config: FTPStorageConfig = config
        self._ftp_client = None
    
    def _connect(self) -> bool:
        """建立FTP连接"""
        try:
            if self._ftp_client is not None:
                # 测试连接是否仍然有效
                try:
                    self._ftp_client.voidcmd("NOOP")
                    return True
                except:
                    self._disconnect()
            
            # 建立新连接
            if self.config.use_tls:
                # 使用FTPS (FTP over TLS)
                context = ssl.create_default_context()
                context.check_hostname = False
                context.verify_mode = ssl.CERT_NONE
                self._ftp_client = ftplib.FTP_TLS(context=context)
            else:
                # 使用普通FTP
                self._ftp_client = ftplib.FTP()
            
            # 连接到服务器
            self._ftp_client.connect(self.config.hostname, self.config.port)
            
            # 登录
            self._ftp_client.login(self.config.username, self.config.password)
            
            # 设置传输模式
            if self.config.passive_mode:
                self._ftp_client.set_pasv(True)
            else:
                self._ftp_client.set_pasv(False)
            
            # 如果使用FTPS，保护数据连接
            if self.config.use_tls:
                self._ftp_client.prot_p()
            
            # 切换到根目录
            if self.config.root_path and self.config.root_path != '/':
                self._ftp_client.cwd(self.config.root_path)
            
            return True
        except Exception as e:
            self._disconnect()
            raise Exception(f"FTP连接失败: {str(e)}")
    
    def _disconnect(self):
        """断开FTP连接"""
        if self._ftp_client:
            try:
                self._ftp_client.quit()
            except:
                try:
                    self._ftp_client.close()
                except:
                    pass
            self._ftp_client = None
    
    def _normalize_path(self, path: str) -> str:
        """规范化路径"""
        if not path:
            return "."
        
        # 使用POSIX路径（FTP服务器通常使用Unix风格路径）
        if not path.startswith('/'):
            path = posixpath.join(self.config.root_path, path)
        
        return posixpath.normpath(path)
    
    def _get_relative_path(self, full_path: str) -> str:
        """获取相对于根路径的路径"""
        root = self.config.root_path.rstrip('/')
        if full_path.startswith(root):
            relative = full_path[len(root):].lstrip('/')
            return relative if relative else ""
        return full_path
    
    def test_connection(self) -> Tuple[bool, str]:
        """测试连接"""
        try:
            self._connect()
            
            # 测试访问根目录
            try:
                current_dir = self._ftp_client.pwd()
                return True, f"FTP连接成功，当前目录: {current_dir}"
            except Exception as e:
                return False, f"无法访问根目录: {str(e)}"
        except Exception as e:
            return False, f"FTP连接失败: {str(e)}"
        finally:
            self._disconnect()
    
    def list_files(self, prefix: str = "", max_keys: int = 1000, 
                   continuation_token: Optional[str] = None) -> ListResult:
        """列出文件"""
        try:
            self._connect()
            
            files = []
            start_path = self._normalize_path(prefix) if prefix else "."
            
            # 如果指定了continuation_token，从该位置开始
            skip_until = continuation_token
            found_start = continuation_token is None
            
            def scan_directory(dir_path: str):
                nonlocal files, found_start, skip_until
                
                if len(files) >= max_keys:
                    return
                
                try:
                    # 保存当前目录
                    original_dir = self._ftp_client.pwd()
                    
                    # 切换到目标目录
                    if dir_path != ".":
                        self._ftp_client.cwd(dir_path)
                    
                    # 获取目录列表
                    items = []
                    self._ftp_client.retrlines('LIST', items.append)
                    
                    for item_line in items:
                        if len(files) >= max_keys:
                            break
                        
                        # 解析LIST命令的输出
                        file_info = self._parse_list_line(item_line)
                        if not file_info:
                            continue
                        
                        # 构建完整路径
                        if dir_path == ".":
                            item_path = file_info['name']
                        else:
                            item_path = posixpath.join(dir_path, file_info['name'])
                        
                        relative_path = self._get_relative_path(item_path)
                        
                        # 检查是否匹配前缀
                        if prefix and not relative_path.startswith(prefix):
                            continue
                        
                        # 处理分页
                        if not found_start:
                            if relative_path == skip_until:
                                found_start = True
                            continue
                        
                        if file_info['type'] == 'file':
                            # 文件
                            file_meta = FileMetadata(
                                key=relative_path,
                                size=file_info['size'],
                                last_modified=file_info['modified'],
                                etag=None,  # FTP不支持ETag
                                content_type='binary/octet-stream'
                            )
                            files.append(file_meta)
                        
                        elif file_info['type'] == 'directory':
                            # 递归扫描子目录
                            scan_directory(item_path)
                    
                    # 恢复原目录
                    self._ftp_client.cwd(original_dir)
                
                except ftplib.error_perm:
                    # 跳过无权限的目录
                    pass
                except Exception:
                    # 跳过其他错误的目录
                    pass
            
            # 开始扫描
            scan_directory(start_path)
            
            # 确定是否还有更多文件
            is_truncated = len(files) >= max_keys
            next_token = files[-1].key if is_truncated and files else None
            
            return ListResult(
                files=files,
                is_truncated=is_truncated,
                next_token=next_token
            )
        
        except Exception as e:
            raise Exception(f"列出文件失败: {str(e)}")
        finally:
            self._disconnect()
    
    def _parse_list_line(self, line: str) -> Optional[Dict[str, Any]]:
        """解析LIST命令的输出行"""
        try:
            # Unix风格的LIST输出格式
            # -rw-r--r--   1 <USER>  <GROUP>      1234 Jan 01 12:00 filename
            parts = line.split()
            if len(parts) < 9:
                return None
            
            permissions = parts[0]
            size_str = parts[4]
            
            # 文件名可能包含空格，需要重新组合
            name = ' '.join(parts[8:])
            
            # 跳过 . 和 ..
            if name in ['.', '..']:
                return None
            
            # 判断文件类型
            if permissions.startswith('d'):
                file_type = 'directory'
                size = 0
            elif permissions.startswith('-'):
                file_type = 'file'
                try:
                    size = int(size_str)
                except ValueError:
                    size = 0
            else:
                return None
            
            # 解析修改时间
            try:
                # 简化处理，使用当前时间
                modified = datetime.now()
                
                # 尝试解析时间字段
                month_str = parts[5]
                day_str = parts[6]
                time_or_year = parts[7]
                
                # 这里可以添加更复杂的时间解析逻辑
                # 目前使用当前时间作为默认值
            except:
                modified = datetime.now()
            
            return {
                'name': name,
                'type': file_type,
                'size': size,
                'modified': modified,
                'permissions': permissions
            }
        except Exception:
            return None
    
    def get_file(self, key: str) -> Optional[bytes]:
        """下载文件"""
        try:
            self._connect()
            
            file_path = self._normalize_path(key)
            
            # 使用BytesIO来接收文件内容
            file_obj = BytesIO()
            
            # 下载文件
            self._ftp_client.retrbinary(f'RETR {file_path}', file_obj.write)
            
            file_obj.seek(0)
            return file_obj.read()
        
        except Exception:
            return None
        finally:
            self._disconnect()
    
    def put_file(self, key: str, data: bytes, 
                 content_type: str = 'binary/octet-stream',
                 metadata: Optional[Dict[str, str]] = None) -> bool:
        """上传文件"""
        try:
            self._connect()
            
            file_path = self._normalize_path(key)
            
            # 确保目录存在
            dir_path = posixpath.dirname(file_path)
            if dir_path and dir_path != '.' and dir_path != '/':
                self._ensure_directory_exists(dir_path)
            
            # 使用BytesIO来上传文件内容
            file_obj = BytesIO(data)
            
            # 上传文件
            self._ftp_client.storbinary(f'STOR {file_path}', file_obj)
            
            return True
        
        except Exception:
            return False
        finally:
            self._disconnect()
    
    def _ensure_directory_exists(self, dir_path: str):
        """确保目录存在"""
        try:
            # 尝试切换到目录
            original_dir = self._ftp_client.pwd()
            self._ftp_client.cwd(dir_path)
            self._ftp_client.cwd(original_dir)
        except ftplib.error_perm:
            # 目录不存在，递归创建
            parent_dir = posixpath.dirname(dir_path)
            if parent_dir and parent_dir != '.' and parent_dir != '/' and parent_dir != dir_path:
                self._ensure_directory_exists(parent_dir)
            
            # 创建目录
            self._ftp_client.mkd(dir_path)
    
    def delete_file(self, key: str) -> bool:
        """删除文件"""
        try:
            self._connect()
            
            file_path = self._normalize_path(key)
            self._ftp_client.delete(file_path)
            return True
        
        except Exception:
            return False
        finally:
            self._disconnect()
    
    def get_file_metadata(self, key: str) -> Optional[FileMetadata]:
        """获取文件元数据"""
        try:
            self._connect()
            
            file_path = self._normalize_path(key)
            
            # 获取文件大小
            try:
                size = self._ftp_client.size(file_path)
            except:
                size = 0
            
            # 获取修改时间
            try:
                timestamp = self._ftp_client.sendcmd(f'MDTM {file_path}')
                # MDTM返回格式: 213 YYYYMMDDHHMMSS
                if timestamp.startswith('213 '):
                    time_str = timestamp[4:]
                    last_modified = datetime.strptime(time_str, '%Y%m%d%H%M%S')
                else:
                    last_modified = datetime.now()
            except:
                last_modified = datetime.now()
            
            return FileMetadata(
                key=key,
                size=size or 0,
                last_modified=last_modified,
                etag=None,  # FTP不支持ETag
                content_type='binary/octet-stream'
            )
        
        except Exception:
            return None
        finally:
            self._disconnect()
    
    def file_exists(self, key: str) -> bool:
        """检查文件是否存在"""
        try:
            self._connect()
            
            file_path = self._normalize_path(key)
            
            # 尝试获取文件大小来检查文件是否存在
            try:
                self._ftp_client.size(file_path)
                return True
            except ftplib.error_perm:
                return False
        
        except Exception:
            return False
        finally:
            self._disconnect()


# 注册FTP适配器
StorageFactory.register_adapter(StorageType.FTP, FTPStorageAdapter)
