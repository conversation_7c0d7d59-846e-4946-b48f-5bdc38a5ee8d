{"sources": {"source1": {"name": "阿里云OSS源", "description": "生产环境数据源", "access_key": "LTAI5tR6qB4obJTCWFrDgkDc", "secret_key": "******************************", "endpoint": "https://oss-cn-shanghai.aliyuncs.com", "region": "cn-shanghai", "bucket": "jayce-s3test"}}, "targets": {"target1": {"name": "aliyunCOS目标", "description": "备份存储目标", "access_key": "LTAI5tR6qB4obJTCWFrDgkDc", "secret_key": "******************************", "endpoint": "https://oss-cn-hangzhou.aliyuncs.com", "region": "cn-hangzhou", "bucket": "jayce-s3test-2"}}, "tasks": {"task1": {"task_id": "task1", "name": "每日备份任务", "description": "每天凌晨2点执行的备份任务", "source_id": "source1", "target_id": "target1", "prefix": "", "sync_mode": "incremental", "delete_extra": false, "file_filter": "", "exclude_filter": "", "max_workers": 20, "retry_times": 5, "retry_delay": 3, "verify_integrity": true, "bandwidth_limit": 0, "chunk_threshold": 100, "chunk_size": 10, "schedule_type": "minutely", "schedule_time": "13:00", "schedule_interval": 10, "enabled": true, "status": "completed", "created_at": "2024-06-16T14:30:00", "last_run": "2025-06-18T15:57:44.221802"}}, "global_settings": {"max_workers": 20, "retry_times": 5, "retry_delay": 3, "log_level": "INFO"}, "optimization_settings": {"enabled": true, "parallel_scanning": {"enabled": true, "max_workers": 5}, "cache_management": {"enabled": true, "cache_validity_minutes": 60, "max_cache_age_days": 7, "cache_database": "lightrek_cache.db"}, "streaming_processing": {"enabled": true, "batch_size": 5000}}}