{"version": "2.0", "sources": {"d5011361-1787-4ce6-9907-7c788d2055cd": {"name": "AWS S3测试", "description": "", "access_key": "AKIAIOSFODNN7EXAMPLE", "secret_key": "wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY", "endpoint": "https://s3.amazonaws.com", "region": "us-east-1", "bucket": "test-bucket", "storage_type": "s3", "created_at": "2025-06-26T20:53:07.088389"}, "33c5fc5a-ba10-4c50-b4fa-731110920d56": {"name": "阿里云OSS测试", "description": "", "access_key": "LTAI4G***EXAMPLE", "secret_key": "************************", "endpoint": "https://oss-cn-shanghai.aliyuncs.com", "region": "cn-shanghai", "bucket": "test-bucket-al<PERSON><PERSON>", "storage_type": "s3", "created_at": "2025-06-26T20:53:09.132541"}, "2db9525c-cbc0-451a-a3b1-333f0a8c106c": {"name": "SFTP测试", "description": "", "hostname": "", "port": 22, "username": "testuser", "password": "testpass", "root_path": "/home/<USER>", "storage_type": "sftp", "created_at": "2025-06-26T20:53:11.179371"}, "9a4581e2-1da8-48d7-b0ae-924edf8354d8": {"name": "SMB测试", "description": "", "hostname": "", "share_name": "shared", "username": "testuser", "password": "testpass", "domain": "", "storage_type": "smb", "created_at": "2025-06-26T20:53:13.243069"}, "590417b8-c9d3-4885-bed0-c19a34899a93": {"name": "FTP测试", "description": "", "hostname": "", "port": 21, "username": "testuser", "password": "testpass", "use_tls": false, "storage_type": "ftp", "created_at": "2025-06-26T20:53:15.291114"}, "3e541116-5495-4672-afbb-3f0c7130e1cf": {"name": "端口测试_22", "description": "", "hostname": "", "port": 22, "username": "test", "password": "", "root_path": "/", "storage_type": "sftp", "created_at": "2025-06-26T20:53:27.577266"}, "3266a977-e4d4-4288-8eaf-016d752a8bc4": {"name": "端口测试_443", "description": "", "hostname": "", "port": 443, "username": "test", "password": "", "root_path": "/", "storage_type": "sftp", "created_at": "2025-06-26T20:53:29.620950"}, "22a17706-369b-4742-ba8f-37e7ec86e8f2": {"name": "URL测试", "description": "", "access_key": "test", "secret_key": "test", "endpoint": "http://example.com", "region": "", "bucket": "test", "storage_type": "s3", "created_at": "2025-06-26T20:53:33.725922"}, "91876be9-eac6-4f37-932a-87e684e845d9": {"name": "URL测试", "description": "", "access_key": "test", "secret_key": "test", "endpoint": "https://s3.amazonaws.com", "region": "", "bucket": "test", "storage_type": "s3", "created_at": "2025-06-26T20:53:35.775382"}, "25629856-e498-4dec-92e6-aaef44ef41f6": {"name": "a", "description": "", "access_key": "test", "secret_key": "test", "endpoint": "https://s3.amazonaws.com", "region": "", "bucket": "test", "storage_type": "s3", "created_at": "2025-06-26T20:54:18.817029"}, "eb1002cc-dc94-45b8-bb5e-480e317ec29f": {"name": "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa", "description": "", "access_key": "test", "secret_key": "test", "endpoint": "https://s3.amazonaws.com", "region": "", "bucket": "test", "storage_type": "s3", "created_at": "2025-06-26T20:54:20.846999"}, "ee992689-ba5e-4b20-a7b1-d693c86c863a": {"name": "端口边界测试_1", "description": "", "hostname": "", "port": 1, "username": "test", "password": "", "root_path": "/", "storage_type": "sftp", "created_at": "2025-06-26T20:54:29.025377"}, "431c8653-4651-44e8-897a-70231d0a2ab6": {"name": "端口边界测试_65535", "description": "", "hostname": "", "port": 65535, "username": "test", "password": "", "root_path": "/", "storage_type": "sftp", "created_at": "2025-06-26T20:54:31.093769"}, "3f61faa7-c0cd-4098-af81-290f77a7bb38": {"name": "本地测试源", "description": "", "root_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\lightrek_test_1m_et5f0", "storage_type": "local", "created_at": "2025-06-26T20:54:45.422674"}, "bedae5d0-5315-4d1f-a485-66b74372f7fb": {"name": "有效S3存储", "description": "", "access_key": "test_key", "secret_key": "test_secret", "endpoint": "https://s3.amazonaws.com", "region": "", "bucket": "test-bucket", "storage_type": "s3", "created_at": "2025-06-26T20:56:05.966654"}}, "targets": {"c1bfab04-e3fe-4327-9d1a-cce3e12dbb62": {"name": "", "description": "", "access_key": "", "secret_key": "", "endpoint": "", "region": "", "bucket": "", "storage_type": "s3", "created_at": "2025-06-26T20:53:50.136912"}, "a6cf7266-2760-46e7-a69a-ce0b037754e6": {"name": "本地测试目标", "description": "", "root_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\lightrek_test_1m_et5f0\\target", "storage_type": "local", "created_at": "2025-06-26T20:54:47.470200"}}, "tasks": {"907b99bf-7493-44c7-aa00-7eb5940c9cfe": {"task_id": "907b99bf-7493-44c7-aa00-7eb5940c9cfe", "name": "", "description": "", "source_id": "", "target_id": "", "prefix": "", "sync_mode": "incremental", "delete_extra": false, "file_filter": "", "exclude_filter": "", "max_workers": 20, "retry_times": 5, "retry_delay": 3, "verify_integrity": true, "bandwidth_limit": 0, "chunk_threshold": 100, "chunk_size": 10, "schedule_type": "manual", "schedule_interval": 1, "schedule_time": "00:00", "enabled": true, "created_at": "2025-06-26T20:53:52.180313"}}, "global_settings": {"default_max_workers": 20, "default_retry_times": 5, "default_retry_delay": 3, "default_chunk_size_mb": 10, "default_bandwidth_limit": 0}}