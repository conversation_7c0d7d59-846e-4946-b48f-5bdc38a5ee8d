# LightRek S3同步工具

## 系统信息
- 平台: macos
- 架构: arm64
- 版本: 1.0.0

## 使用说明

### 启动程序

```bash
# 方式1: 使用启动脚本
./start.sh

# 方式2: 直接运行
./lightrek
```

### 功能特性
- ✅ 多云存储支持（阿里云OSS、腾讯云COS、AWS S3等）
- ✅ 可视化Web管理界面
- ✅ 多任务并发同步
- ✅ 增量/全量/镜像同步模式
- ✅ 大文件分片传输（>100MB）
- ✅ 定时任务调度
- ✅ 实时进度监控
- ✅ 详细日志记录
- ✅ 连接测试和存储桶自动发现

### 访问地址
程序启动后，请访问: http://localhost:8001

### 配置文件
- `lightrek_config.json`: 主配置文件
- `lightrek.db`: SQLite数据库文件（自动创建）

### 注意事项
1. 确保网络连接正常
2. 配置正确的访问密钥和端点
3. 防火墙允许8001端口访问
4. 大文件传输需要足够的磁盘空间

### 技术支持
如有问题，请检查日志文件：`lightrek_sync.log`
