#!/usr/bin/env python3
"""
修复JavaScript模板字符串语法问题
将 ${{variable}} 修复为 ${variable}
"""

import re
import shutil
from pathlib import Path

def fix_javascript_template_strings(file_path):
    """修复JavaScript模板字符串语法"""
    
    # 备份原文件
    backup_path = str(file_path) + '.backup'
    shutil.copy2(str(file_path), backup_path)
    print(f"已备份原文件到: {backup_path}")
    
    # 读取文件内容
    with open(str(file_path), 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 统计修复前的问题数量
    original_count = len(re.findall(r'\$\{\{[^}]+\}\}', content))
    print(f"发现 {original_count} 个需要修复的JavaScript模板字符串")
    
    # 修复JavaScript模板字符串
    # 将 ${{variable}} 替换为 ${variable}
    fixed_content = re.sub(r'\$\{\{([^}]+)\}\}', r'${\1}', content)
    
    # 统计修复后的结果
    remaining_count = len(re.findall(r'\$\{\{[^}]+\}\}', fixed_content))
    fixed_count = original_count - remaining_count
    
    # 写入修复后的内容
    with open(str(file_path), 'w', encoding='utf-8') as f:
        f.write(fixed_content)
    
    print(f"修复完成:")
    print(f"  - 修复数量: {fixed_count}")
    print(f"  - 剩余问题: {remaining_count}")
    
    if remaining_count == 0:
        print("✅ 所有JavaScript模板字符串问题已修复!")
    else:
        print("⚠️ 仍有部分问题未修复，请手动检查")
    
    return fixed_count, remaining_count

def main():
    """主函数"""
    file_path = Path("complete_web_interface.py")
    
    if not file_path.exists():
        print(f"❌ 文件不存在: {file_path}")
        return
    
    print("🔧 开始修复JavaScript模板字符串语法问题...")
    print("=" * 50)
    
    try:
        fixed_count, remaining_count = fix_javascript_template_strings(file_path)
        
        print("\n" + "=" * 50)
        print("🎯 修复总结:")
        print(f"  - 文件: {file_path}")
        print(f"  - 修复数量: {fixed_count}")
        print(f"  - 剩余问题: {remaining_count}")
        
        if remaining_count == 0:
            print("\n🎉 修复成功! Web界面应该可以正常工作了")
            print("💡 请重启Web服务器并测试功能")
        else:
            print("\n⚠️ 部分问题未修复，可能需要手动处理")
            
    except Exception as e:
        print(f"❌ 修复过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
