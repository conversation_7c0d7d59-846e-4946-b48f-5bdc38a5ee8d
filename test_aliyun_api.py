#!/usr/bin/env python3
"""
测试阿里云OSS API调用的脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from storage_abstraction import S3StorageConfig, StorageType
from s3_storage_adapter import S3StorageAdapter

def test_different_api_params():
    """测试不同的API参数组合"""
    print("🔍 测试阿里云OSS不同的API参数...")
    
    # 配置信息 - 请替换为实际配置
    config = S3StorageConfig(
        storage_type=StorageType.S3,
        name="阿里云OSS测试",
        endpoint="https://oss-cn-hangzhou.aliyuncs.com",  # 替换为你的区域
        access_key="your-access-key",  # 替换为你的Access Key
        secret_key="your-secret-key",  # 替换为你的Secret Key
        bucket="jayce-s3test",  # 替换为你的bucket名称
        region="cn-hangzhou"  # 替换为你的区域
    )
    
    adapter = S3StorageAdapter(config)
    
    # 测试1: 使用list-type=2 (标准S3 v2 API)
    print("\n📋 测试1: 使用list-type=2")
    try:
        query_params = {
            'list-type': '2',
            'max-keys': '10'
        }
        status, headers, body = adapter._make_request('GET', '', query_params)
        print(f"   状态码: {status}")
        if status == 200:
            print("   ✅ 成功")
        else:
            print(f"   ❌ 失败: {body.decode('utf-8', errors='ignore')[:200]}")
    except Exception as e:
        print(f"   ❌ 异常: {e}")
    
    # 测试2: 不使用list-type (传统API)
    print("\n📋 测试2: 不使用list-type (传统API)")
    try:
        query_params = {
            'max-keys': '10'
        }
        status, headers, body = adapter._make_request('GET', '', query_params)
        print(f"   状态码: {status}")
        if status == 200:
            print("   ✅ 成功")
            # 解析XML看看结构
            import xml.etree.ElementTree as ET
            root = ET.fromstring(body)
            print(f"   根元素: {root.tag}")
            contents = root.findall('.//Contents')
            print(f"   找到 {len(contents)} 个文件")
        else:
            print(f"   ❌ 失败: {body.decode('utf-8', errors='ignore')[:200]}")
    except Exception as e:
        print(f"   ❌ 异常: {e}")
    
    # 测试3: 使用marker分页
    print("\n📋 测试3: 使用marker分页")
    try:
        query_params = {
            'max-keys': '10',
            'marker': ''
        }
        status, headers, body = adapter._make_request('GET', '', query_params)
        print(f"   状态码: {status}")
        if status == 200:
            print("   ✅ 成功")
        else:
            print(f"   ❌ 失败: {body.decode('utf-8', errors='ignore')[:200]}")
    except Exception as e:
        print(f"   ❌ 异常: {e}")
    
    # 测试4: 空参数
    print("\n📋 测试4: 空参数")
    try:
        query_params = {}
        status, headers, body = adapter._make_request('GET', '', query_params)
        print(f"   状态码: {status}")
        if status == 200:
            print("   ✅ 成功")
        else:
            print(f"   ❌ 失败: {body.decode('utf-8', errors='ignore')[:200]}")
    except Exception as e:
        print(f"   ❌ 异常: {e}")

if __name__ == "__main__":
    print("🚀 阿里云OSS API参数测试")
    print("=" * 50)
    print("⚠️ 注意: 请在运行前修改配置中的Access Key、Secret Key和bucket名称")
    print()
    
    test_different_api_params()
    
    print("\n✅ 测试完成!")
    print("\n💡 根据测试结果，我们可以确定阿里云OSS支持的正确API参数")
