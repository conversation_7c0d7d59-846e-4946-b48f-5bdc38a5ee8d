#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试SMB文件下载修复
"""

import logging
import time

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def test_smb_single_file_download():
    """测试单个文件下载"""
    logger = setup_logging()
    
    try:
        # 导入SMB适配器以确保注册
        import smb_storage_adapter
        from storage_abstraction import StorageFactory, SMBStorageConfig, StorageType
        
        logger.info("🧪 测试SMB单个文件下载")
        
        # 创建SMB配置
        config = SMBStorageConfig(
            storage_type=StorageType.SMB,
            hostname="Jay<PERSON>",
            port=445,
            username="smb",
            password="smbsmb",
            domain="WORKGROUP",
            share_name="hlmj",
            root_path=""
        )
        
        adapter = StorageFactory.create_adapter(config)
        
        # 先获取文件列表
        logger.info("📁 获取文件列表...")
        result = adapter.list_files("", max_keys=5)
        
        if len(result.files) == 0:
            logger.error("❌ 没有文件可以测试")
            return False
        
        # 选择一个小文件进行测试
        test_files = [f for f in result.files if f.size < 50000]  # 小于50KB的文件
        if not test_files:
            test_files = result.files[:1]  # 如果没有小文件，选择第一个
        
        test_file = test_files[0]
        logger.info(f"🎯 测试文件: {test_file.key} ({test_file.size} bytes)")
        
        # 测试下载
        start_time = time.time()
        data = adapter.get_file(test_file.key)
        end_time = time.time()
        
        if data:
            logger.info(f"✅ 下载成功: {len(data)} bytes, 耗时: {end_time - start_time:.2f}s")
            
            # 验证数据
            if len(data) > 0:
                logger.info(f"📊 数据前16字节: {data[:16].hex()}")
                return True
            else:
                logger.warning("⚠️ 下载的数据为空")
                return False
        else:
            logger.error("❌ 下载失败，返回None")
            return False
        
    except Exception as e:
        logger.error(f"❌ 测试单个文件下载失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False

def test_smb_multiple_files_download():
    """测试多个文件下载"""
    logger = logging.getLogger(__name__)
    
    try:
        # 导入SMB适配器以确保注册
        import smb_storage_adapter
        from storage_abstraction import StorageFactory, SMBStorageConfig, StorageType
        
        logger.info("🧪 测试SMB多个文件下载")
        
        # 创建SMB配置
        config = SMBStorageConfig(
            storage_type=StorageType.SMB,
            hostname="Jayce",
            port=445,
            username="smb",
            password="smbsmb",
            domain="WORKGROUP",
            share_name="hlmj",
            root_path=""
        )
        
        adapter = StorageFactory.create_adapter(config)
        
        # 获取文件列表
        logger.info("📁 获取文件列表...")
        result = adapter.list_files("", max_keys=3)
        
        if len(result.files) == 0:
            logger.error("❌ 没有文件可以测试")
            return False
        
        # 测试下载多个文件
        success_count = 0
        total_files = len(result.files)
        
        for i, file_meta in enumerate(result.files):
            logger.info(f"📥 下载文件 {i+1}/{total_files}: {file_meta.key}")
            
            try:
                start_time = time.time()
                data = adapter.get_file(file_meta.key)
                end_time = time.time()
                
                if data:
                    logger.info(f"  ✅ 成功: {len(data)} bytes, 耗时: {end_time - start_time:.2f}s")
                    success_count += 1
                else:
                    logger.error(f"  ❌ 失败: 返回None")
                
            except Exception as e:
                logger.error(f"  ❌ 异常: {e}")
            
            # 短暂延迟避免连接冲突
            time.sleep(0.5)
        
        logger.info(f"📊 下载结果: {success_count}/{total_files} 成功")
        return success_count > 0
        
    except Exception as e:
        logger.error(f"❌ 测试多个文件下载失败: {e}")
        return False

def test_smb_connection_stability():
    """测试SMB连接稳定性"""
    logger = logging.getLogger(__name__)
    
    try:
        # 导入SMB适配器以确保注册
        import smb_storage_adapter
        from storage_abstraction import StorageFactory, SMBStorageConfig, StorageType
        
        logger.info("🧪 测试SMB连接稳定性")
        
        # 创建SMB配置
        config = SMBStorageConfig(
            storage_type=StorageType.SMB,
            hostname="Jayce",
            port=445,
            username="smb",
            password="smbsmb",
            domain="WORKGROUP",
            share_name="hlmj",
            root_path=""
        )
        
        adapter = StorageFactory.create_adapter(config)
        
        # 多次连接测试
        success_count = 0
        test_count = 5
        
        for i in range(test_count):
            logger.info(f"🔗 连接测试 {i+1}/{test_count}")
            
            try:
                # 测试连接
                success, message = adapter.test_connection()
                if success:
                    logger.info(f"  ✅ 连接成功")
                    success_count += 1
                else:
                    logger.error(f"  ❌ 连接失败: {message}")
                
            except Exception as e:
                logger.error(f"  ❌ 连接异常: {e}")
            
            # 短暂延迟
            time.sleep(1.0)
        
        logger.info(f"📊 连接稳定性: {success_count}/{test_count} 成功")
        return success_count >= test_count * 0.8  # 80%成功率
        
    except Exception as e:
        logger.error(f"❌ 测试连接稳定性失败: {e}")
        return False

def main():
    """主函数"""
    logger = setup_logging()
    
    logger.info("🧪 SMB文件下载修复测试")
    logger.info("=" * 60)
    
    tests = [
        ("SMB连接稳定性测试", test_smb_connection_stability),
        ("SMB单个文件下载测试", test_smb_single_file_download),
        ("SMB多个文件下载测试", test_smb_multiple_files_download),
    ]
    
    passed_tests = 0
    
    for test_name, test_func in tests:
        logger.info(f"\n📋 执行测试: {test_name}")
        
        try:
            if test_func():
                logger.info(f"✅ {test_name} 通过")
                passed_tests += 1
            else:
                logger.error(f"❌ {test_name} 失败")
        except Exception as e:
            logger.error(f"❌ {test_name} 异常: {e}")
    
    logger.info(f"\n📊 测试结果: {passed_tests}/{len(tests)} 通过")
    
    if passed_tests >= 2:  # 至少2个测试通过
        logger.info("🎉 SMB文件下载功能基本正常！")
        logger.info("")
        logger.info("📋 修复内容:")
        logger.info("  ✅ 改进了文件下载重试机制")
        logger.info("  ✅ 添加了分块读取避免内存问题")
        logger.info("  ✅ 增强了错误处理和日志记录")
        logger.info("  ✅ 改进了连接管理")
        logger.info("")
        logger.info("🚀 现在您可以:")
        logger.info("  1. 重新运行同步任务")
        logger.info("  2. SMB文件下载应该更稳定")
        logger.info("  3. 查看详细的下载错误信息")
        
        return 0
    else:
        logger.error("❌ SMB文件下载功能仍有问题，需要进一步修复")
        return 1

if __name__ == "__main__":
    exit(main())
