#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
独立的SMB文件下载器 - 在独立进程中运行，避免状态污染
"""

import sys
import json
import base64
import os

# 设置标准输出编码为UTF-8
if sys.stdout.encoding != 'utf-8':
    import io
    sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')

def download_smb_file(config_data, file_key):
    """在独立进程中下载SMB文件"""
    try:
        # 清理所有可能的模块缓存
        import sys
        modules_to_clear = [name for name in sys.modules.keys() if 'smb' in name.lower()]
        for module_name in modules_to_clear:
            if module_name in sys.modules:
                del sys.modules[module_name]

        # 添加当前目录到路径
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

        # 重新导入SMB适配器
        import smb_storage_adapter  # 这会注册SMB存储类型
        from storage_abstraction import StorageFactory, SMBStorageConfig, StorageType
        
        # 创建SMB配置
        config = SMBStorageConfig(
            storage_type=StorageType.SMB,
            hostname=config_data['hostname'],
            port=config_data['port'],
            username=config_data['username'],
            password=config_data['password'],
            domain=config_data.get('domain', ''),
            share_name=config_data['share_name'],
            root_path=config_data.get('root_path', '')
        )
        
        # 创建适配器
        adapter = StorageFactory.create_adapter(config)
        
        # 在子进程中直接使用简单下载，避免双重隔离
        data = adapter._simple_download(file_key)
        
        if data:
            # 成功：将数据写入临时文件
            import tempfile
            with tempfile.NamedTemporaryFile(delete=False, suffix='.dat') as f:
                f.write(data)
                output_file = f.name
            print(f"SUCCESS:{len(data)}:{output_file}")
        else:
            # 失败：返回None
            print("FAILED:None")
            
    except Exception as e:
        # 错误：输出错误信息
        print(f"ERROR:{str(e)}")

def main():
    """主函数"""
    if len(sys.argv) != 2:
        print("ERROR:Invalid arguments")
        sys.exit(1)

    try:
        # 从临时文件读取配置
        temp_file = sys.argv[1]

        with open(temp_file, 'r', encoding='utf-8') as f:
            data = json.load(f)

        config_data = data['config']
        file_key = data['file_key']

        # 下载文件
        download_smb_file(config_data, file_key)

    except Exception as e:
        print(f"ERROR:{str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
