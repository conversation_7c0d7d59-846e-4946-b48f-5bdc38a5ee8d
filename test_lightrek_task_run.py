#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 lightrek.py 启动的Web服务器任务运行功能
"""

import requests
import json
import time

def test_lightrek_task_run():
    """测试lightrek.py启动的任务运行功能"""
    base_url = 'http://localhost:8000'  # lightrek.py默认端口
    
    print('🧪 测试 LightRek 任务运行功能')
    print('=' * 50)
    
    # 1. 检查服务器连接
    print('\n1️⃣ 检查服务器连接')
    try:
        # 尝试访问主页
        response = requests.get(f'{base_url}/')
        if response.status_code == 200:
            print('✅ Web服务器连接正常')
        else:
            print(f'❌ Web服务器连接失败: {response.status_code}')
            return False
    except Exception as e:
        print(f'❌ 连接异常: {e}')
        print('请确保已启动 lightrek.py 服务器:')
        print('  python lightrek.py --port 8000')
        return False
    
    # 2. 获取现有任务列表
    print('\n2️⃣ 获取现有任务列表')
    try:
        response = requests.get(f'{base_url}/api/tasks')
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                tasks = data.get('tasks', {})
                print(f'✅ 获取任务列表成功 ({len(tasks)} 个任务)')
                
                if tasks:
                    # 显示任务信息
                    for task_id, task_info in list(tasks.items())[:3]:  # 只显示前3个
                        task_name = task_info.get('name', '未命名')
                        print(f'   📋 任务: {task_name} (ID: {task_id[:8]}...)')
                    return tasks
                else:
                    print('   ℹ️ 当前没有任务，将创建测试任务')
                    return {}
            else:
                print(f'❌ 获取任务列表失败: {data.get("message")}')
                return {}
        else:
            print(f'❌ 任务列表API失败: {response.status_code}')
            return {}
    except Exception as e:
        print(f'❌ 获取任务列表异常: {e}')
        return {}

def test_existing_task_run(tasks):
    """测试运行现有任务"""
    if not tasks:
        return False
    
    base_url = 'http://localhost:8000'
    first_task_id = list(tasks.keys())[0]
    first_task_name = tasks[first_task_id].get('name', '未命名')
    
    print(f'\n3️⃣ 测试运行现有任务: {first_task_name}')
    
    try:
        response = requests.post(f'{base_url}/api/tasks/{first_task_id}/run',
                               headers={'Content-Type': 'application/json'})
        
        print(f'📡 HTTP状态码: {response.status_code}')
        
        if response.status_code == 200:
            data = response.json()
            print(f'📋 API响应: {data}')
            
            if data.get('success'):
                print(f'✅ 任务启动成功: {data.get("message")}')
                return True
            else:
                print(f'❌ 任务启动失败: {data.get("message")}')
                return False
        else:
            print(f'❌ 任务启动API失败: {response.status_code}')
            print(f'📄 响应内容: {response.text[:200]}')
            return False
    except Exception as e:
        print(f'❌ 任务启动异常: {e}')
        return False

def create_and_test_task():
    """创建测试任务并运行"""
    base_url = 'http://localhost:8000'
    
    print('\n4️⃣ 创建测试任务并运行')
    
    # 创建测试数据源
    print('\n   📁 创建测试数据源')
    test_source = {
        'name': '测试本地数据源',
        'type': 'local',
        'path': 'C:\\temp\\test_source'
    }
    
    try:
        response = requests.post(f'{base_url}/api/sources', 
                               json=test_source,
                               headers={'Content-Type': 'application/json'})
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                source_id = data.get('source_id')
                print(f'   ✅ 数据源创建成功: {source_id[:8]}...')
            else:
                print(f'   ❌ 数据源创建失败: {data.get("message")}')
                return False
        else:
            print(f'   ❌ 数据源创建API失败: {response.status_code}')
            return False
    except Exception as e:
        print(f'   ❌ 数据源创建异常: {e}')
        return False
    
    # 创建测试目标存储
    print('\n   🎯 创建测试目标存储')
    test_target = {
        'name': '测试本地目标存储',
        'type': 'local',
        'path': 'C:\\temp\\test_target'
    }
    
    try:
        response = requests.post(f'{base_url}/api/targets', 
                               json=test_target,
                               headers={'Content-Type': 'application/json'})
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                target_id = data.get('target_id')
                print(f'   ✅ 目标存储创建成功: {target_id[:8]}...')
            else:
                print(f'   ❌ 目标存储创建失败: {data.get("message")}')
                return False
        else:
            print(f'   ❌ 目标存储创建API失败: {response.status_code}')
            return False
    except Exception as e:
        print(f'   ❌ 目标存储创建异常: {e}')
        return False
    
    # 创建测试同步任务
    print('\n   📋 创建测试同步任务')
    test_task = {
        'name': '测试同步任务',
        'description': '用于测试任务运行功能的测试任务',
        'source_id': source_id,
        'target_id': target_id,
        'sync_mode': 'incremental'
    }
    
    try:
        response = requests.post(f'{base_url}/api/tasks', 
                               json=test_task,
                               headers={'Content-Type': 'application/json'})
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                task_id = data.get('task_id')
                print(f'   ✅ 同步任务创建成功: {task_id[:8]}...')
            else:
                print(f'   ❌ 同步任务创建失败: {data.get("message")}')
                return False
        else:
            print(f'   ❌ 同步任务创建API失败: {response.status_code}')
            return False
    except Exception as e:
        print(f'   ❌ 同步任务创建异常: {e}')
        return False
    
    # 运行测试任务
    print('\n   🚀 运行测试任务')
    try:
        response = requests.post(f'{base_url}/api/tasks/{task_id}/run',
                               headers={'Content-Type': 'application/json'})
        
        print(f'   📡 HTTP状态码: {response.status_code}')
        
        if response.status_code == 200:
            data = response.json()
            print(f'   📋 API响应: {data}')
            
            if data.get('success'):
                print(f'   ✅ 测试任务启动成功: {data.get("message")}')
                return True
            else:
                print(f'   ❌ 测试任务启动失败: {data.get("message")}')
                return False
        else:
            print(f'   ❌ 测试任务启动API失败: {response.status_code}')
            print(f'   📄 响应内容: {response.text[:200]}')
            return False
    except Exception as e:
        print(f'   ❌ 测试任务启动异常: {e}')
        return False

def main():
    """主函数"""
    print('🧪 LightRek 任务运行功能测试')
    print('=' * 60)
    print('测试目标: lightrek.py 启动的Web服务器')
    print('预期端口: 8000')
    print('=' * 60)
    
    # 获取现有任务
    existing_tasks = test_lightrek_task_run()
    
    success_count = 0
    total_tests = 0
    
    # 测试现有任务运行
    if existing_tasks:
        total_tests += 1
        if test_existing_task_run(existing_tasks):
            success_count += 1
    
    # 测试创建新任务并运行
    total_tests += 1
    if create_and_test_task():
        success_count += 1
    
    # 总结
    print('\n' + '=' * 60)
    print(f'📊 测试结果: {success_count}/{total_tests} 通过')
    
    if success_count == total_tests:
        print('🎉 任务运行功能修复成功!')
        print('✅ static_web_server.py 中的任务运行方法已正确修复')
        print('✅ 现在支持 start_task 和 run_task 两种方法')
        print('✅ 错误处理和日志输出已改进')
    else:
        print('❌ 部分测试失败')
        print('请检查:')
        print('  - lightrek.py 是否正在运行')
        print('  - 端口8000是否可访问')
        print('  - 任务管理器是否正确初始化')
        print('  - 数据源和目标存储配置是否正确')
    
    print('\n💡 如果问题仍然存在，请查看服务器控制台输出获取详细信息')

if __name__ == '__main__':
    main()
