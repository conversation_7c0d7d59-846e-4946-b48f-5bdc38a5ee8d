#!/usr/bin/env python3
"""
测试所有存储类型的连接功能
"""

import requests
import json

def test_storage_connection(storage_type, config_data, description):
    """测试存储连接"""
    print(f"\n🧪 测试{description}连接...")
    print("-" * 40)
    
    try:
        response = requests.post(
            'http://localhost:8001/api/test-connection',
            json=config_data,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        print(f"HTTP状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ {description}连接测试成功")
                print(f"消息: {result.get('message', '')}")
            else:
                print(f"❌ {description}连接测试失败")
                print(f"错误信息: {result.get('message', '未知错误')}")
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")

def test_all_storage_types():
    """测试所有存储类型"""
    print("🔍 测试所有存储类型的连接功能")
    print("=" * 60)
    
    # 1. 测试SFTP（预期失败，但应该有详细错误信息）
    sftp_config = {
        "name": "SFTP测试",
        "description": "测试SFTP连接",
        "storage_type": "sftp",
        "hostname": "invalid.example.com",
        "port": 22,
        "username": "testuser",
        "password": "testpass",
        "root_path": "/"
    }
    test_storage_connection("sftp", sftp_config, "SFTP")
    
    # 2. 测试S3（预期失败，但应该有详细错误信息）
    s3_config = {
        "name": "S3测试",
        "description": "测试S3连接",
        "storage_type": "s3",
        "access_key": "invalid_key",
        "secret_key": "invalid_secret",
        "endpoint": "https://s3.amazonaws.com",
        "region": "us-east-1",
        "bucket": "test-bucket"
    }
    test_storage_connection("s3", s3_config, "S3")
    
    # 3. 测试SMB（预期失败，但应该有详细错误信息）
    smb_config = {
        "name": "SMB测试",
        "description": "测试SMB连接",
        "storage_type": "smb",
        "hostname": "invalid.example.com",
        "port": 445,
        "username": "testuser",
        "password": "testpass",
        "domain": "WORKGROUP",
        "share_name": "shared",
        "root_path": "/"
    }
    test_storage_connection("smb", smb_config, "SMB")
    
    # 4. 测试FTP（预期失败，但应该有详细错误信息）
    ftp_config = {
        "name": "FTP测试",
        "description": "测试FTP连接",
        "storage_type": "ftp",
        "hostname": "invalid.example.com",
        "port": 21,
        "username": "testuser",
        "password": "testpass",
        "use_tls": False,
        "passive_mode": True,
        "root_path": "/"
    }
    test_storage_connection("ftp", ftp_config, "FTP")
    
    # 5. 测试本地存储（应该成功）
    local_config = {
        "name": "本地存储测试",
        "description": "测试本地存储连接",
        "storage_type": "local",
        "root_path": "."  # 当前目录
    }
    test_storage_connection("local", local_config, "本地存储")
    
    print("\n" + "=" * 60)
    print("所有存储类型连接测试完成")

if __name__ == "__main__":
    test_all_storage_types()
