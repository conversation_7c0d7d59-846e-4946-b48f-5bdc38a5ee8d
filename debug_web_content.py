#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试Web界面内容
"""

import requests

def debug_web_content():
    """调试Web界面返回的内容"""
    base_url = 'http://localhost:8003'
    
    print('=== 调试Web界面内容 ===')
    
    try:
        response = requests.get(f'{base_url}/')
        if response.status_code == 200:
            content = response.text
            print(f'页面内容长度: {len(content)} 字符')
            
            # 检查关键元素
            elements_to_check = [
                'id="optimization-config"',
                'id="task-logs"',
                'id="user-manual"',
                'showOptimizationConfig()',
                'showTaskLogs()',
                'showUserManual()',
                'function loadOptimizationConfig()',
                'function loadTaskLogs()',
                'function showPage('
            ]
            
            print('\\n检查关键元素:')
            for element in elements_to_check:
                found = element in content
                print(f'  {element}: {"✅" if found else "❌"}')
            
            # 查找可能的错误
            print('\\n查找可能的问题:')
            
            # 检查是否有JavaScript错误
            if 'SyntaxError' in content:
                print('❌ 发现JavaScript语法错误')
            
            # 检查是否有未闭合的标签
            open_divs = content.count('<div')
            close_divs = content.count('</div>')
            print(f'div标签: 开始{open_divs}, 结束{close_divs}, 匹配: {"✅" if open_divs == close_divs else "❌"}')
            
            # 检查是否有格式化错误
            if '{' in content and '}' in content:
                unescaped_braces = 0
                i = 0
                while i < len(content) - 1:
                    if content[i] == '{' and content[i+1] != '{':
                        unescaped_braces += 1
                    i += 1
                print(f'未转义的大括号: {unescaped_braces}')
            
            # 保存内容到文件进行详细检查
            with open('debug_web_content.html', 'w', encoding='utf-8') as f:
                f.write(content)
            print('\\n页面内容已保存到 debug_web_content.html')
            
            # 检查特定的页面部分
            print('\\n检查页面部分:')
            
            # 查找性能优化页面
            opt_start = content.find('id="optimization-config"')
            if opt_start != -1:
                opt_end = content.find('</div>', opt_start)
                if opt_end != -1:
                    opt_section = content[opt_start:opt_end+6]
                    print(f'性能优化页面长度: {len(opt_section)} 字符')
                else:
                    print('❌ 性能优化页面未正确闭合')
            else:
                print('❌ 未找到性能优化页面')
            
            # 查找任务日志页面
            logs_start = content.find('id="task-logs"')
            if logs_start != -1:
                logs_end = content.find('</div>', logs_start)
                if logs_end != -1:
                    logs_section = content[logs_start:logs_end+6]
                    print(f'任务日志页面长度: {len(logs_section)} 字符')
                else:
                    print('❌ 任务日志页面未正确闭合')
            else:
                print('❌ 未找到任务日志页面')
            
            # 查找用户手册页面
            manual_start = content.find('id="user-manual"')
            if manual_start != -1:
                manual_end = content.find('</div>', manual_start)
                if manual_end != -1:
                    manual_section = content[manual_start:manual_end+6]
                    print(f'用户手册页面长度: {len(manual_section)} 字符')
                else:
                    print('❌ 用户手册页面未正确闭合')
            else:
                print('❌ 未找到用户手册页面')
            
            # 检查JavaScript函数定义
            print('\\n检查JavaScript函数:')
            js_functions = [
                'function showOptimizationConfig()',
                'function showTaskLogs()',
                'function showUserManual()',
                'function loadOptimizationConfig()',
                'function loadTaskLogs()',
                'function showPage('
            ]
            
            for func in js_functions:
                pos = content.find(func)
                if pos != -1:
                    print(f'  {func}: 位置 {pos}')
                else:
                    print(f'  {func}: ❌ 未找到')
            
        else:
            print(f'❌ 页面访问失败: {response.status_code}')
            
    except Exception as e:
        print(f'❌ 调试过程中出错: {e}')

if __name__ == '__main__':
    debug_web_content()
