"""
统一任务管理器 - 支持多种存储类型的同步任务管理
"""

import threading
import time
import uuid
from dataclasses import dataclass
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime
import logging

from storage_abstraction import StorageFactory, StorageAdapter, FileMetadata
from unified_config_manager import UnifiedConfigManager
from database_manager import db_manager
from efficient_sync_engine import EfficientSyncEngine
import concurrent.futures
import os

# 导入所有存储适配器以确保它们被注册
try:
    import s3_storage_adapter
    print("✅ S3适配器已加载")
except ImportError as e:
    print(f"⚠️ S3适配器加载失败: {e}")

try:
    import sftp_storage_adapter
    print("✅ SFTP适配器已加载")
except ImportError as e:
    print(f"⚠️ SFTP适配器加载失败: {e}")

try:
    import smb_storage_adapter
    print("✅ SMB适配器已加载")
except ImportError as e:
    print(f"⚠️ SMB适配器加载失败: {e}")

try:
    import ftp_storage_adapter
    print("✅ FTP适配器已加载")
except ImportError as e:
    print(f"⚠️ FTP适配器加载失败: {e}")

try:
    import local_storage_adapter
    print("✅ 本地存储适配器已加载")
except ImportError as e:
    print(f"⚠️ 本地存储适配器加载失败: {e}")


@dataclass
class UnifiedSyncTask:
    """统一同步任务配置"""
    task_id: str
    name: str
    description: str
    source_id: str
    target_id: str
    prefix: str = ""
    max_workers: int = 20
    retry_times: int = 5
    retry_delay: int = 3
    verify_integrity: bool = True
    incremental_sync: bool = True
    sync_mode: str = "incremental"  # incremental, full, mirror
    delete_extra: bool = False
    file_filter: str = ""
    exclude_filter: str = ""
    bandwidth_limit: int = 0  # MB/s
    chunk_threshold: int = 100  # MB
    chunk_size: int = 10  # MB
    schedule_type: str = "manual"
    schedule_interval: int = 1
    schedule_time: str = "00:00"
    enabled: bool = True
    created_at: str = ""
    updated_at: str = ""
    last_run: str = ""
    last_status: str = "未运行"


class UnifiedTaskManager:
    """统一任务管理器"""
    
    def __init__(self, config_manager: UnifiedConfigManager, db_manager=None):
        self.config_manager = config_manager
        self.db_manager = db_manager
        self.running_tasks: Dict[str, threading.Thread] = {}
        self.task_status: Dict[str, Dict[str, Any]] = {}
        self.logger = logging.getLogger(__name__)
        
        # 设置日志
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)
    
    def create_task(self, name: str, description: str, source_id: str, target_id: str, **kwargs) -> str:
        """创建同步任务"""
        task_id = str(uuid.uuid4())
        
        task_config = {
            'task_id': task_id,
            'name': name,
            'description': description,
            'source_id': source_id,
            'target_id': target_id,
            **kwargs
        }
        
        if self.config_manager.add_task(task_id, task_config):
            return task_id
        else:
            raise Exception("创建任务失败")
    
    def get_task(self, task_id: str) -> Optional[UnifiedSyncTask]:
        """获取任务配置"""
        task_config = self.config_manager.get_task(task_id)
        if task_config:
            # 确保 task_config 包含 task_id
            if 'task_id' not in task_config:
                task_config['task_id'] = task_id

            try:
                return UnifiedSyncTask(**task_config)
            except TypeError as e:
                self.logger.error(f"创建任务对象失败: {e}")
                self.logger.error(f"任务配置: {task_config}")
                # 尝试使用默认值创建最小配置
                minimal_config = {
                    'task_id': task_id,
                    'name': task_config.get('name', '未命名任务'),
                    'description': task_config.get('description', ''),
                    'source_id': task_config.get('source_id', ''),
                    'target_id': task_config.get('target_id', '')
                }
                # 添加其他存在的配置项
                for key, value in task_config.items():
                    if key not in minimal_config:
                        minimal_config[key] = value

                return UnifiedSyncTask(**minimal_config)
        return None
    
    def get_all_tasks(self) -> Dict[str, UnifiedSyncTask]:
        """获取所有任务"""
        tasks = {}
        for task_id, task_config in self.config_manager.get_all_tasks().items():
            try:
                tasks[task_id] = UnifiedSyncTask(**task_config)
            except Exception as e:
                self.logger.error(f"解析任务配置失败 {task_id}: {e}")
        return tasks
    
    def update_task(self, task_id: str, **kwargs) -> bool:
        """更新任务配置"""
        return self.config_manager.update_task(task_id, kwargs)
    
    def delete_task(self, task_id: str) -> bool:
        """删除任务"""
        # 如果任务正在运行，先停止
        if task_id in self.running_tasks:
            self.stop_task(task_id)
        
        return self.config_manager.remove_task(task_id)
    
    def start_task(self, task_id: str) -> bool:
        """启动同步任务"""
        if task_id in self.running_tasks:
            self.logger.warning(f"任务 {task_id} 已在运行中")
            return False
        
        task = self.get_task(task_id)
        if not task:
            self.logger.error(f"任务 {task_id} 不存在")
            return False
        
        # 验证源和目标配置
        source_config = self.config_manager.get_source(task.source_id)
        target_config = self.config_manager.get_target(task.target_id)
        
        if not source_config or not target_config:
            self.logger.error(f"任务 {task.name} 的源或目标配置不存在")
            return False
        
        # 创建并启动同步线程
        sync_thread = threading.Thread(
            target=self._run_sync_task,
            args=(task, source_config, target_config),
            daemon=True
        )
        
        self.running_tasks[task_id] = sync_thread
        self.task_status[task_id] = {
            'status': 'starting',
            'start_time': datetime.now().isoformat(),
            'progress': 0,
            'message': '正在启动...'
        }
        
        sync_thread.start()
        return True
    
    def stop_task(self, task_id: str) -> bool:
        """停止同步任务"""
        if task_id not in self.running_tasks:
            return False
        
        # 设置停止标志
        if task_id in self.task_status:
            self.task_status[task_id]['status'] = 'stopping'
            self.task_status[task_id]['message'] = '正在停止...'
        
        # 等待线程结束（最多等待10秒）
        thread = self.running_tasks[task_id]
        thread.join(timeout=10)
        
        # 清理
        if task_id in self.running_tasks:
            del self.running_tasks[task_id]
        
        if task_id in self.task_status:
            self.task_status[task_id]['status'] = 'stopped'
            self.task_status[task_id]['message'] = '已停止'
        
        return True
    
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务状态"""
        return self.task_status.get(task_id)
    
    def get_all_task_status(self) -> Dict[str, Dict[str, Any]]:
        """获取所有任务状态"""
        return self.task_status.copy()
    
    def _run_sync_task(self, task: UnifiedSyncTask, source_config, target_config):
        """运行同步任务"""
        task_id = task.task_id
        execution_id = str(uuid.uuid4())
        
        try:
            self.logger.info(f"开始执行任务: {task.name}")

            # 创建任务执行记录
            db_manager.start_task_execution(
                execution_id=execution_id,
                task_id=task_id,
                task_name=task.name,
                source_id=task.source_id,
                target_id=task.target_id
            )

            # 添加开始日志
            db_manager.add_task_log(execution_id, 'INFO', f'开始执行任务: {task.name}')

            # 更新任务状态
            self.task_status[task_id].update({
                'status': 'running',
                'message': '正在连接存储...',
                'execution_id': execution_id,
                'progress': 0,
                'start_time': datetime.now().isoformat(),
                'files_processed': 0,
                'files_total': 0,
                'bytes_transferred': 0
            })
            
            # 创建存储适配器
            source_adapter = StorageFactory.create_adapter(source_config)
            target_adapter = StorageFactory.create_adapter(target_config)
            
            # 测试连接
            source_ok, source_msg = source_adapter.test_connection()
            if not source_ok:
                raise Exception(f"源存储连接失败: {source_msg}")
            
            target_ok, target_msg = target_adapter.test_connection()
            if not target_ok:
                raise Exception(f"目标存储连接失败: {target_msg}")
            
            self.logger.info(f"存储连接成功 - 源: {source_msg}, 目标: {target_msg}")
            
            # 开始同步
            self._perform_sync(task, source_adapter, target_adapter, execution_id)
            
            # 任务完成
            self.task_status[task_id].update({
                'status': 'completed',
                'message': '同步完成',
                'end_time': datetime.now().isoformat(),
                'progress': 100
            })

            # 更新数据库中的任务执行状态
            db_manager.complete_task_execution(
                execution_id=execution_id,
                status='completed',
                files_processed=self.task_status[task_id].get('files_processed', 0),
                files_failed=self.task_status[task_id].get('files_failed', 0),
                bytes_transferred=self.task_status[task_id].get('bytes_transferred', 0)
            )

            # 更新任务的最后运行时间
            self.config_manager.update_task(task_id, {
                'last_run': datetime.now().isoformat(),
                'last_status': '成功'
            })

            self.logger.info(f"任务 {task.name} 执行完成")
            db_manager.add_task_log(execution_id, 'INFO', f'任务执行完成: {task.name}')
        
        except Exception as e:
            self.logger.error(f"任务 {task.name} 执行失败: {str(e)}")
            
            # 更新任务状态
            if task_id in self.task_status:
                self.task_status[task_id].update({
                    'status': 'failed',
                    'message': f'执行失败: {str(e)}',
                    'end_time': datetime.now().isoformat()
                })

            # 更新数据库中的任务执行状态
            db_manager.complete_task_execution(
                execution_id=execution_id,
                status='failed',
                files_processed=self.task_status[task_id].get('files_processed', 0) if task_id in self.task_status else 0,
                files_failed=self.task_status[task_id].get('files_failed', 0) if task_id in self.task_status else 0,
                bytes_transferred=self.task_status[task_id].get('bytes_transferred', 0) if task_id in self.task_status else 0,
                error_message=str(e)
            )

            # 更新任务的最后运行状态
            self.config_manager.update_task(task_id, {
                'last_run': datetime.now().isoformat(),
                'last_status': f'失败: {str(e)}'
            })

            db_manager.add_task_log(execution_id, 'ERROR', f'任务执行失败: {str(e)}')
        
        finally:
            # 清理运行中的任务记录 - 延迟清理，确保状态更新完成
            def cleanup_task():
                import time
                time.sleep(2)  # 等待2秒确保状态更新完成
                if task_id in self.running_tasks:
                    del self.running_tasks[task_id]
                    self.logger.info(f"任务 {task_id} 已从运行列表中清理")

            # 在后台线程中执行清理
            import threading
            cleanup_thread = threading.Thread(target=cleanup_task, daemon=True)
            cleanup_thread.start()
    
    def _perform_sync(self, task: UnifiedSyncTask, source_adapter: StorageAdapter, 
                     target_adapter: StorageAdapter, execution_id: str):
        """执行同步操作"""
        task_id = task.task_id
        
        # 更新状态
        self.task_status[task_id]['message'] = '正在扫描文件...'
        
        # 扫描源文件
        self.logger.info(f"开始扫描源文件，前缀: {task.prefix}")
        source_files = self._scan_all_files(source_adapter, task.prefix, task_id)

        self.logger.info(f"源文件扫描完成，共 {len(source_files)} 个文件")

        # 根据同步模式处理
        self.task_status[task_id]['message'] = '正在扫描目标文件...'
        target_files = self._scan_all_files(target_adapter, task.prefix, task_id)

        if task.sync_mode == "full":
            # 全量同步：重新上传所有文件
            files_to_sync = source_files
        elif task.sync_mode == "mirror":
            # 镜像同步：同步变化的文件，并删除目标中多余的文件
            self.task_status[task_id]['message'] = '正在比较文件差异...'
            files_to_sync = self._compare_files(source_files, target_files, task.verify_integrity)

            # 处理镜像同步中的删除操作
            if task.delete_extra:
                self._handle_mirror_mode_deletions(task, source_files, target_files, target_adapter, execution_id)
        else:
            # 增量同步：只同步有变化的文件
            self.task_status[task_id]['message'] = '正在比较文件差异...'
            files_to_sync = self._compare_files(source_files, target_files, task.verify_integrity)

            # 增量同步也支持删除目标中多余的文件
            if task.delete_extra:
                self.logger.info("增量同步模式下启用删除多余文件功能")
                self._handle_incremental_mode_deletions(task, source_files, target_files, target_adapter, execution_id)
        
        self.logger.info(f"需要同步 {len(files_to_sync)} 个文件")
        
        if not files_to_sync:
            self.logger.info("没有需要同步的文件")
            return
        
        # 检查是否应该使用压缩传输
        from compressed_transfer import compressed_transfer_engine
        if compressed_transfer_engine.should_use_compression(source_adapter, len(files_to_sync)):
            self.logger.info(f"文件数量较多({len(files_to_sync)})，建议使用压缩传输模式")
            db_manager.add_task_log(execution_id, 'INFO', f'检测到{len(files_to_sync)}个文件，建议使用压缩传输模式以提高效率')

        # 检查是否可以使用服务端复制优化
        if self._can_use_server_side_copy(source_adapter, target_adapter):
            self.logger.info("检测到同一存储服务，启用服务端复制优化")
            db_manager.add_task_log(execution_id, 'INFO', '启用阿里云OSS/AWS S3服务端复制优化，传输效率大幅提升')
            self._sync_files_with_server_copy(task, source_adapter, target_adapter, files_to_sync, execution_id)
        else:
            # 执行高效文件同步
            self._sync_files_efficient(task, source_adapter, target_adapter, files_to_sync, execution_id)
    
    def _scan_all_files(self, adapter: StorageAdapter, prefix: str = "", task_id: str = None) -> List[FileMetadata]:
        """扫描所有文件，支持实时进度显示和并行处理"""
        all_files = []
        continuation_token = None
        batch_count = 0

        # 获取性能优化配置
        optimization_config = self.config_manager.get_optimization_config()
        enable_parallel_scan = optimization_config.get('enable_parallel_scan', True)
        max_workers = min(optimization_config.get('max_workers', 20), 10)  # 扫描时限制并发数

        if enable_parallel_scan and hasattr(adapter, 'supports_parallel_scan'):
            # 使用并行扫描（如果适配器支持）
            return self._scan_files_parallel(adapter, prefix, task_id, max_workers)
        else:
            # 使用串行扫描，但优化批次大小 - 移除限制，扫描所有文件
            batch_size = 10000  # 大批次大小，提高效率

            while True:
                try:
                    result = adapter.list_files(prefix=prefix, max_keys=batch_size, continuation_token=continuation_token)
                    all_files.extend(result.files)
                    batch_count += 1

                    # 更新扫描进度
                    if task_id and task_id in self.task_status:
                        files_count = len(all_files)
                        self.task_status[task_id]['message'] = f'正在扫描文件... 已发现 {files_count} 个文件 (批次 {batch_count})'
                        self.task_status[task_id]['files_scanned'] = files_count
                        self.logger.info(f"文件扫描进度: 已扫描 {files_count} 个文件")

                    if not result.is_truncated:
                        break

                    continuation_token = result.next_token
                except Exception as e:
                    self.logger.error(f"扫描文件失败: {e}")
                    break

            # 扫描完成
            if task_id and task_id in self.task_status:
                final_count = len(all_files)
                self.task_status[task_id]['message'] = f'文件扫描完成，共发现 {final_count} 个文件'
                self.logger.info(f"文件扫描完成: 总共 {final_count} 个文件")

            return all_files

    def _scan_files_parallel(self, adapter: StorageAdapter, prefix: str, task_id: str, max_workers: int) -> List[FileMetadata]:
        """并行扫描文件（适用于本地存储等支持并行的适配器）"""
        # 对于本地存储，并行扫描可能不如串行扫描效果好
        # 回退到优化的串行扫描
        self.logger.info("使用优化的串行扫描模式")
        return self._scan_all_files_optimized(adapter, prefix, task_id)

    def _scan_all_files_optimized(self, adapter: StorageAdapter, prefix: str = "", task_id: str = None) -> List[FileMetadata]:
        """优化的文件扫描，确保扫描所有文件"""
        all_files = []
        continuation_token = None
        batch_count = 0

        # 使用更大的批次大小来提高效率，移除总数限制
        batch_size = 10000

        while True:
            try:
                result = adapter.list_files(prefix=prefix, max_keys=batch_size, continuation_token=continuation_token)
                batch_files = result.files
                all_files.extend(batch_files)
                batch_count += 1

                # 更新扫描进度
                if task_id and task_id in self.task_status:
                    files_count = len(all_files)
                    self.task_status[task_id]['message'] = f'正在扫描文件... 已发现 {files_count} 个文件 (批次 {batch_count})'
                    self.task_status[task_id]['files_scanned'] = files_count

                    # 每1000个文件记录一次日志
                    if files_count % 1000 == 0:
                        self.logger.info(f"文件扫描进度: 已扫描 {files_count} 个文件")

                # 检查是否还有更多文件
                if not result.is_truncated or not result.next_token:
                    break

                continuation_token = result.next_token

                # 如果这批文件数量少于批次大小，说明可能已经到末尾
                if len(batch_files) < batch_size:
                    self.logger.info(f"批次 {batch_count} 返回 {len(batch_files)} 个文件，可能接近扫描结束")

            except Exception as e:
                self.logger.error(f"扫描文件失败: {e}")
                break

        # 扫描完成
        if task_id and task_id in self.task_status:
            final_count = len(all_files)
            self.task_status[task_id]['message'] = f'文件扫描完成，共发现 {final_count} 个文件'
            self.logger.info(f"文件扫描完成: 总共 {final_count} 个文件，共 {batch_count} 个批次")

        return all_files
    
    def _compare_files(self, source_files: List[FileMetadata], target_files: List[FileMetadata], 
                      verify_integrity: bool = True) -> List[FileMetadata]:
        """比较文件差异"""
        # 创建目标文件映射
        target_map = {f.key: f for f in target_files}
        
        files_to_sync = []
        
        for source_file in source_files:
            target_file = target_map.get(source_file.key)

            if not target_file:
                # 目标文件不存在
                files_to_sync.append(source_file)
            else:
                # 安全地比较文件大小
                try:
                    source_size = int(source_file.size) if source_file.size else 0
                    target_size = int(target_file.size) if target_file.size else 0

                    if source_size != target_size:
                        # 文件大小不同
                        files_to_sync.append(source_file)
                        continue
                except (ValueError, TypeError):
                    # 如果大小无法比较，则认为需要同步
                    files_to_sync.append(source_file)
                    continue

                if verify_integrity and source_file.etag and target_file.etag:
                    # 验证文件完整性（如果有ETag）
                    if source_file.etag != target_file.etag:
                        files_to_sync.append(source_file)
                        continue

                # 比较修改时间
                try:
                    if source_file.last_modified > target_file.last_modified:
                        # 源文件更新
                        files_to_sync.append(source_file)
                except (TypeError, AttributeError):
                    # 如果时间无法比较，跳过这个文件
                    pass
        
        return files_to_sync

    def _handle_mirror_mode_deletions(self, task: UnifiedSyncTask, source_files: List[FileMetadata],
                                     target_files: List[FileMetadata], target_adapter: StorageAdapter, execution_id: str):
        """处理镜像模式中的删除操作"""
        task_id = task.task_id

        # 创建源文件键的集合
        source_keys = {file_meta.key for file_meta in source_files}

        # 找出目标中多余的文件
        files_to_delete = []
        for target_file in target_files:
            if target_file.key not in source_keys:
                files_to_delete.append(target_file.key)

        if not files_to_delete:
            self.logger.info("镜像模式：没有需要删除的多余文件")
            db_manager.add_task_log(execution_id, 'INFO', '镜像模式：没有需要删除的多余文件')
            return

        self.logger.info(f"镜像模式：需要删除 {len(files_to_delete)} 个多余文件")
        db_manager.add_task_log(execution_id, 'INFO', f'镜像模式：需要删除 {len(files_to_delete)} 个多余文件')

        # 更新任务状态
        self.task_status[task_id]['message'] = f'正在删除 {len(files_to_delete)} 个多余文件...'

        deleted_count = 0
        failed_count = 0

        for i, file_key in enumerate(files_to_delete):
            # 检查是否需要停止
            if task_id in self.task_status and self.task_status[task_id]['status'] == 'stopping':
                break

            try:
                # 更新进度
                progress = int((i / len(files_to_delete)) * 100)
                self.task_status[task_id].update({
                    'message': f'正在删除多余文件: {file_key} ({i+1}/{len(files_to_delete)})',
                    'delete_progress': progress
                })

                # 删除文件
                if target_adapter.delete_file(file_key):
                    deleted_count += 1
                    self.logger.info(f"已删除多余文件: {file_key}")
                    db_manager.add_task_log(execution_id, 'INFO', f'已删除多余文件: {file_key}')
                else:
                    failed_count += 1
                    self.logger.warning(f"删除文件失败: {file_key}")
                    db_manager.add_task_log(execution_id, 'WARN', f'删除文件失败: {file_key}')

            except Exception as e:
                failed_count += 1
                self.logger.error(f"删除文件异常 {file_key}: {str(e)}")
                db_manager.add_task_log(execution_id, 'ERROR', f'删除文件异常 {file_key}: {str(e)}')

        # 记录删除结果
        result_msg = f"镜像模式删除完成: 成功删除 {deleted_count} 个文件，失败 {failed_count} 个"
        self.logger.info(result_msg)
        db_manager.add_task_log(execution_id, 'INFO', result_msg)

    def _handle_incremental_mode_deletions(self, task: UnifiedSyncTask, source_files: List[FileMetadata],
                                         target_files: List[FileMetadata], target_adapter: StorageAdapter, execution_id: str):
        """处理增量模式中的删除操作"""
        task_id = task.task_id

        # 创建源文件键的集合
        source_keys = {file_meta.key for file_meta in source_files}

        # 找出目标中多余的文件
        files_to_delete = []
        for target_file in target_files:
            if target_file.key not in source_keys:
                files_to_delete.append(target_file.key)

        if not files_to_delete:
            self.logger.info("增量模式：没有需要删除的多余文件")
            db_manager.add_task_log(execution_id, 'INFO', '增量模式：没有需要删除的多余文件')
            return

        self.logger.info(f"增量模式：需要删除 {len(files_to_delete)} 个多余文件")
        db_manager.add_task_log(execution_id, 'INFO', f'增量模式：需要删除 {len(files_to_delete)} 个多余文件')

        # 更新任务状态
        self.task_status[task_id]['message'] = f'正在删除 {len(files_to_delete)} 个多余文件...'

        deleted_count = 0
        failed_count = 0

        for i, file_key in enumerate(files_to_delete):
            # 检查是否需要停止
            if task_id in self.task_status and self.task_status[task_id]['status'] == 'stopping':
                break

            try:
                # 更新进度
                progress = int((i / len(files_to_delete)) * 100)
                self.task_status[task_id].update({
                    'message': f'正在删除多余文件: {file_key} ({i+1}/{len(files_to_delete)})',
                    'delete_progress': progress
                })

                # 删除文件
                if target_adapter.delete_file(file_key):
                    deleted_count += 1
                    self.logger.info(f"已删除多余文件: {file_key}")
                    db_manager.add_task_log(execution_id, 'INFO', f'已删除多余文件: {file_key}')
                else:
                    failed_count += 1
                    self.logger.warning(f"删除文件失败: {file_key}")
                    db_manager.add_task_log(execution_id, 'WARN', f'删除文件失败: {file_key}')

            except Exception as e:
                failed_count += 1
                self.logger.error(f"删除文件异常 {file_key}: {str(e)}")
                db_manager.add_task_log(execution_id, 'ERROR', f'删除文件异常 {file_key}: {str(e)}')

        # 记录删除结果
        result_msg = f"增量模式删除完成: 成功删除 {deleted_count} 个文件，失败 {failed_count} 个"
        self.logger.info(result_msg)
        db_manager.add_task_log(execution_id, 'INFO', result_msg)

        # 更新任务状态中的删除统计
        if task_id in self.task_status:
            self.task_status[task_id]['files_deleted'] = deleted_count
            self.task_status[task_id]['delete_failed'] = failed_count

    def _sync_files_efficient(self, task: UnifiedSyncTask, source_adapter: StorageAdapter,
                             target_adapter: StorageAdapter, files_to_sync: List[FileMetadata], execution_id: str):
        """使用高效同步引擎同步文件"""
        task_id = task.task_id

        if not files_to_sync:
            self.logger.info("没有需要同步的文件")
            db_manager.add_task_log(execution_id, 'INFO', '没有需要同步的文件')
            return

        self.logger.info(f"开始高效同步 {len(files_to_sync)} 个文件")
        db_manager.add_task_log(execution_id, 'INFO', f'开始高效同步 {len(files_to_sync)} 个文件')

        # 创建高效同步引擎
        sync_engine = EfficientSyncEngine(max_workers=8, chunk_size=8*1024*1024)

        def progress_callback(progress):
            """进度回调函数"""
            if task_id in self.task_status:
                completion_rate = int((progress.completed_files / progress.total_files) * 100) if progress.total_files > 0 else 0

                self.task_status[task_id].update({
                    'progress': completion_rate,
                    'message': f'高效同步中: {progress.current_file} ({progress.completed_files}/{progress.total_files})',
                    'files_processed': progress.completed_files,
                    'files_total': progress.total_files,
                    'bytes_transferred': progress.transferred_bytes,
                    'sync_speed_mbps': progress.speed_mbps
                })

                # 更新数据库执行记录
                db_manager.update_task_execution(
                    execution_id,
                    files_total=progress.total_files,
                    files_processed=progress.completed_files,
                    files_failed=progress.failed_files,
                    bytes_total=progress.total_bytes,
                    bytes_transferred=progress.transferred_bytes
                )

        try:
            # 执行高效同步
            final_progress = sync_engine.sync_files(
                source_adapter, target_adapter, files_to_sync,
                task_id, execution_id, progress_callback
            )

            # 记录最终结果
            success_rate = (final_progress.completed_files / final_progress.total_files) * 100 if final_progress.total_files > 0 else 0

            self.logger.info(f"高效同步完成: {final_progress.completed_files}/{final_progress.total_files} 文件成功, "
                           f"失败: {final_progress.failed_files}, 速度: {final_progress.speed_mbps:.2f} MB/s")

            db_manager.add_task_log(execution_id, 'INFO',
                                  f'高效同步完成: 成功率 {success_rate:.1f}%, 速度 {final_progress.speed_mbps:.2f} MB/s')

        except Exception as e:
            self.logger.error(f"高效同步过程中出错: {e}")
            db_manager.add_task_log(execution_id, 'ERROR', f'高效同步失败: {e}')
            raise

    def _sync_files(self, task: UnifiedSyncTask, source_adapter: StorageAdapter,
                   target_adapter: StorageAdapter, files_to_sync: List[FileMetadata], execution_id: str):
        """同步文件"""
        task_id = task.task_id
        total_files = len(files_to_sync)
        completed_files = 0
        
        for i, file_meta in enumerate(files_to_sync):
            # 检查是否需要停止
            if task_id in self.task_status and self.task_status[task_id]['status'] == 'stopping':
                break
            
            try:
                # 更新进度
                progress = int((i / total_files) * 100)
                self.task_status[task_id].update({
                    'progress': progress,
                    'message': f'正在同步: {file_meta.key} ({i+1}/{total_files})'
                })
                
                # 下载文件 - 对SMB存储使用子进程隔离下载
                self.logger.info(f"检查存储类型: {type(source_adapter.config)} - {getattr(source_adapter.config, 'storage_type', 'None')}")
                if hasattr(source_adapter.config, 'storage_type') and source_adapter.config.storage_type.name == 'SMB':
                    # 为SMB存储使用子进程隔离下载避免状态污染
                    self.logger.info(f"使用SMB子进程下载: {file_meta.key}")
                    file_data = self._download_smb_file_with_subprocess(source_adapter.config, file_meta.key)
                else:
                    # 其他存储类型使用原有逻辑
                    self.logger.info(f"使用标准下载: {file_meta.key}")
                    file_data = source_adapter.get_file(file_meta.key)

                if file_data is None:
                    self.logger.warning(f"无法下载文件: {file_meta.key}")
                    continue

                # 确保文件大小是整数类型
                try:
                    file_size = int(file_meta.size) if file_meta.size else 0
                except (ValueError, TypeError):
                    file_size = len(file_data) if file_data else 0

                # 上传文件
                if file_size > task.chunk_threshold * 1024 * 1024:
                    # 大文件分片上传
                    success = target_adapter.put_file_chunked(
                        file_meta.key, file_data, task.chunk_size,
                        file_meta.content_type or 'binary/octet-stream'
                    )
                else:
                    # 普通上传
                    success = target_adapter.put_file(
                        file_meta.key, file_data,
                        file_meta.content_type or 'binary/octet-stream'
                    )

                if success:
                    # 验证上传是否真正成功
                    if task.verify_integrity:
                        try:
                            # 检查文件是否存在
                            if target_adapter.file_exists(file_meta.key):
                                completed_files += 1
                                self.logger.debug(f"文件同步成功并验证: {file_meta.key}")
                                db_manager.add_task_log(execution_id, 'DEBUG', f'文件同步成功: {file_meta.key}')
                            else:
                                self.logger.warning(f"文件上传后验证失败，文件不存在: {file_meta.key}")
                                db_manager.add_task_log(execution_id, 'WARN', f'文件上传后验证失败: {file_meta.key}')
                        except Exception as e:
                            # 验证失败，但上传可能成功了，记录警告而不是错误
                            completed_files += 1
                            self.logger.warning(f"文件上传成功但验证异常: {file_meta.key}, 错误: {e}")
                            db_manager.add_task_log(execution_id, 'WARN', f'文件上传成功但验证异常: {file_meta.key}')
                    else:
                        completed_files += 1
                        self.logger.debug(f"文件同步成功: {file_meta.key}")
                        db_manager.add_task_log(execution_id, 'DEBUG', f'文件同步成功: {file_meta.key}')
                else:
                    self.logger.warning(f"文件同步失败: {file_meta.key}")
                    db_manager.add_task_log(execution_id, 'ERROR', f'文件同步失败: {file_meta.key}')

                # 带宽限制
                if task.bandwidth_limit > 0 and file_size > 0:
                    time.sleep(file_size / (task.bandwidth_limit * 1024 * 1024))
            
            except Exception as e:
                self.logger.error(f"同步文件 {file_meta.key} 时出错: {e}")
        
        self.logger.info(f"文件同步完成: {completed_files}/{total_files}")

    def _can_use_server_side_copy(self, source_adapter: StorageAdapter, target_adapter: StorageAdapter) -> bool:
        """检查是否可以使用服务端复制"""
        # 检查是否都是S3存储适配器
        if not (hasattr(source_adapter, 'copy_object') and hasattr(target_adapter, 'copy_object')):
            return False

        # 检查是否为同一存储服务
        if hasattr(source_adapter, '_is_same_storage_service'):
            return source_adapter._is_same_storage_service(target_adapter)

        return False

    def _sync_files_with_server_copy(self, task: UnifiedSyncTask, source_adapter: StorageAdapter,
                                   target_adapter: StorageAdapter, files_to_sync: List[FileMetadata], execution_id: str):
        """使用服务端复制进行文件同步"""
        task_id = task.task_id
        total_files = len(files_to_sync)
        success_count = 0
        failed_count = 0

        self.logger.info(f"开始服务端复制同步 {total_files} 个文件")
        db_manager.add_task_log(execution_id, 'INFO', f'开始服务端复制同步 {total_files} 个文件')

        start_time = time.time()

        for i, file_meta in enumerate(files_to_sync):
            try:
                # 使用服务端复制
                success = target_adapter.copy_object(file_meta.key, file_meta.key, source_adapter)

                if success:
                    success_count += 1
                    self.logger.debug(f"服务端复制成功: {file_meta.key}")
                else:
                    # 服务端复制失败，回退到普通复制
                    self.logger.warning(f"服务端复制失败，回退到普通复制: {file_meta.key}")

                    # 对SMB存储使用子进程隔离下载
                    if hasattr(source_adapter.config, 'storage_type') and source_adapter.config.storage_type.name == 'SMB':
                        self.logger.info(f"使用SMB子进程下载(回退): {file_meta.key}")
                        data = self._download_smb_file_with_subprocess(source_adapter.config, file_meta.key)
                    else:
                        self.logger.info(f"使用标准下载(回退): {file_meta.key}")
                        data = source_adapter.get_file(file_meta.key)
                    if data and target_adapter.put_file(file_meta.key, data):
                        success_count += 1
                    else:
                        failed_count += 1
                        db_manager.add_task_log(execution_id, 'ERROR', f'文件复制失败: {file_meta.key}')

                # 更新进度
                if (i + 1) % 100 == 0 or i == total_files - 1:
                    progress = int((i + 1) / total_files * 100)
                    elapsed_time = time.time() - start_time
                    avg_time_per_file = elapsed_time / (i + 1)
                    estimated_remaining = avg_time_per_file * (total_files - i - 1)

                    self.logger.info(f"服务端复制进度: {progress}% ({i+1}/{total_files})")
                    db_manager.add_task_log(execution_id, 'INFO',
                        f'服务端复制进度: {progress}% ({i+1}/{total_files}), '
                        f'成功: {success_count}, 失败: {failed_count}, '
                        f'预计剩余时间: {estimated_remaining:.1f}秒')

            except Exception as e:
                failed_count += 1
                self.logger.error(f"服务端复制异常: {file_meta.key} - {e}")
                db_manager.add_task_log(execution_id, 'ERROR', f'服务端复制异常: {file_meta.key} - {e}')

        total_time = time.time() - start_time
        avg_speed = total_files / total_time if total_time > 0 else 0

        completion_msg = (f"服务端复制完成: 总计 {total_files} 个文件, "
                         f"成功 {success_count}, 失败 {failed_count}, "
                         f"耗时 {total_time:.1f}秒, 平均速度 {avg_speed:.1f} 文件/秒")

        self.logger.info(completion_msg)
        db_manager.add_task_log(execution_id, 'INFO', completion_msg)

    def _download_smb_file_with_subprocess(self, smb_config, file_key):
        """使用子进程隔离下载SMB文件"""
        import subprocess
        import json
        import base64
        import os
        import tempfile

        temp_file = None
        try:
            # 准备配置数据
            config_data = {
                'hostname': smb_config.hostname,
                'port': smb_config.port,
                'username': smb_config.username,
                'password': smb_config.password,
                'domain': smb_config.domain or '',
                'share_name': smb_config.share_name,
                'root_path': smb_config.root_path or ''
            }

            # 创建临时文件
            with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False, encoding='utf-8') as f:
                json.dump({
                    'config': config_data,
                    'file_key': file_key
                }, f)
                temp_file = f.name

            # 构建命令
            script_path = os.path.join(os.path.dirname(__file__), 'smb_downloader.py')
            cmd = ['python', script_path, temp_file]

            self.logger.debug(f"SMB子进程命令: {cmd}")
            self.logger.debug(f"SMB子进程工作目录: {os.path.dirname(__file__)}")

            # 执行子进程
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=30,
                cwd=os.path.dirname(__file__),
                encoding='utf-8',
                errors='ignore'
            )

            self.logger.debug(f"SMB子进程返回码: {result.returncode}")
            self.logger.debug(f"SMB子进程标准输出: {result.stdout}")
            self.logger.debug(f"SMB子进程标准错误: {result.stderr}")

            if result.returncode == 0:
                output = result.stdout.strip()
                # 查找SUCCESS行
                success_line = None
                for line in output.split('\n'):
                    if line.startswith("SUCCESS:"):
                        success_line = line
                        break

                if success_line:
                    # 解析成功结果：SUCCESS:size:output_file
                    parts = success_line.split(':', 2)
                    if len(parts) == 3:
                        size = int(parts[1])
                        output_file = parts[2]

                        # 从输出文件读取数据
                        try:
                            with open(output_file, 'rb') as f:
                                data = f.read()
                            # 清理输出文件
                            os.unlink(output_file)
                            self.logger.debug(f"SMB子进程下载成功: {file_key} ({size} bytes)")
                            return data
                        except Exception as e:
                            self.logger.warning(f"SMB子进程读取输出文件失败 {file_key}: {e}")
                            return None
                    else:
                        self.logger.warning(f"SMB子进程输出格式错误: {output}")
                        return None
                else:
                    # 查找FAILED或ERROR行
                    for line in output.split('\n'):
                        if line.startswith("FAILED:"):
                            self.logger.warning(f"SMB子进程下载失败: {file_key}")
                            return None
                        elif line.startswith("ERROR:"):
                            error_msg = line[6:]  # 移除"ERROR:"前缀
                            self.logger.warning(f"SMB子进程错误 {file_key}: {error_msg}")
                            return None

                    self.logger.warning(f"SMB子进程未知输出: {output}")
                    return None
            else:
                self.logger.warning(f"SMB子进程失败 {file_key} (返回码: {result.returncode}): {result.stderr}")
                return None

        except subprocess.TimeoutExpired:
            self.logger.warning(f"SMB子进程下载超时: {file_key}")
            return None
        except Exception as e:
            self.logger.warning(f"SMB子进程下载异常 {file_key}: {e}")
            return None
        finally:
            # 清理临时文件
            if temp_file and os.path.exists(temp_file):
                try:
                    os.unlink(temp_file)
                except:
                    pass
