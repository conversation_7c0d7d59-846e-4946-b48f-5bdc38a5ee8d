#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试任务运行修复
"""

import requests
import json
import time

def test_task_run_fix():
    """测试任务运行功能修复"""
    base_url = 'http://localhost:8008'  # 使用当前运行的服务器端口
    
    print('🔧 测试任务运行功能修复')
    print('=' * 50)
    
    # 1. 检查服务器连接
    print('\n1️⃣ 检查服务器连接')
    try:
        response = requests.get(f'{base_url}/api/statistics')
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print('✅ 服务器连接正常')
                stats = data.get('statistics', {})
                print(f'   📊 当前统计: {stats}')
            else:
                print(f'❌ 服务器响应错误: {data.get("message")}')
                return False
        else:
            print(f'❌ 服务器连接失败: {response.status_code}')
            return False
    except Exception as e:
        print(f'❌ 连接异常: {e}')
        return False
    
    # 2. 获取现有任务列表
    print('\n2️⃣ 获取现有任务列表')
    try:
        response = requests.get(f'{base_url}/api/tasks')
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                tasks = data.get('tasks', {})
                print(f'✅ 获取任务列表成功 ({len(tasks)} 个任务)')
                
                if tasks:
                    # 显示任务信息
                    for task_id, task_info in list(tasks.items())[:3]:  # 只显示前3个
                        print(f'   📋 任务: {task_info.get("name", "未命名")} (ID: {task_id[:8]}...)')
                else:
                    print('   ℹ️ 当前没有任务')
                
                return tasks
            else:
                print(f'❌ 获取任务列表失败: {data.get("message")}')
                return {}
        else:
            print(f'❌ 任务列表API失败: {response.status_code}')
            return {}
    except Exception as e:
        print(f'❌ 获取任务列表异常: {e}')
        return {}

def test_task_creation_and_run():
    """测试任务创建和运行"""
    base_url = 'http://localhost:8008'
    
    print('\n3️⃣ 测试任务创建和运行')
    
    # 首先创建测试数据源和目标
    print('\n   📁 创建测试数据源')
    test_source = {
        'name': '测试数据源',
        'type': 'local',
        'path': 'C:\\temp\\source'
    }
    
    try:
        response = requests.post(f'{base_url}/api/sources', 
                               json=test_source,
                               headers={'Content-Type': 'application/json'})
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                source_id = data.get('source_id')
                print(f'   ✅ 数据源创建成功: {source_id[:8]}...')
            else:
                print(f'   ❌ 数据源创建失败: {data.get("message")}')
                return False
        else:
            print(f'   ❌ 数据源创建API失败: {response.status_code}')
            return False
    except Exception as e:
        print(f'   ❌ 数据源创建异常: {e}')
        return False
    
    print('\n   🎯 创建测试目标存储')
    test_target = {
        'name': '测试目标存储',
        'type': 'local',
        'path': 'C:\\temp\\target'
    }
    
    try:
        response = requests.post(f'{base_url}/api/targets', 
                               json=test_target,
                               headers={'Content-Type': 'application/json'})
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                target_id = data.get('target_id')
                print(f'   ✅ 目标存储创建成功: {target_id[:8]}...')
            else:
                print(f'   ❌ 目标存储创建失败: {data.get("message")}')
                return False
        else:
            print(f'   ❌ 目标存储创建API失败: {response.status_code}')
            return False
    except Exception as e:
        print(f'   ❌ 目标存储创建异常: {e}')
        return False
    
    print('\n   📋 创建测试同步任务')
    test_task = {
        'name': '测试同步任务',
        'description': '用于测试任务运行功能的测试任务',
        'source_id': source_id,
        'target_id': target_id,
        'sync_mode': 'incremental',
        'max_workers': 2,
        'retry_times': 1
    }
    
    try:
        response = requests.post(f'{base_url}/api/tasks', 
                               json=test_task,
                               headers={'Content-Type': 'application/json'})
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                task_id = data.get('task_id')
                print(f'   ✅ 同步任务创建成功: {task_id[:8]}...')
            else:
                print(f'   ❌ 同步任务创建失败: {data.get("message")}')
                return False
        else:
            print(f'   ❌ 同步任务创建API失败: {response.status_code}')
            return False
    except Exception as e:
        print(f'   ❌ 同步任务创建异常: {e}')
        return False
    
    print('\n   🚀 测试任务运行')
    try:
        response = requests.post(f'{base_url}/api/tasks/{task_id}/run',
                               headers={'Content-Type': 'application/json'})
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print(f'   ✅ 任务启动成功: {data.get("message")}')
                return True
            else:
                print(f'   ❌ 任务启动失败: {data.get("message")}')
                return False
        else:
            print(f'   ❌ 任务启动API失败: {response.status_code}')
            print(f'   📄 响应内容: {response.text[:200]}')
            return False
    except Exception as e:
        print(f'   ❌ 任务启动异常: {e}')
        return False

def main():
    """主函数"""
    print('🧪 LightRek 任务运行功能测试')
    print('=' * 60)
    
    # 测试现有任务
    existing_tasks = test_task_run_fix()
    
    # 如果有现有任务，测试运行第一个
    if existing_tasks:
        first_task_id = list(existing_tasks.keys())[0]
        first_task_name = existing_tasks[first_task_id].get('name', '未命名')
        
        print(f'\n🎯 测试运行现有任务: {first_task_name}')
        
        base_url = 'http://localhost:8008'
        try:
            response = requests.post(f'{base_url}/api/tasks/{first_task_id}/run',
                                   headers={'Content-Type': 'application/json'})
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    print(f'✅ 现有任务启动成功: {data.get("message")}')
                else:
                    print(f'❌ 现有任务启动失败: {data.get("message")}')
            else:
                print(f'❌ 现有任务启动API失败: {response.status_code}')
                print(f'📄 响应内容: {response.text[:200]}')
        except Exception as e:
            print(f'❌ 现有任务启动异常: {e}')
    
    # 测试创建新任务并运行
    success = test_task_creation_and_run()
    
    # 总结
    print('\n' + '=' * 60)
    if success:
        print('🎉 任务运行功能修复成功!')
        print('✅ 任务管理器方法调用正常')
        print('✅ API响应格式正确')
        print('✅ 错误处理机制完善')
    else:
        print('❌ 任务运行功能仍有问题')
        print('请检查:')
        print('  - 任务管理器是否正确初始化')
        print('  - start_task 方法是否存在')
        print('  - 数据源和目标存储配置是否正确')
    
    print('\n💡 如果问题仍然存在，请检查服务器日志获取更多信息')

if __name__ == '__main__':
    main()
