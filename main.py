#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LightRek 统一存储同步工具 - 主启动程序
"""

import sys
import time
import argparse
import signal
from web_server import create_web_server


def signal_handler(signum, frame):
    """信号处理器"""
    print(f"\n🛑 收到信号 {signum}，正在关闭程序...")
    sys.exit(0)


def main():
    """主函数"""
    # 设置信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='LightRek 统一存储同步工具')
    parser.add_argument('--port', type=int, default=8001, help='Web界面端口 (默认: 8001)')
    parser.add_argument('--config', type=str, default='lightrek_unified_config.json', help='配置文件路径')
    parser.add_argument('--no-web', action='store_true', help='不启动Web界面')
    parser.add_argument('--version', action='version', version='LightRek v2.0.0')
    
    args = parser.parse_args()
    
    print("🚀 启动 LightRek 统一存储同步工具...")
    print(f"📁 配置文件: {args.config}")
    print(f"🌐 Web端口: {args.port}")
    
    try:
        # 导入必要的模块
        from unified_config_manager import UnifiedConfigManager
        from unified_task_manager import UnifiedTaskManager
        from database_manager import DatabaseManager
        
        # 初始化管理器
        print("⚙️ 初始化配置管理器...")
        config_manager = UnifiedConfigManager(config_file=args.config)
        
        print("📋 初始化任务管理器...")
        task_manager = UnifiedTaskManager(config_manager)
        
        print("💾 初始化数据库管理器...")
        db_manager = DatabaseManager()
        
        # 启动Web界面（如果需要）
        web_server = None
        if not args.no_web:
            print("🌐 启动Web界面...")
            web_server = create_web_server(config_manager, task_manager, db_manager, args.port)
            
            if web_server.start():
                print(f"✅ Web界面启动成功: {web_server.get_url()}")
                print("📖 用户手册: http://localhost:{}/manual".format(args.port))
            else:
                print("❌ Web界面启动失败")
                return 1
        
        # 显示系统状态
        print("\n📊 系统状态:")
        sources = config_manager.get_all_sources()
        targets = config_manager.get_all_targets()
        tasks = config_manager.get_all_tasks()
        
        print(f"   📁 数据源: {len(sources)} 个")
        print(f"   🎯 目标存储: {len(targets)} 个")
        print(f"   📋 同步任务: {len(tasks)} 个")
        
        if tasks:
            print("\n📋 已配置的任务:")
            for task_id, task in tasks.items():
                status = task_manager.get_task_status(task_id)
                print(f"   • {task.get('name', task_id)}: {status}")
        
        print("\n✅ LightRek 启动完成!")
        
        if not args.no_web:
            print(f"🌐 请访问 {web_server.get_url()} 使用Web界面")
        
        print("按 Ctrl+C 退出程序")
        
        # 主循环
        try:
            while True:
                time.sleep(1)
                
                # 检查Web服务器状态
                if web_server and not web_server.is_running():
                    print("⚠️ Web服务器意外停止，尝试重启...")
                    if not web_server.restart():
                        print("❌ Web服务器重启失败")
                        break
                
        except KeyboardInterrupt:
            print("\n🛑 收到停止信号")
        
        # 清理资源
        print("🧹 清理资源...")
        if web_server:
            web_server.stop()
        
        if task_manager:
            task_manager.stop_all_tasks()
        
        print("👋 LightRek 已退出")
        return 0
        
    except ImportError as e:
        print(f"❌ 导入模块失败: {e}")
        print("请确保所有依赖模块都已正确安装")
        return 1
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


def show_help():
    """显示帮助信息"""
    help_text = """
🚀 LightRek 统一存储同步工具

用法:
    python main.py [选项]

选项:
    --port PORT         Web界面端口 (默认: 8001)
    --config FILE       配置文件路径 (默认: lightrek_unified_config.json)
    --no-web           不启动Web界面
    --version          显示版本信息
    --help             显示此帮助信息

示例:
    python main.py                          # 使用默认设置启动
    python main.py --port 8080              # 使用8080端口启动Web界面
    python main.py --no-web                 # 只启动后台服务，不启动Web界面
    python main.py --config my_config.json  # 使用自定义配置文件

功能特性:
    ✅ 支持多种存储类型 (S3, SFTP, FTP, SMB, 本地存储)
    ✅ 实时任务监控和日志查看
    ✅ 增量同步和压缩传输
    ✅ 并发处理和性能优化
    ✅ 友好的Web管理界面

更多信息请访问: http://localhost:8001/manual
"""
    print(help_text)


if __name__ == "__main__":
    # 检查是否请求帮助
    if len(sys.argv) > 1 and sys.argv[1] in ['--help', '-h', 'help']:
        show_help()
        sys.exit(0)
    
    # 运行主程序
    exit_code = main()
    sys.exit(exit_code)
