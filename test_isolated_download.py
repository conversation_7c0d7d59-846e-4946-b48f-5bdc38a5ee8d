#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试完全隔离的SMB下载方法
"""

import smb_storage_adapter
from storage_abstraction import StorageFactory, SMBStorageConfig, StorageType

def test_isolated_download():
    """测试完全隔离的下载方法"""
    print("🧪 测试完全隔离的SMB下载方法")
    
    config = SMBStorageConfig(
        storage_type=StorageType.SMB,
        hostname='<PERSON><PERSON>',
        port=445,
        username='smb',
        password='smbsmb',
        domain='WORKGROUP',
        share_name='hlmj',
        root_path=''
    )
    
    adapter = StorageFactory.create_adapter(config)
    
    # 模拟生产环境的使用模式
    print("📋 模拟生产环境使用模式:")
    
    # 1. 先调用 list_files（这会改变连接状态）
    print("1. 调用 list_files...")
    try:
        result = adapter.list_files("", max_keys=5)
        print(f"   ✅ 成功: 找到 {len(result.files)} 个文件")
        
        if len(result.files) >= 3:
            test_files = result.files[:3]
            print("   文件列表:")
            for i, f in enumerate(test_files):
                print(f"     {i+1}. {f.key} ({f.size} bytes)")
        else:
            print("   ❌ 文件数量不足")
            return False
            
    except Exception as e:
        print(f"   ❌ 失败: {e}")
        return False
    
    # 2. 然后调用 get_file（使用隔离的下载方法）
    print("\n2. 使用隔离下载方法下载文件...")
    success_count = 0
    
    for i, file_meta in enumerate(test_files):
        print(f"   下载 {i+1}/{len(test_files)}: {file_meta.key}")
        try:
            data = adapter.get_file(file_meta.key)
            if data:
                print(f"     ✅ 成功: {len(data)} bytes")
                success_count += 1
                
                # 验证数据完整性
                if len(data) == file_meta.size:
                    print(f"     ✅ 大小匹配: {len(data)} == {file_meta.size}")
                else:
                    print(f"     ⚠️ 大小不匹配: {len(data)} != {file_meta.size}")
            else:
                print(f"     ❌ 失败: 返回None")
        except Exception as e:
            print(f"     ❌ 异常: {e}")
    
    print(f"\n📊 下载结果: {success_count}/{len(test_files)} 成功")
    
    # 3. 再次调用 list_files 验证连接状态未被破坏
    print("\n3. 再次调用 list_files 验证连接状态...")
    try:
        result2 = adapter.list_files("", max_keys=3)
        print(f"   ✅ 成功: 找到 {len(result2.files)} 个文件")
    except Exception as e:
        print(f"   ❌ 失败: {e}")
        return False
    
    # 4. 再次下载验证稳定性
    print("\n4. 再次下载验证稳定性...")
    success_count2 = 0
    
    for i, file_meta in enumerate(test_files[:2]):  # 只测试前2个文件
        print(f"   下载 {i+1}/2: {file_meta.key}")
        try:
            data = adapter.get_file(file_meta.key)
            if data:
                print(f"     ✅ 成功: {len(data)} bytes")
                success_count2 += 1
            else:
                print(f"     ❌ 失败: 返回None")
        except Exception as e:
            print(f"     ❌ 异常: {e}")
    
    print(f"\n📊 第二次下载结果: {success_count2}/2 成功")
    
    # 评估结果
    total_expected = len(test_files) + 2
    total_success = success_count + success_count2
    success_rate = total_success / total_expected
    
    print(f"\n🎯 总体结果:")
    print(f"   成功率: {total_success}/{total_expected} ({success_rate:.1%})")
    
    if success_rate >= 0.8:
        print("   ✅ 隔离下载方法工作正常")
        return True
    else:
        print("   ❌ 隔离下载方法仍有问题")
        return False

def test_concurrent_isolated_download():
    """测试并发隔离下载"""
    print("\n🧪 测试并发隔离下载")
    
    config = SMBStorageConfig(
        storage_type=StorageType.SMB,
        hostname='Jayce',
        port=445,
        username='smb',
        password='smbsmb',
        domain='WORKGROUP',
        share_name='hlmj',
        root_path=''
    )
    
    import threading
    import time
    
    # 测试文件
    test_files = ['398.xml', '398_MonitorData.ini', 'BugCrashReporter.exe']
    results = {}
    
    def download_worker(filename, thread_id):
        """下载工作线程"""
        print(f"   线程{thread_id} 开始下载: {filename}")
        try:
            adapter = StorageFactory.create_adapter(config)
            data = adapter.get_file(filename)
            if data:
                results[thread_id] = {'success': True, 'size': len(data), 'file': filename}
                print(f"   线程{thread_id} ✅ 成功: {len(data)} bytes")
            else:
                results[thread_id] = {'success': False, 'error': 'None returned', 'file': filename}
                print(f"   线程{thread_id} ❌ 失败: 返回None")
        except Exception as e:
            results[thread_id] = {'success': False, 'error': str(e), 'file': filename}
            print(f"   线程{thread_id} ❌ 异常: {e}")
    
    # 启动并发下载
    threads = []
    start_time = time.time()
    
    for i, filename in enumerate(test_files):
        thread = threading.Thread(target=download_worker, args=(filename, i+1))
        threads.append(thread)
        thread.start()
    
    # 等待所有线程完成
    for thread in threads:
        thread.join()
    
    end_time = time.time()
    
    # 分析结果
    successful = sum(1 for r in results.values() if r['success'])
    print(f"\n📊 并发下载结果:")
    print(f"   成功率: {successful}/{len(test_files)}")
    print(f"   总耗时: {end_time - start_time:.2f}s")
    
    return successful >= len(test_files) * 0.8

def main():
    """主函数"""
    print("🧪 完全隔离的SMB下载方法测试")
    print("=" * 60)
    
    tests = [
        ("隔离下载方法测试", test_isolated_download),
        ("并发隔离下载测试", test_concurrent_isolated_download),
    ]
    
    passed_tests = 0
    
    for test_name, test_func in tests:
        print(f"\n📋 执行测试: {test_name}")
        
        try:
            if test_func():
                print(f"✅ {test_name} 通过")
                passed_tests += 1
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
            import traceback
            traceback.print_exc()
    
    print(f"\n📊 测试结果: {passed_tests}/{len(tests)} 通过")
    
    if passed_tests >= len(tests):
        print("🎉 完全隔离的SMB下载方法工作正常！")
        print("")
        print("🚀 现在可以重新运行同步任务，应该能够:")
        print("  ✅ 在调用 list_files 后成功下载文件")
        print("  ✅ 避免连接状态污染问题")
        print("  ✅ 消除 STATUS_OBJECT_NAME_INVALID 错误")
        
        return 0
    else:
        print("❌ 完全隔离的SMB下载方法仍有问题")
        return 1

if __name__ == "__main__":
    exit(main())
