#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试文件浏览器功能
"""

import logging
import tempfile
import os
from datetime import datetime

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def test_file_browser_manager():
    """测试文件浏览器管理器"""
    logger = setup_logging()
    
    try:
        from file_browser_manager import FileBrowserManager
        from unified_config_manager import UnifiedConfigManager
        
        config_manager = UnifiedConfigManager()
        browser = FileBrowserManager(config_manager)
        
        logger.info("✅ 文件浏览器管理器创建成功")
        
        # 检查必要的方法
        required_methods = [
            'browse_storage',
            'download_files',
            'get_file_info'
        ]
        
        for method_name in required_methods:
            if hasattr(browser, method_name):
                logger.info(f"✅ 方法 {method_name} 存在")
            else:
                logger.error(f"❌ 方法 {method_name} 不存在")
                return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试文件浏览器管理器失败: {e}")
        return False

def test_local_storage_browsing():
    """测试本地存储浏览"""
    logger = logging.getLogger(__name__)
    
    try:
        from file_browser_manager import FileBrowserManager
        from unified_config_manager import UnifiedConfigManager
        from storage_abstraction import LocalStorageConfig
        
        config_manager = UnifiedConfigManager()
        browser = FileBrowserManager(config_manager)
        
        # 创建临时测试目录
        temp_dir = tempfile.mkdtemp()
        logger.info(f"创建测试目录: {temp_dir}")
        
        # 创建测试文件和子目录
        test_files = [
            'test1.txt',
            'test2.pdf',
            'subdir/test3.jpg',
            'subdir/nested/test4.zip'
        ]
        
        for file_path in test_files:
            full_path = os.path.join(temp_dir, file_path)
            os.makedirs(os.path.dirname(full_path), exist_ok=True)
            with open(full_path, 'w') as f:
                f.write(f"Test content for {file_path}")
        
        # 创建本地存储配置
        storage_config = LocalStorageConfig(
            root_path=temp_dir,
            name="测试本地存储",
            description="用于测试文件浏览器的本地存储"
        )
        
        # 添加到配置管理器
        test_storage_id = "test_local_storage"
        config_manager.add_source(test_storage_id, "local", {
            'name': storage_config.name,
            'description': storage_config.description,
            'root_path': storage_config.root_path
        })
        
        # 测试浏览根目录
        result = browser.browse_storage(test_storage_id, 'source', '')
        
        if result['success']:
            files = result['files']
            logger.info(f"✅ 成功浏览根目录，找到 {len(files)} 个项目")
            
            # 检查是否包含预期的文件和目录
            file_names = [f['name'] for f in files]
            expected_items = ['test1.txt', 'test2.pdf', 'subdir']
            
            for item in expected_items:
                if item in file_names:
                    logger.info(f"✅ 找到预期项目: {item}")
                else:
                    logger.error(f"❌ 未找到预期项目: {item}")
                    return False
            
            # 测试浏览子目录
            subdir_result = browser.browse_storage(test_storage_id, 'source', 'subdir')
            
            if subdir_result['success']:
                subdir_files = subdir_result['files']
                logger.info(f"✅ 成功浏览子目录，找到 {len(subdir_files)} 个项目")
                
                subdir_names = [f['name'] for f in subdir_files]
                if 'test3.jpg' in subdir_names and 'nested' in subdir_names:
                    logger.info("✅ 子目录内容正确")
                else:
                    logger.error(f"❌ 子目录内容不正确: {subdir_names}")
                    return False
            else:
                logger.error(f"❌ 浏览子目录失败: {subdir_result['message']}")
                return False
        else:
            logger.error(f"❌ 浏览根目录失败: {result['message']}")
            return False
        
        # 清理测试数据
        import shutil
        shutil.rmtree(temp_dir)
        config_manager.remove_source(test_storage_id)
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试本地存储浏览失败: {e}")
        return False

def test_download_functionality():
    """测试下载功能"""
    logger = logging.getLogger(__name__)
    
    try:
        from file_browser_manager import FileBrowserManager
        from unified_config_manager import UnifiedConfigManager
        
        config_manager = UnifiedConfigManager()
        browser = FileBrowserManager(config_manager)
        
        # 创建临时测试目录
        temp_dir = tempfile.mkdtemp()
        logger.info(f"创建测试目录: {temp_dir}")
        
        # 创建测试文件
        test_files = ['file1.txt', 'file2.txt', 'file3.txt']
        file_paths = []
        
        for filename in test_files:
            file_path = os.path.join(temp_dir, filename)
            with open(file_path, 'w') as f:
                f.write(f"Test content for {filename} - {datetime.now()}")
            file_paths.append(filename)
        
        # 创建本地存储配置
        test_storage_id = "test_download_storage"
        config_manager.add_source(test_storage_id, "local", {
            'name': "测试下载存储",
            'description': "用于测试下载功能的本地存储",
            'root_path': temp_dir
        })
        
        # 测试下载功能
        result = browser.download_files(test_storage_id, 'source', file_paths, 'test_download.zip')
        
        if result['success']:
            logger.info(f"✅ 下载成功，文件: {result['zip_filename']}")
            logger.info(f"   下载文件数: {result['downloaded_count']}")
            logger.info(f"   失败文件数: {result['failed_count']}")
            logger.info(f"   ZIP大小: {result['zip_size']} 字节")
            
            # 检查ZIP文件是否存在
            if os.path.exists(result['zip_path']):
                logger.info("✅ ZIP文件创建成功")
                
                # 清理ZIP文件
                os.unlink(result['zip_path'])
                try:
                    os.rmdir(result['temp_dir'])
                except:
                    pass
            else:
                logger.error("❌ ZIP文件未创建")
                return False
        else:
            logger.error(f"❌ 下载失败: {result['message']}")
            return False
        
        # 清理测试数据
        import shutil
        shutil.rmtree(temp_dir)
        config_manager.remove_source(test_storage_id)
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试下载功能失败: {e}")
        return False

def test_web_api_integration():
    """测试Web API集成"""
    logger = logging.getLogger(__name__)
    
    try:
        import requests
        
        base_url = 'http://localhost:8000'
        
        # 测试浏览API（需要Web服务器运行）
        try:
            # 这里只是测试API端点是否存在，实际测试需要有效的存储配置
            response = requests.post(f'{base_url}/api/browse/source/test', 
                                   json={'path': ''}, timeout=5)
            
            # 即使返回错误，只要不是404就说明API端点存在
            if response.status_code != 404:
                logger.info("✅ 浏览API端点存在")
            else:
                logger.warning("⚠️ 浏览API端点不存在")
                
        except requests.exceptions.RequestException as e:
            logger.warning(f"⚠️ 无法连接到Web服务器: {e}")
            logger.info("这是正常的，如果Web服务器没有运行")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试Web API集成失败: {e}")
        return False

def main():
    """主函数"""
    logger = setup_logging()
    
    logger.info("🧪 文件浏览器功能测试")
    logger.info("=" * 60)
    
    tests = [
        ("文件浏览器管理器测试", test_file_browser_manager),
        ("本地存储浏览测试", test_local_storage_browsing),
        ("下载功能测试", test_download_functionality),
        ("Web API集成测试", test_web_api_integration),
    ]
    
    passed_tests = 0
    
    for test_name, test_func in tests:
        logger.info(f"\n📋 执行测试: {test_name}")
        
        try:
            if test_func():
                logger.info(f"✅ {test_name} 通过")
                passed_tests += 1
            else:
                logger.error(f"❌ {test_name} 失败")
        except Exception as e:
            logger.error(f"❌ {test_name} 异常: {e}")
    
    logger.info(f"\n📊 测试结果: {passed_tests}/{len(tests)} 通过")
    
    if passed_tests == len(tests):
        logger.info("🎉 所有测试通过！文件浏览器功能已就绪")
        logger.info("")
        logger.info("📋 新增功能:")
        logger.info("  ✅ 文件和文件夹遍历 - 支持所有存储类型")
        logger.info("  ✅ 树形目录导航 - 面包屑导航和点击跳转")
        logger.info("  ✅ 文件选择和预览 - 支持多选和文件信息显示")
        logger.info("  ✅ 打包下载功能 - 选中文件打包为ZIP下载")
        logger.info("  ✅ 响应式界面 - 适配桌面和移动设备")
        logger.info("")
        logger.info("🚀 使用方法:")
        logger.info("  1. 在数据源或目标存储页面点击'📁 浏览文件'按钮")
        logger.info("  2. 选择要浏览的存储")
        logger.info("  3. 点击文件夹进入子目录")
        logger.info("  4. 选择文件后点击'📦 下载选中文件'")
        logger.info("  5. 文件将自动打包为ZIP并下载到本地")
        
        return 0
    else:
        logger.error("❌ 部分测试失败，需要进一步修复")
        return 1

if __name__ == "__main__":
    exit(main())
