#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试存储适配器加载
"""

def test_storage_adapters():
    """测试存储适配器加载"""
    print("🔍 测试存储适配器加载")
    print("=" * 50)
    
    # 1. 检查paramiko是否可用
    print("\n1️⃣ 检查paramiko是否可用")
    try:
        import paramiko
        print(f"✅ paramiko可用，版本: {paramiko.__version__}")
    except ImportError as e:
        print(f"❌ paramiko不可用: {e}")
        print("请运行: pip install paramiko")
    
    # 2. 检查存储抽象层
    print("\n2️⃣ 检查存储抽象层")
    try:
        from storage_abstraction import StorageFactory, StorageType
        print("✅ 存储抽象层导入成功")
        print(f"初始支持的类型: {[t.value for t in StorageFactory.get_supported_types()]}")
    except ImportError as e:
        print(f"❌ 存储抽象层导入失败: {e}")
        return
    
    # 3. 导入各种适配器
    print("\n3️⃣ 导入各种适配器")
    adapters = [
        ('s3_storage_adapter', 'S3'),
        ('sftp_storage_adapter', 'SFTP'),
        ('smb_storage_adapter', 'SMB'),
        ('ftp_storage_adapter', 'FTP'),
        ('local_storage_adapter', 'Local')
    ]
    
    for module_name, adapter_name in adapters:
        try:
            module = __import__(module_name)
            print(f"✅ {adapter_name}适配器导入成功")
        except ImportError as e:
            print(f"❌ {adapter_name}适配器导入失败: {e}")
    
    # 4. 检查注册情况
    print("\n4️⃣ 检查适配器注册情况")
    print(f"支持的类型: {[t.value for t in StorageFactory.get_supported_types()]}")
    
    # 5. 测试SFTP适配器
    print("\n5️⃣ 测试SFTP适配器")
    if StorageType.SFTP in StorageFactory.get_supported_types():
        try:
            from storage_abstraction import SFTPStorageConfig
            config = SFTPStorageConfig(
                hostname="test.example.com",
                port=22,
                username="test",
                password="test"
            )
            print("✅ SFTP配置创建成功")
            
            try:
                adapter = StorageFactory.create_adapter(config)
                print(f"✅ SFTP适配器创建成功: {type(adapter).__name__}")
            except Exception as e:
                print(f"❌ SFTP适配器创建失败: {e}")
        except Exception as e:
            print(f"❌ SFTP配置创建失败: {e}")
    else:
        print("❌ SFTP适配器未注册")
    
    # 6. 测试任务管理器中的适配器加载
    print("\n6️⃣ 测试任务管理器中的适配器加载")
    try:
        from unified_task_manager import UnifiedTaskManager
        from unified_config_manager import UnifiedConfigManager
        
        config_manager = UnifiedConfigManager()
        task_manager = UnifiedTaskManager(config_manager)
        
        print("✅ 任务管理器初始化成功")
        print(f"任务管理器加载后支持的类型: {[t.value for t in StorageFactory.get_supported_types()]}")
    except Exception as e:
        print(f"❌ 任务管理器初始化失败: {e}")
    
    # 7. 测试创建SFTP任务
    print("\n7️⃣ 测试创建SFTP任务")
    try:
        # 创建SFTP源
        source_id = config_manager.add_source(
            "test_sftp_source",
            "sftp",
            {
                "name": "测试SFTP源",
                "hostname": "test.example.com",
                "port": 22,
                "username": "test",
                "password": "test"
            }
        )
        print(f"✅ SFTP源创建成功: {source_id}")
        
        # 创建本地目标
        target_id = config_manager.add_target(
            "test_local_target",
            "local",
            {
                "name": "测试本地目标",
                "root_path": "C:\\temp\\test_target"
            }
        )
        print(f"✅ 本地目标创建成功: {target_id}")
        
        # 创建任务
        task_id = task_manager.create_task(
            name="测试SFTP任务",
            description="测试SFTP到本地的同步任务",
            source_id=source_id,
            target_id=target_id
        )
        print(f"✅ 任务创建成功: {task_id}")
        
        # 尝试启动任务
        print("⚠️ 注意: 这里不会真正启动任务，因为连接信息是虚构的")
    except Exception as e:
        print(f"❌ 创建SFTP任务失败: {e}")

def main():
    """主函数"""
    print("🧪 LightRek 存储适配器测试")
    print("=" * 60)
    
    test_storage_adapters()
    
    print("\n" + "=" * 60)
    print("📋 测试总结:")
    print("1. 如果看到 'SFTP适配器已加载'，说明适配器模块导入成功")
    print("2. 如果看到 'sftp' 在支持的类型列表中，说明适配器已正确注册")
    print("3. 如果看到 'SFTP适配器创建成功'，说明适配器可以正常实例化")
    print("4. 如果看到 'SFTP源创建成功'，说明配置管理器可以正确处理SFTP配置")
    print("\n💡 如果仍有问题，请检查:")
    print("- paramiko库是否正确安装")
    print("- sftp_storage_adapter.py文件是否存在")
    print("- PARAMIKO_AVAILABLE变量是否正确设置")
    print("- 适配器注册代码是否正确执行")

if __name__ == "__main__":
    main()
