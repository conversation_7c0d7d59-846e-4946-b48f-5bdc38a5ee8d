#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Web界面功能测试
"""

import requests
import json
import time

def test_web_interface():
    """测试Web界面的API功能"""
    print('=== Web界面功能测试 ===')
    
    base_url = 'http://localhost:8002'
    
    print('\n--- 测试基础页面访问 ---')
    # 测试主页
    try:
        response = requests.get(f'{base_url}/')
        print(f'主页访问: {response.status_code == 200}')
        if response.status_code == 200:
            print(f'主页内容长度: {len(response.text)} 字符')
    except Exception as e:
        print(f'主页访问失败: {e}')
    
    # 测试用户手册页面
    try:
        response = requests.get(f'{base_url}/manual')
        print(f'用户手册页面: {response.status_code == 200}')
        if response.status_code == 200:
            print(f'手册内容长度: {len(response.text)} 字符')
    except Exception as e:
        print(f'用户手册访问失败: {e}')
    
    print('\n--- 测试API端点 ---')
    
    # 测试获取数据源
    try:
        response = requests.get(f'{base_url}/api/sources')
        print(f'获取数据源API: {response.status_code == 200}')
        if response.status_code == 200:
            sources_data = response.json()
            print(f'数据源数量: {len(sources_data.get("sources", {}))}')
            if sources_data.get("sources"):
                print('数据源列表:')
                for source_id, source in sources_data["sources"].items():
                    print(f'  - {source_id}: {source.get("name")} ({source.get("storage_type")})')
    except Exception as e:
        print(f'获取数据源失败: {e}')
    
    # 测试获取目标存储
    try:
        response = requests.get(f'{base_url}/api/targets')
        print(f'获取目标存储API: {response.status_code == 200}')
        if response.status_code == 200:
            targets_data = response.json()
            print(f'目标存储数量: {len(targets_data.get("targets", {}))}')
            if targets_data.get("targets"):
                print('目标存储列表:')
                for target_id, target in targets_data["targets"].items():
                    print(f'  - {target_id}: {target.get("name")} ({target.get("storage_type")})')
    except Exception as e:
        print(f'获取目标存储失败: {e}')
    
    # 测试获取任务
    try:
        response = requests.get(f'{base_url}/api/tasks')
        print(f'获取任务API: {response.status_code == 200}')
        if response.status_code == 200:
            tasks_data = response.json()
            print(f'任务数量: {len(tasks_data.get("tasks", {}))}')
            if tasks_data.get("tasks"):
                print('任务列表:')
                for task_id, task in tasks_data["tasks"].items():
                    print(f'  - {task_id}: {task.get("name")} ({task.get("sync_mode")})')
    except Exception as e:
        print(f'获取任务失败: {e}')
    
    # 测试任务状态
    try:
        response = requests.get(f'{base_url}/api/task-status')
        print(f'获取任务状态API: {response.status_code == 200}')
        if response.status_code == 200:
            status_data = response.json()
            print(f'任务状态数量: {len(status_data.get("status", {}))}')
    except Exception as e:
        print(f'获取任务状态失败: {e}')
    
    # 测试任务执行历史
    try:
        response = requests.get(f'{base_url}/api/task-executions')
        print(f'获取任务执行历史API: {response.status_code == 200}')
        if response.status_code == 200:
            executions_data = response.json()
            print(f'执行历史数量: {len(executions_data.get("executions", []))}')
    except Exception as e:
        print(f'获取任务执行历史失败: {e}')
    
    # 测试统计信息
    try:
        response = requests.get(f'{base_url}/api/statistics')
        print(f'获取统计信息API: {response.status_code == 200}')
        if response.status_code == 200:
            stats_data = response.json()
            if stats_data.get("success"):
                stats = stats_data.get("statistics", {})
                print(f'统计信息: 源={stats.get("sources")}, 目标={stats.get("targets")}, 任务={stats.get("tasks")}')
    except Exception as e:
        print(f'获取统计信息失败: {e}')
    
    print('\n--- 测试POST API功能 ---')
    
    # 测试添加本地数据源
    test_source_data = {
        'name': 'API测试本地源',
        'description': '通过API测试添加的本地数据源',
        'storage_type': 'local',
        'root_path': 'C:/temp/api_test_source'
    }
    
    try:
        response = requests.post(f'{base_url}/api/sources', 
                               json=test_source_data,
                               headers={'Content-Type': 'application/json'})
        print(f'添加数据源API: {response.status_code == 200}')
        if response.status_code == 200:
            result = response.json()
            print(f'添加结果: {result.get("success")}, 消息: {result.get("message")}')
    except Exception as e:
        print(f'添加数据源失败: {e}')
    
    # 测试添加本地目标存储
    test_target_data = {
        'name': 'API测试本地目标',
        'description': '通过API测试添加的本地目标存储',
        'storage_type': 'local',
        'root_path': 'C:/temp/api_test_target'
    }
    
    try:
        response = requests.post(f'{base_url}/api/targets',
                               json=test_target_data,
                               headers={'Content-Type': 'application/json'})
        print(f'添加目标存储API: {response.status_code == 200}')
        if response.status_code == 200:
            result = response.json()
            print(f'添加结果: {result.get("success")}, 消息: {result.get("message")}')
    except Exception as e:
        print(f'添加目标存储失败: {e}')
    
    # 等待一下让数据保存
    time.sleep(1)
    
    # 重新获取数据源和目标，找到刚添加的
    try:
        response = requests.get(f'{base_url}/api/sources')
        if response.status_code == 200:
            sources_data = response.json()
            api_test_source_id = None
            for source_id, source in sources_data.get("sources", {}).items():
                if source.get("name") == "API测试本地源":
                    api_test_source_id = source_id
                    break
            
        response = requests.get(f'{base_url}/api/targets')
        if response.status_code == 200:
            targets_data = response.json()
            api_test_target_id = None
            for target_id, target in targets_data.get("targets", {}).items():
                if target.get("name") == "API测试本地目标":
                    api_test_target_id = target_id
                    break
        
        # 如果找到了源和目标，创建测试任务
        if api_test_source_id and api_test_target_id:
            test_task_data = {
                'name': 'API测试同步任务',
                'description': '通过API创建的测试同步任务',
                'source_id': api_test_source_id,
                'target_id': api_test_target_id,
                'sync_mode': 'incremental',
                'max_workers': 5,
                'verify_integrity': True
            }
            
            response = requests.post(f'{base_url}/api/tasks',
                                   json=test_task_data,
                                   headers={'Content-Type': 'application/json'})
            print(f'添加任务API: {response.status_code == 200}')
            if response.status_code == 200:
                result = response.json()
                print(f'任务添加结果: {result.get("success")}, 消息: {result.get("message")}')
                if result.get("success"):
                    task_id = result.get("task_id")
                    print(f'新任务ID: {task_id}')
        else:
            print('未找到测试用的源或目标，跳过任务创建测试')
            
    except Exception as e:
        print(f'创建测试任务失败: {e}')
    
    print('\n--- 测试连接测试功能 ---')
    
    # 测试本地存储连接
    connection_test_data = {
        'storage_type': 'local',
        'root_path': 'C:/temp'
    }
    
    try:
        response = requests.post(f'{base_url}/api/test-connection',
                               json=connection_test_data,
                               headers={'Content-Type': 'application/json'})
        print(f'连接测试API: {response.status_code == 200}')
        if response.status_code == 200:
            result = response.json()
            print(f'连接测试结果: {result.get("success")}, 消息: {result.get("message")}')
    except Exception as e:
        print(f'连接测试失败: {e}')
    
    print('\n--- 测试优化配置 ---')
    
    # 获取当前优化配置
    try:
        response = requests.get(f'{base_url}/api/optimization-config')
        print(f'获取优化配置API: {response.status_code == 200}')
        if response.status_code == 200:
            config_data = response.json()
            print(f'当前优化配置: {config_data}')
    except Exception as e:
        print(f'获取优化配置失败: {e}')
    
    # 更新优化配置
    optimization_data = {
        'max_workers': 25,
        'chunk_size_mb': 15,
        'retry_times': 5,
        'enable_parallel_scan': True,
        'enable_cache': True,
        'cache_ttl_hours': 48
    }
    
    try:
        response = requests.post(f'{base_url}/api/optimization-config',
                               json=optimization_data,
                               headers={'Content-Type': 'application/json'})
        print(f'更新优化配置API: {response.status_code == 200}')
        if response.status_code == 200:
            result = response.json()
            print(f'优化配置更新结果: {result.get("success")}, 消息: {result.get("message")}')
    except Exception as e:
        print(f'更新优化配置失败: {e}')
    
    print('\n--- 测试错误处理 ---')
    
    # 测试无效的API端点
    try:
        response = requests.get(f'{base_url}/api/invalid-endpoint')
        print(f'无效端点响应状态: {response.status_code}')
    except Exception as e:
        print(f'无效端点测试异常: {e}')
    
    # 测试无效的POST数据
    try:
        response = requests.post(f'{base_url}/api/sources',
                               json={'invalid': 'data'},
                               headers={'Content-Type': 'application/json'})
        print(f'无效数据POST响应状态: {response.status_code}')
        if response.status_code == 200:
            result = response.json()
            print(f'无效数据响应: {result.get("success")}, 错误: {result.get("message")}')
    except Exception as e:
        print(f'无效数据POST测试异常: {e}')
    
    print('Web界面功能测试完成')

if __name__ == '__main__':
    test_web_interface()
