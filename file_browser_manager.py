#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件浏览器管理器 - 提供文件遍历和打包下载功能
"""

import os
import zipfile
import tempfile
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
import asyncio
from pathlib import Path
import mimetypes

from storage_abstraction import StorageFactory, StorageType, FileMetadata
from timezone_compat import timezone, safe_replace_timezone, safe_fromtimestamp

class FileBrowserManager:
    """文件浏览器管理器"""

    def __init__(self, config_manager):
        self.config_manager = config_manager
        self.logger = logging.getLogger(__name__)

    def _safe_format_datetime(self, dt):
        """安全地格式化日期时间，处理时区问题"""
        if dt is None:
            return None

        try:
            # 如果是数字（时间戳）
            if isinstance(dt, (int, float)):
                try:
                    dt = safe_fromtimestamp(dt, timezone.utc)
                except:
                    return str(dt)

            # 如果是字符串，尝试解析
            if isinstance(dt, str):
                try:
                    # 处理各种ISO格式
                    if dt.endswith('Z'):
                        dt = dt.replace('Z', '+00:00')
                    dt = datetime.fromisoformat(dt)
                except ValueError:
                    try:
                        # 尝试其他常见格式
                        dt = datetime.strptime(dt, '%Y-%m-%d %H:%M:%S')
                        dt = safe_replace_timezone(dt, timezone.utc)
                    except ValueError:
                        return dt  # 返回原始字符串

            # 如果是datetime对象
            if isinstance(dt, datetime):
                # 如果没有时区信息，假设为UTC
                if dt.tzinfo is None:
                    dt = safe_replace_timezone(dt, timezone.utc)

                # 转换为ISO格式字符串
                return dt.isoformat()

            return str(dt)

        except Exception as e:
            self.logger.warning(f"格式化日期时间失败: {e}, 输入: {dt}")
            return str(dt) if dt else None
        
    def browse_storage(self, storage_id: str, storage_type: str, path: str = "") -> Dict[str, Any]:
        """浏览存储中的文件和文件夹"""
        try:
            # 获取存储配置
            if storage_type == 'source':
                storage_config = self.config_manager.get_source(storage_id)
            else:
                storage_config = self.config_manager.get_target(storage_id)
            
            if not storage_config:
                return {'success': False, 'message': '存储配置不存在'}
            
            # 创建存储适配器
            adapter = StorageFactory.create_adapter(storage_config)
            
            # 连接存储
            try:
                if hasattr(adapter, 'connect_sync'):
                    if not adapter.connect_sync():
                        return {'success': False, 'message': '连接存储失败'}
                elif hasattr(adapter, 'connect'):
                    # 检查是否为异步方法
                    import inspect
                    if inspect.iscoroutinefunction(adapter.connect):
                        # 异步连接 - 使用超时机制
                        try:
                            loop = asyncio.get_event_loop()
                        except RuntimeError:
                            loop = asyncio.new_event_loop()
                            asyncio.set_event_loop(loop)

                        try:
                            # 设置30秒超时
                            task = asyncio.wait_for(adapter.connect(), timeout=30.0)
                            if loop.is_running():
                                # 如果循环正在运行，使用线程池
                                import concurrent.futures
                                with concurrent.futures.ThreadPoolExecutor() as executor:
                                    future = executor.submit(asyncio.run, task)
                                    if not future.result(timeout=30):
                                        return {'success': False, 'message': '连接存储失败'}
                            else:
                                if not loop.run_until_complete(task):
                                    return {'success': False, 'message': '连接存储失败'}
                        except asyncio.TimeoutError:
                            return {'success': False, 'message': '连接存储超时'}
                    else:
                        # 同步连接
                        if not adapter.connect():
                            return {'success': False, 'message': '连接存储失败'}
            except Exception as e:
                error_msg = f"连接存储失败: {str(e)}"
                self.logger.error(error_msg)
                return {'success': False, 'message': error_msg}
            
            # 获取文件列表
            files = []
            directories = set()

            try:
                if hasattr(adapter, 'list_files_sync'):
                    result = adapter.list_files_sync(path)
                    file_list = []
                    if hasattr(result, 'files'):
                        # 如果是ListResult对象，提取files属性
                        files_data = result.files
                        if hasattr(files_data, '__iter__'):
                            for file_meta in files_data:
                                file_list.append(file_meta)
                        else:
                            file_list.append(files_data)
                    elif hasattr(result, '__iter__') and not isinstance(result, str):
                        # 如果是迭代器（排除字符串）
                        for file_meta in result:
                            file_list.append(file_meta)
                    else:
                        # 如果是单个结果或列表
                        if isinstance(result, list):
                            file_list.extend(result)
                        else:
                            file_list.append(result)
                elif hasattr(adapter, 'list_files'):
                    # 检查是否为异步方法
                    import inspect
                    if inspect.iscoroutinefunction(adapter.list_files):
                        # 异步适配器 - 使用超时和限制
                        try:
                            loop = asyncio.get_event_loop()
                        except RuntimeError:
                            loop = asyncio.new_event_loop()
                            asyncio.set_event_loop(loop)

                        try:
                            file_list = []
                            async def collect_files():
                                # 限制文件数量，避免卡死
                                result = await adapter.list_files(path, max_keys=1000)
                                # 检查返回结果的类型
                                if hasattr(result, 'files'):
                                    # 如果是ListResult对象，提取files属性
                                    files_data = result.files
                                    if hasattr(files_data, '__iter__'):
                                        for file_meta in files_data:
                                            file_list.append(file_meta)
                                            if len(file_list) >= 1000:  # 限制数量
                                                break
                                    else:
                                        file_list.append(files_data)
                                elif hasattr(result, '__aiter__'):
                                    # 如果是异步迭代器
                                    count = 0
                                    async for file_meta in result:
                                        file_list.append(file_meta)
                                        count += 1
                                        if count >= 1000:  # 限制数量
                                            break
                                elif hasattr(result, '__iter__') and not isinstance(result, str):
                                    # 如果是普通迭代器（排除字符串）
                                    count = 0
                                    for file_meta in result:
                                        file_list.append(file_meta)
                                        count += 1
                                        if count >= 1000:  # 限制数量
                                            break
                                else:
                                    # 如果是单个结果或列表
                                    if isinstance(result, list):
                                        file_list.extend(result[:1000])  # 限制数量
                                    else:
                                        file_list.append(result)

                            # 设置60秒超时
                            task = asyncio.wait_for(collect_files(), timeout=60.0)
                            if loop.is_running():
                                # 如果循环正在运行，使用线程池
                                import concurrent.futures
                                with concurrent.futures.ThreadPoolExecutor() as executor:
                                    future = executor.submit(asyncio.run, task)
                                    future.result(timeout=60)
                            else:
                                loop.run_until_complete(task)
                        except asyncio.TimeoutError:
                            self.logger.warning(f"列出文件超时: {path}")
                            return {'success': False, 'message': '列出文件超时，请尝试更具体的路径'}
                    else:
                        # 同步方法，直接调用
                        result = adapter.list_files(path)
                        file_list = []
                        if hasattr(result, 'files'):
                            # 如果是ListResult对象，提取files属性
                            files_data = result.files
                            if hasattr(files_data, '__iter__'):
                                for file_meta in files_data:
                                    file_list.append(file_meta)
                            else:
                                file_list.append(files_data)
                        elif hasattr(result, '__iter__') and not isinstance(result, str):
                            # 如果是迭代器（排除字符串）
                            for file_meta in result:
                                file_list.append(file_meta)
                        else:
                            # 如果是单个结果或列表
                            if isinstance(result, list):
                                file_list.extend(result)
                            else:
                                file_list.append(result)
                else:
                    return {'success': False, 'message': '存储适配器不支持文件列表功能'}
            except Exception as e:
                error_msg = f"列出文件失败: {str(e)}"
                self.logger.error(error_msg)
                return {'success': False, 'message': error_msg}
            
            # 处理文件列表，构建树形结构
            current_path_parts = path.strip('/').split('/') if path.strip('/') else []
            current_depth = len(current_path_parts)
            
            for file_meta in file_list:
                try:
                    # 安全地获取文件路径
                    file_path = getattr(file_meta, 'key', '') or getattr(file_meta, 'path', '') or str(file_meta)
                    if not file_path:
                        continue

                    file_parts = file_path.strip('/').split('/')

                    # 只显示当前路径下的直接子项
                    if len(file_parts) > current_depth:
                        # 构建相对路径
                        if current_depth == 0:
                            relative_parts = file_parts
                        else:
                            # 检查是否在当前路径下
                            if file_parts[:current_depth] == current_path_parts:
                                relative_parts = file_parts[current_depth:]
                            else:
                                continue

                        if len(relative_parts) > 1:
                            # 这是一个子目录中的文件，添加目录
                            dir_name = relative_parts[0]
                            if dir_name not in directories:
                                directories.add(dir_name)
                        else:
                            # 这是当前目录下的文件
                            file_info = {
                                'name': relative_parts[0],
                                'type': 'file',
                                'size': getattr(file_meta, 'size', 0),
                                'last_modified': self._safe_format_datetime(getattr(file_meta, 'last_modified', None)),
                                'path': file_path,
                                'mime_type': mimetypes.guess_type(relative_parts[0])[0] or 'application/octet-stream'
                            }
                            files.append(file_info)
                except Exception as e:
                    self.logger.warning(f"处理文件元数据失败: {e}, file_meta: {file_meta}")
                    continue
            
            # 添加目录到结果
            for dir_name in directories:
                dir_path = '/'.join(current_path_parts + [dir_name]) if current_path_parts else dir_name
                files.append({
                    'name': dir_name,
                    'type': 'directory',
                    'size': 0,
                    'last_modified': None,
                    'path': dir_path,
                    'mime_type': 'inode/directory'
                })
            
            # 排序：目录在前，文件在后，按名称排序
            files.sort(key=lambda x: (x['type'] != 'directory', x['name'].lower()))
            
            # 断开连接
            if hasattr(adapter, 'disconnect_sync'):
                adapter.disconnect_sync()
            elif hasattr(adapter, 'disconnect'):
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    loop.run_until_complete(adapter.disconnect())
                finally:
                    loop.close()
            
            return {
                'success': True,
                'files': files,
                'current_path': path,
                'parent_path': '/'.join(current_path_parts[:-1]) if current_path_parts else None,
                'storage_name': storage_config.name,
                'storage_type': storage_config.storage_type.value
            }
            
        except Exception as e:
            self.logger.error(f"浏览存储失败: {e}")
            return {'success': False, 'message': f'浏览存储失败: {str(e)}'}
    
    def download_files(self, storage_id: str, storage_type: str, file_paths: List[str], 
                      download_name: str = None) -> Dict[str, Any]:
        """打包下载文件"""
        try:
            # 获取存储配置
            if storage_type == 'source':
                storage_config = self.config_manager.get_source(storage_id)
            else:
                storage_config = self.config_manager.get_target(storage_id)
            
            if not storage_config:
                return {'success': False, 'message': '存储配置不存在'}
            
            # 创建存储适配器
            adapter = StorageFactory.create_adapter(storage_config)
            
            # 连接存储
            if hasattr(adapter, 'connect_sync'):
                if not adapter.connect_sync():
                    return {'success': False, 'message': '连接存储失败'}
            elif hasattr(adapter, 'connect'):
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    if not loop.run_until_complete(adapter.connect()):
                        return {'success': False, 'message': '连接存储失败'}
                finally:
                    loop.close()
            
            # 创建临时ZIP文件
            temp_dir = tempfile.mkdtemp()
            zip_filename = download_name or f"download_{datetime.now().strftime('%Y%m%d_%H%M%S')}.zip"
            zip_path = os.path.join(temp_dir, zip_filename)
            
            downloaded_count = 0
            failed_count = 0
            
            with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for file_path in file_paths:
                    try:
                        # 创建临时文件用于下载
                        temp_file = tempfile.NamedTemporaryFile(delete=False)
                        temp_file.close()
                        
                        # 下载文件
                        if hasattr(adapter, 'download_file_sync'):
                            success = adapter.download_file_sync(file_path, temp_file.name)
                        elif hasattr(adapter, 'download_file'):
                            loop = asyncio.new_event_loop()
                            asyncio.set_event_loop(loop)
                            try:
                                success = loop.run_until_complete(
                                    adapter.download_file(file_path, temp_file.name)
                                )
                            finally:
                                loop.close()
                        else:
                            success = False
                        
                        if success and os.path.exists(temp_file.name):
                            # 添加到ZIP文件
                            arcname = file_path.lstrip('/')
                            zipf.write(temp_file.name, arcname)
                            downloaded_count += 1
                            self.logger.info(f"已添加到ZIP: {file_path}")
                        else:
                            failed_count += 1
                            self.logger.warning(f"下载文件失败: {file_path}")
                        
                        # 清理临时文件
                        try:
                            os.unlink(temp_file.name)
                        except:
                            pass
                            
                    except Exception as e:
                        failed_count += 1
                        self.logger.error(f"处理文件失败 {file_path}: {e}")
            
            # 断开连接
            if hasattr(adapter, 'disconnect_sync'):
                adapter.disconnect_sync()
            elif hasattr(adapter, 'disconnect'):
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    loop.run_until_complete(adapter.disconnect())
                finally:
                    loop.close()
            
            if downloaded_count == 0:
                # 清理空的ZIP文件
                try:
                    os.unlink(zip_path)
                    os.rmdir(temp_dir)
                except:
                    pass
                return {'success': False, 'message': '没有成功下载任何文件'}
            
            # 获取ZIP文件大小
            zip_size = os.path.getsize(zip_path)
            
            return {
                'success': True,
                'zip_path': zip_path,
                'zip_filename': zip_filename,
                'zip_size': zip_size,
                'downloaded_count': downloaded_count,
                'failed_count': failed_count,
                'temp_dir': temp_dir
            }
            
        except Exception as e:
            self.logger.error(f"打包下载失败: {e}")
            return {'success': False, 'message': f'打包下载失败: {str(e)}'}
    
    def get_file_info(self, storage_id: str, storage_type: str, file_path: str) -> Dict[str, Any]:
        """获取单个文件的详细信息"""
        try:
            # 获取存储配置
            if storage_type == 'source':
                storage_config = self.config_manager.get_source(storage_id)
            else:
                storage_config = self.config_manager.get_target(storage_id)
            
            if not storage_config:
                return {'success': False, 'message': '存储配置不存在'}
            
            # 创建存储适配器
            adapter = StorageFactory.create_adapter(storage_config)
            
            # 连接存储
            if hasattr(adapter, 'connect_sync'):
                if not adapter.connect_sync():
                    return {'success': False, 'message': '连接存储失败'}
            elif hasattr(adapter, 'connect'):
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    if not loop.run_until_complete(adapter.connect()):
                        return {'success': False, 'message': '连接存储失败'}
                finally:
                    loop.close()
            
            # 获取文件元数据
            if hasattr(adapter, 'get_file_metadata_sync'):
                file_meta = adapter.get_file_metadata_sync(file_path)
            elif hasattr(adapter, 'get_file_metadata'):
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    file_meta = loop.run_until_complete(adapter.get_file_metadata(file_path))
                finally:
                    loop.close()
            else:
                return {'success': False, 'message': '存储适配器不支持文件元数据功能'}
            
            # 断开连接
            if hasattr(adapter, 'disconnect_sync'):
                adapter.disconnect_sync()
            elif hasattr(adapter, 'disconnect'):
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    loop.run_until_complete(adapter.disconnect())
                finally:
                    loop.close()
            
            if not file_meta:
                return {'success': False, 'message': '文件不存在'}
            
            return {
                'success': True,
                'file_info': {
                    'path': file_meta.key,
                    'size': file_meta.size,
                    'last_modified': file_meta.last_modified,
                    'etag': file_meta.etag,
                    'mime_type': mimetypes.guess_type(file_path)[0] or 'application/octet-stream'
                }
            }
            
        except Exception as e:
            self.logger.error(f"获取文件信息失败: {e}")
            return {'success': False, 'message': f'获取文件信息失败: {str(e)}'}


# 创建全局实例
file_browser_manager = None

def get_file_browser_manager(config_manager):
    """获取文件浏览器管理器实例"""
    global file_browser_manager
    if file_browser_manager is None:
        file_browser_manager = FileBrowserManager(config_manager)
    return file_browser_manager
