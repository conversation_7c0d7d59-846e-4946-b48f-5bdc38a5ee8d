#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试使用全新适配器实例的下载方法
"""

import smb_storage_adapter
from storage_abstraction import StorageFactory, SMBStorageConfig, StorageType

def test_fresh_adapter_download():
    """测试使用全新适配器实例的下载方法"""
    print("🧪 测试使用全新适配器实例的下载方法")
    
    config = SMBStorageConfig(
        storage_type=StorageType.SMB,
        hostname='Jayce',
        port=445,
        username='smb',
        password='smbsmb',
        domain='WORKGROUP',
        share_name='hlmj',
        root_path=''
    )
    
    adapter = StorageFactory.create_adapter(config)
    
    # 模拟生产环境的使用模式
    print("📋 模拟生产环境使用模式:")
    
    # 1. 先调用 list_files（这会改变连接状态）
    print("1. 调用 list_files...")
    try:
        result = adapter.list_files("", max_keys=3)
        print(f"   ✅ 成功: 找到 {len(result.files)} 个文件")
        
        if len(result.files) >= 3:
            test_files = result.files[:3]
            print("   文件列表:")
            for i, f in enumerate(test_files):
                print(f"     {i+1}. {f.key} ({f.size} bytes)")
        else:
            print("   ❌ 文件数量不足")
            return False
            
    except Exception as e:
        print(f"   ❌ 失败: {e}")
        return False
    
    # 2. 然后调用 get_file（使用全新适配器实例）
    print("\n2. 使用全新适配器实例下载文件...")
    success_count = 0
    
    for i, file_meta in enumerate(test_files):
        print(f"   下载 {i+1}/{len(test_files)}: {file_meta.key}")
        try:
            data = adapter.get_file(file_meta.key)
            if data:
                print(f"     ✅ 成功: {len(data)} bytes")
                success_count += 1
                
                # 验证数据完整性
                if len(data) == file_meta.size:
                    print(f"     ✅ 大小匹配: {len(data)} == {file_meta.size}")
                else:
                    print(f"     ⚠️ 大小不匹配: {len(data)} != {file_meta.size}")
            else:
                print(f"     ❌ 失败: 返回None")
        except Exception as e:
            print(f"     ❌ 异常: {e}")
    
    print(f"\n📊 下载结果: {success_count}/{len(test_files)} 成功")
    
    # 3. 再次调用 list_files 验证连接状态未被破坏
    print("\n3. 再次调用 list_files 验证连接状态...")
    try:
        result2 = adapter.list_files("", max_keys=2)
        print(f"   ✅ 成功: 找到 {len(result2.files)} 个文件")
    except Exception as e:
        print(f"   ❌ 失败: {e}")
        return False
    
    # 4. 再次下载验证稳定性
    print("\n4. 再次下载验证稳定性...")
    success_count2 = 0
    
    for i, file_meta in enumerate(test_files[:2]):  # 只测试前2个文件
        print(f"   下载 {i+1}/2: {file_meta.key}")
        try:
            data = adapter.get_file(file_meta.key)
            if data:
                print(f"     ✅ 成功: {len(data)} bytes")
                success_count2 += 1
            else:
                print(f"     ❌ 失败: 返回None")
        except Exception as e:
            print(f"     ❌ 异常: {e}")
    
    print(f"\n📊 第二次下载结果: {success_count2}/2 成功")
    
    # 评估结果
    total_expected = len(test_files) + 2
    total_success = success_count + success_count2
    success_rate = total_success / total_expected
    
    print(f"\n🎯 总体结果:")
    print(f"   成功率: {total_success}/{total_expected} ({success_rate:.1%})")
    
    if success_rate >= 0.8:
        print("   ✅ 全新适配器下载方法工作正常")
        return True
    else:
        print("   ❌ 全新适配器下载方法仍有问题")
        return False

def main():
    """主函数"""
    print("🧪 全新适配器实例下载方法测试")
    print("=" * 60)
    
    try:
        if test_fresh_adapter_download():
            print("🎉 全新适配器实例下载方法工作正常！")
            print("")
            print("🚀 现在可以重新运行同步任务，应该能够:")
            print("  ✅ 在调用 list_files 后成功下载文件")
            print("  ✅ 完全避免连接状态污染问题")
            print("  ✅ 消除 STATUS_OBJECT_NAME_INVALID 错误")
            print("  ✅ 每次下载使用独立的连接状态")
            
            return 0
        else:
            print("❌ 全新适配器实例下载方法仍有问题")
            return 1
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit(main())
