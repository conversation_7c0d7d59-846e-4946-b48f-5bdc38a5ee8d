#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
集成高性能SFTP适配器到现有系统
"""

import sys
import os
import shutil
import logging
from pathlib import Path

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def backup_original_adapter():
    """备份原始SFTP适配器"""
    logger = logging.getLogger(__name__)
    
    original_file = "sftp_storage_adapter.py"
    backup_file = "sftp_storage_adapter_backup.py"
    
    if os.path.exists(original_file):
        shutil.copy2(original_file, backup_file)
        logger.info(f"已备份原始适配器: {original_file} -> {backup_file}")
        return True
    else:
        logger.warning(f"原始适配器文件不存在: {original_file}")
        return False

def install_dependencies():
    """安装必要的依赖"""
    logger = logging.getLogger(__name__)
    
    dependencies = [
        "asyncssh>=2.14.0",
        "aiofiles>=23.0.0",
    ]
    
    logger.info("安装高性能SFTP依赖...")
    
    for dep in dependencies:
        try:
            import subprocess
            result = subprocess.run([
                sys.executable, "-m", "pip", "install", dep
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.info(f"✅ 成功安装: {dep}")
            else:
                logger.error(f"❌ 安装失败: {dep}")
                logger.error(f"错误信息: {result.stderr}")
                return False
        except Exception as e:
            logger.error(f"安装依赖异常 {dep}: {e}")
            return False
    
    return True

def update_storage_abstraction():
    """更新存储抽象层以支持高性能SFTP"""
    logger = logging.getLogger(__name__)
    
    try:
        # 读取现有的storage_abstraction.py
        with open("storage_abstraction.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查是否已经包含高性能SFTP支持
        if "HighPerformanceSFTPAdapter" in content:
            logger.info("存储抽象层已包含高性能SFTP支持")
            return True
        
        # 添加高性能SFTP适配器导入和注册
        import_section = """
# 高性能SFTP适配器
try:
    from high_performance_sftp_adapter import HighPerformanceSFTPAdapter, HighPerformanceSFTPConfig
    ASYNCSSH_AVAILABLE = True
    print("✅ AsyncSSH高性能SFTP适配器可用")
except ImportError as e:
    ASYNCSSH_AVAILABLE = False
    print(f"⚠️ AsyncSSH高性能SFTP适配器不可用: {e}")
"""
        
        registration_section = """
# 注册高性能SFTP适配器
if ASYNCSSH_AVAILABLE:
    StorageFactory.register_adapter(StorageType.SFTP_HIGH_PERFORMANCE, HighPerformanceSFTPAdapter)
"""
        
        # 在适当位置插入代码
        lines = content.split('\n')
        
        # 找到导入部分的结束位置
        import_end_idx = 0
        for i, line in enumerate(lines):
            if line.startswith('from') or line.startswith('import'):
                import_end_idx = i
        
        # 插入导入
        lines.insert(import_end_idx + 1, import_section)
        
        # 找到注册部分
        for i, line in enumerate(lines):
            if "StorageFactory.register_adapter" in line and "SFTP" in line:
                lines.insert(i + 1, registration_section)
                break
        
        # 写回文件
        with open("storage_abstraction.py", "w", encoding="utf-8") as f:
            f.write('\n'.join(lines))
        
        logger.info("✅ 已更新存储抽象层以支持高性能SFTP")
        return True
        
    except Exception as e:
        logger.error(f"更新存储抽象层失败: {e}")
        return False

def update_storage_type_enum():
    """更新StorageType枚举"""
    logger = logging.getLogger(__name__)
    
    try:
        # 读取storage_abstraction.py
        with open("storage_abstraction.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查是否已经包含高性能SFTP类型
        if "SFTP_HIGH_PERFORMANCE" in content:
            logger.info("StorageType枚举已包含高性能SFTP类型")
            return True
        
        # 添加新的存储类型
        if "class StorageType(Enum):" in content:
            content = content.replace(
                'SFTP = "sftp"',
                'SFTP = "sftp"\n    SFTP_HIGH_PERFORMANCE = "sftp_hp"'
            )
            
            with open("storage_abstraction.py", "w", encoding="utf-8") as f:
                f.write(content)
            
            logger.info("✅ 已添加高性能SFTP存储类型")
            return True
        else:
            logger.error("未找到StorageType枚举定义")
            return False
            
    except Exception as e:
        logger.error(f"更新StorageType枚举失败: {e}")
        return False

def create_migration_script():
    """创建迁移脚本"""
    logger = logging.getLogger(__name__)
    
    migration_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SFTP适配器迁移脚本
将现有的SFTP配置迁移到高性能版本
"""

import json
import logging
from unified_config_manager import UnifiedConfigManager
from high_performance_sftp_adapter import SFTPPerformanceOptimizer

def migrate_sftp_configs():
    """迁移SFTP配置"""
    logger = logging.getLogger(__name__)
    
    try:
        config_manager = UnifiedConfigManager()
        
        # 获取所有数据源
        sources = config_manager.get_all_sources()
        targets = config_manager.get_all_targets()
        
        migrated_count = 0
        
        # 迁移数据源
        for source_id, source_config in sources.items():
            if source_config.get('type') == 'sftp':
                logger.info(f"迁移数据源: {source_config.get('name', source_id)}")
                
                # 创建高性能配置
                hp_config = SFTPPerformanceOptimizer.create_optimized_config(
                    hostname=source_config.get('hostname', ''),
                    username=source_config.get('username', ''),
                    password=source_config.get('password', ''),
                    private_key_path=source_config.get('private_key_path', ''),
                    performance_profile='balanced'
                )
                
                # 更新配置类型
                source_config['type'] = 'sftp_hp'
                source_config['hp_config'] = hp_config.__dict__
                
                config_manager.update_source(source_id, source_config)
                migrated_count += 1
        
        # 迁移目标存储
        for target_id, target_config in targets.items():
            if target_config.get('type') == 'sftp':
                logger.info(f"迁移目标存储: {target_config.get('name', target_id)}")
                
                # 创建高性能配置
                hp_config = SFTPPerformanceOptimizer.create_optimized_config(
                    hostname=target_config.get('hostname', ''),
                    username=target_config.get('username', ''),
                    password=target_config.get('password', ''),
                    private_key_path=target_config.get('private_key_path', ''),
                    performance_profile='balanced'
                )
                
                # 更新配置类型
                target_config['type'] = 'sftp_hp'
                target_config['hp_config'] = hp_config.__dict__
                
                config_manager.update_target(target_id, target_config)
                migrated_count += 1
        
        logger.info(f"✅ 迁移完成，共迁移 {migrated_count} 个SFTP配置")
        return True
        
    except Exception as e:
        logger.error(f"迁移失败: {e}")
        return False

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    migrate_sftp_configs()
'''
    
    try:
        with open("migrate_sftp_configs.py", "w", encoding="utf-8") as f:
            f.write(migration_script)
        
        logger.info("✅ 已创建迁移脚本: migrate_sftp_configs.py")
        return True
        
    except Exception as e:
        logger.error(f"创建迁移脚本失败: {e}")
        return False

def create_performance_test_script():
    """创建性能测试脚本"""
    logger = logging.getLogger(__name__)
    
    test_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高性能SFTP适配器性能测试
"""

import asyncio
import time
import logging
from high_performance_sftp_adapter import HighPerformanceSFTPAdapter, SFTPPerformanceOptimizer

async def test_sftp_performance():
    """测试SFTP性能"""
    logger = logging.getLogger(__name__)
    
    # 创建测试配置
    config = SFTPPerformanceOptimizer.create_optimized_config(
        hostname="your-sftp-server.com",
        username="testuser",
        password="testpass",
        performance_profile="high_performance"
    )
    
    adapter = HighPerformanceSFTPAdapter(config)
    
    try:
        # 连接测试
        logger.info("🔗 测试连接...")
        start_time = time.time()
        
        if await adapter.connect():
            connect_time = time.time() - start_time
            logger.info(f"✅ 连接成功，耗时: {connect_time:.2f}秒")
            
            # 获取连接统计
            stats = await adapter.get_connection_stats()
            logger.info(f"📊 连接统计: {stats}")
            
            # 文件列表测试
            logger.info("📂 测试文件列表...")
            start_time = time.time()
            
            file_count = 0
            async for file_meta in adapter.list_files():
                file_count += 1
                if file_count >= 100:  # 限制测试文件数量
                    break
            
            list_time = time.time() - start_time
            logger.info(f"✅ 列出 {file_count} 个文件，耗时: {list_time:.2f}秒")
            
            # 性能指标
            if file_count > 0:
                files_per_second = file_count / list_time
                logger.info(f"📈 文件列表性能: {files_per_second:.1f} 文件/秒")
            
        else:
            logger.error("❌ 连接失败")
            
    except Exception as e:
        logger.error(f"测试异常: {e}")
    finally:
        await adapter.disconnect()

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    asyncio.run(test_sftp_performance())
'''
    
    try:
        with open("test_sftp_performance.py", "w", encoding="utf-8") as f:
            f.write(test_script)
        
        logger.info("✅ 已创建性能测试脚本: test_sftp_performance.py")
        return True
        
    except Exception as e:
        logger.error(f"创建性能测试脚本失败: {e}")
        return False

def main():
    """主函数"""
    logger = setup_logging()
    
    logger.info("🚀 开始集成高性能SFTP适配器")
    
    steps = [
        ("备份原始适配器", backup_original_adapter),
        ("安装依赖", install_dependencies),
        ("更新StorageType枚举", update_storage_type_enum),
        ("更新存储抽象层", update_storage_abstraction),
        ("创建迁移脚本", create_migration_script),
        ("创建性能测试脚本", create_performance_test_script),
    ]
    
    success_count = 0
    
    for step_name, step_func in steps:
        logger.info(f"📋 执行步骤: {step_name}")
        
        try:
            if step_func():
                logger.info(f"✅ {step_name} 完成")
                success_count += 1
            else:
                logger.error(f"❌ {step_name} 失败")
        except Exception as e:
            logger.error(f"❌ {step_name} 异常: {e}")
    
    logger.info(f"📊 集成结果: {success_count}/{len(steps)} 步骤成功")
    
    if success_count == len(steps):
        logger.info("🎉 高性能SFTP适配器集成完成！")
        logger.info("")
        logger.info("📋 后续步骤:")
        logger.info("1. 运行 python migrate_sftp_configs.py 迁移现有配置")
        logger.info("2. 运行 python test_sftp_performance.py 测试性能")
        logger.info("3. 重启 lightrek.py 应用新的适配器")
        logger.info("")
        logger.info("🔧 性能优化建议:")
        logger.info("- 根据网络条件调整性能配置文件")
        logger.info("- 监控连接池使用情况")
        logger.info("- 启用统计缓存以提高重复操作性能")
    else:
        logger.error("❌ 集成过程中出现错误，请检查日志")

if __name__ == "__main__":
    main()
