#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NAS存储适配器 - 支持NFS和SMB/CIFS协议的网络附加存储
"""

import os
import shutil
import logging
from typing import List, Tuple, Optional, Dict, Any
from datetime import datetime
import subprocess
import tempfile
import platform

from storage_abstraction import StorageAdapter, FileMetadata


class NASStorageAdapter(StorageAdapter):
    """NAS存储适配器"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.protocol = config.get('protocol', 'smb').lower()  # nfs 或 smb
        self.server = config.get('server', '')
        self.share_path = config.get('share_path', '')
        self.username = config.get('username', '')
        self.password = config.get('password', '')
        self.domain = config.get('domain', '')
        self.mount_point = config.get('mount_point', '')
        self.auto_mount = config.get('auto_mount', True)
        
        # 如果没有指定挂载点，使用临时目录
        if not self.mount_point:
            self.mount_point = os.path.join(tempfile.gettempdir(), f'lightrek_nas_{self.server.replace(".", "_")}')
        
        self.is_mounted = False
        self.logger = logging.getLogger(__name__)
        
        # 确保挂载点目录存在
        os.makedirs(self.mount_point, exist_ok=True)
    
    def test_connection(self) -> Tuple[bool, str]:
        """测试NAS连接"""
        try:
            if self.auto_mount:
                success, message = self._mount_share()
                if not success:
                    return False, f"挂载失败: {message}"
            
            # 测试访问挂载点
            if not os.path.exists(self.mount_point):
                return False, f"挂载点不存在: {self.mount_point}"
            
            if not os.access(self.mount_point, os.R_OK):
                return False, f"挂载点不可读: {self.mount_point}"
            
            # 尝试列出目录内容
            try:
                os.listdir(self.mount_point)
                return True, f"NAS连接成功 ({self.protocol.upper()}): {self.server}{self.share_path}"
            except PermissionError:
                return False, f"权限不足，无法访问: {self.mount_point}"
            except Exception as e:
                return False, f"访问挂载点失败: {str(e)}"
                
        except Exception as e:
            return False, f"NAS连接测试失败: {str(e)}"
    
    def _mount_share(self) -> Tuple[bool, str]:
        """挂载网络共享"""
        if self.is_mounted:
            return True, "已挂载"
        
        try:
            system = platform.system().lower()
            
            if self.protocol == 'smb':
                return self._mount_smb(system)
            elif self.protocol == 'nfs':
                return self._mount_nfs(system)
            else:
                return False, f"不支持的协议: {self.protocol}"
                
        except Exception as e:
            return False, f"挂载失败: {str(e)}"
    
    def _mount_smb(self, system: str) -> Tuple[bool, str]:
        """挂载SMB/CIFS共享"""
        share_url = f"//{self.server}{self.share_path}"
        
        if system == 'darwin':  # macOS
            cmd = ['mount', '-t', 'smbfs']
            if self.username and self.password:
                if self.domain:
                    auth_url = f"smb://{self.domain};{self.username}:{self.password}@{self.server}{self.share_path}"
                else:
                    auth_url = f"smb://{self.username}:{self.password}@{self.server}{self.share_path}"
            else:
                auth_url = f"smb://{self.server}{self.share_path}"
            cmd.extend([auth_url, self.mount_point])
            
        elif system == 'linux':
            cmd = ['mount', '-t', 'cifs', share_url, self.mount_point]
            if self.username:
                cmd.extend(['-o', f'username={self.username}'])
                if self.password:
                    cmd.extend(['-o', f'password={self.password}'])
                if self.domain:
                    cmd.extend(['-o', f'domain={self.domain}'])
            else:
                cmd.extend(['-o', 'guest'])
                
        elif system == 'windows':
            # Windows使用net use命令
            drive_letter = self.mount_point if len(self.mount_point) == 2 and self.mount_point[1] == ':' else None
            if not drive_letter:
                return False, "Windows系统需要指定驱动器号作为挂载点 (如: Z:)"
            
            cmd = ['net', 'use', drive_letter, share_url]
            if self.username and self.password:
                cmd.extend([f'/user:{self.domain}\\{self.username}' if self.domain else f'/user:{self.username}', self.password])
        else:
            return False, f"不支持的操作系统: {system}"
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            if result.returncode == 0:
                self.is_mounted = True
                return True, f"SMB挂载成功: {share_url} -> {self.mount_point}"
            else:
                return False, f"SMB挂载失败: {result.stderr}"
        except subprocess.TimeoutExpired:
            return False, "SMB挂载超时"
        except Exception as e:
            return False, f"SMB挂载异常: {str(e)}"
    
    def _mount_nfs(self, system: str) -> Tuple[bool, str]:
        """挂载NFS共享"""
        nfs_url = f"{self.server}:{self.share_path}"
        
        if system in ['darwin', 'linux']:
            cmd = ['mount', '-t', 'nfs', nfs_url, self.mount_point]
        elif system == 'windows':
            return False, "Windows系统不直接支持NFS挂载，请使用SMB协议"
        else:
            return False, f"不支持的操作系统: {system}"
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            if result.returncode == 0:
                self.is_mounted = True
                return True, f"NFS挂载成功: {nfs_url} -> {self.mount_point}"
            else:
                return False, f"NFS挂载失败: {result.stderr}"
        except subprocess.TimeoutExpired:
            return False, "NFS挂载超时"
        except Exception as e:
            return False, f"NFS挂载异常: {str(e)}"
    
    def _unmount_share(self) -> bool:
        """卸载网络共享"""
        if not self.is_mounted:
            return True
        
        try:
            system = platform.system().lower()
            
            if system == 'windows':
                if len(self.mount_point) == 2 and self.mount_point[1] == ':':
                    cmd = ['net', 'use', self.mount_point, '/delete']
                else:
                    return True  # Windows下非驱动器挂载点无需卸载
            else:
                cmd = ['umount', self.mount_point]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            self.is_mounted = False
            return result.returncode == 0
            
        except Exception as e:
            self.logger.warning(f"卸载网络共享失败: {str(e)}")
            return False
    
    def list_files(self, prefix: str = "", limit: int = 1000, 
                   continuation_token: str = None) -> Tuple[List[FileMetadata], Optional[str]]:
        """列出文件"""
        try:
            if self.auto_mount and not self.is_mounted:
                success, message = self._mount_share()
                if not success:
                    raise Exception(f"挂载失败: {message}")
            
            search_path = os.path.join(self.mount_point, prefix.lstrip('/'))
            if not os.path.exists(search_path):
                return [], None
            
            files = []
            count = 0
            
            for root, dirs, filenames in os.walk(search_path):
                for filename in filenames:
                    if count >= limit:
                        # 返回continuation_token用于分页
                        return files, f"{root}:{filename}"
                    
                    file_path = os.path.join(root, filename)
                    relative_path = os.path.relpath(file_path, self.mount_point)
                    
                    try:
                        stat = os.stat(file_path)
                        file_meta = FileMetadata(
                            key=relative_path.replace('\\', '/'),  # 统一使用正斜杠
                            size=stat.st_size,
                            last_modified=datetime.fromtimestamp(stat.st_mtime),
                            etag=f"{stat.st_size}-{int(stat.st_mtime)}"  # 简单的etag
                        )
                        files.append(file_meta)
                        count += 1
                    except (OSError, IOError) as e:
                        self.logger.warning(f"无法获取文件信息: {file_path} - {e}")
                        continue
            
            return files, None
            
        except Exception as e:
            self.logger.error(f"列出文件失败: {str(e)}")
            raise
    
    def download_file(self, key: str) -> bytes:
        """下载文件"""
        try:
            if self.auto_mount and not self.is_mounted:
                success, message = self._mount_share()
                if not success:
                    raise Exception(f"挂载失败: {message}")
            
            file_path = os.path.join(self.mount_point, key.lstrip('/'))
            
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"文件不存在: {key}")
            
            with open(file_path, 'rb') as f:
                return f.read()
                
        except Exception as e:
            self.logger.error(f"下载文件失败 {key}: {str(e)}")
            raise
    
    def upload_file(self, key: str, data: bytes) -> bool:
        """上传文件"""
        try:
            if self.auto_mount and not self.is_mounted:
                success, message = self._mount_share()
                if not success:
                    raise Exception(f"挂载失败: {message}")
            
            file_path = os.path.join(self.mount_point, key.lstrip('/'))
            
            # 确保目录存在
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            
            with open(file_path, 'wb') as f:
                f.write(data)
            
            return True
            
        except Exception as e:
            self.logger.error(f"上传文件失败 {key}: {str(e)}")
            return False
    
    def delete_file(self, key: str) -> bool:
        """删除文件"""
        try:
            if self.auto_mount and not self.is_mounted:
                success, message = self._mount_share()
                if not success:
                    raise Exception(f"挂载失败: {message}")
            
            file_path = os.path.join(self.mount_point, key.lstrip('/'))
            
            if os.path.exists(file_path):
                os.remove(file_path)
                return True
            else:
                self.logger.warning(f"文件不存在，无法删除: {key}")
                return False
                
        except Exception as e:
            self.logger.error(f"删除文件失败 {key}: {str(e)}")
            return False
    
    def __del__(self):
        """析构函数，自动卸载"""
        if hasattr(self, 'auto_mount') and self.auto_mount:
            self._unmount_share()
