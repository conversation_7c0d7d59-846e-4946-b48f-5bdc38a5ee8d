"""
S3存储适配器 - 基于新的存储抽象层重构
"""

from storage_abstraction import StorageAdapter, S3StorageConfig, FileMetadata, ListResult, StorageType, StorageFactory
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from urllib.parse import urlparse
import urllib.parse
import urllib.request
import urllib.error
import hashlib
import hmac
import base64
import time
import xml.etree.ElementTree as ET
import re


class S3StorageAdapter(StorageAdapter):
    """S3存储适配器"""
    
    def __init__(self, config: S3StorageConfig):
        super().__init__(config)
        self.config: S3StorageConfig = config
    
    def _get_host_and_path(self, key: str = "") -> Tuple[str, str]:
        """获取主机和路径"""
        parsed = urlparse(self.config.endpoint)
        host = parsed.netloc
        
        # 对key进行URL编码，但保留路径分隔符
        encoded_key = urllib.parse.quote(key, safe='/') if key else ""
        
        if 'aliyuncs.com' in host:
            if self.config.bucket:
                host = f"{self.config.bucket}.{host}"
                path = f"/{encoded_key}" if encoded_key else "/"
            else:
                path = f"/{encoded_key}" if encoded_key else "/"
        else:
            if self.config.bucket:
                path = f"/{self.config.bucket}/{encoded_key}" if encoded_key else f"/{self.config.bucket}/"
            else:
                path = f"/{encoded_key}" if encoded_key else "/"
        
        return host, path
    
    def _aws_signature_v4(self, method: str, path: str, query_params: Dict[str, Any], 
                         headers: Dict[str, str], data: bytes = None) -> Dict[str, str]:
        """AWS Signature V4签名"""
        # 获取当前时间
        t = datetime.now(datetime.timezone.utc)
        amz_date = t.strftime('%Y%m%dT%H%M%SZ')
        date_stamp = t.strftime('%Y%m%d')
        
        # 添加必要的头部
        headers = headers.copy()
        headers['Host'] = self._get_host_and_path()[0]
        headers['X-Amz-Date'] = amz_date
        
        if data:
            headers['X-Amz-Content-Sha256'] = hashlib.sha256(data).hexdigest()
        else:
            headers['X-Amz-Content-Sha256'] = hashlib.sha256(b'').hexdigest()
        
        # 创建规范请求
        canonical_headers = '\n'.join([f"{k.lower()}:{v}" for k, v in sorted(headers.items())]) + '\n'
        signed_headers = ';'.join([k.lower() for k in sorted(headers.keys())])
        
        query_string = '&'.join([f"{k}={urllib.parse.quote_plus(str(v))}" for k, v in sorted(query_params.items())])
        
        canonical_request = f"{method}\n{path}\n{query_string}\n{canonical_headers}\n{signed_headers}\n{headers['X-Amz-Content-Sha256']}"
        
        # 创建签名字符串
        algorithm = 'AWS4-HMAC-SHA256'
        credential_scope = f"{date_stamp}/{self.config.region}/s3/aws4_request"
        string_to_sign = f"{algorithm}\n{amz_date}\n{credential_scope}\n{hashlib.sha256(canonical_request.encode()).hexdigest()}"
        
        # 计算签名
        def sign(key, msg):
            return hmac.new(key, msg.encode('utf-8'), hashlib.sha256).digest()
        
        def get_signature_key(key, date_stamp, region_name, service_name):
            k_date = sign(('AWS4' + key).encode('utf-8'), date_stamp)
            k_region = sign(k_date, region_name)
            k_service = sign(k_region, service_name)
            k_signing = sign(k_service, 'aws4_request')
            return k_signing
        
        signing_key = get_signature_key(self.config.secret_key, date_stamp, self.config.region, 's3')
        signature = hmac.new(signing_key, string_to_sign.encode('utf-8'), hashlib.sha256).hexdigest()
        
        # 添加授权头部
        authorization_header = f"{algorithm} Credential={self.config.access_key}/{credential_scope}, SignedHeaders={signed_headers}, Signature={signature}"
        headers['Authorization'] = authorization_header
        
        return headers
    
    def _aliyun_oss_signature(self, method: str, path: str, query_params: Dict[str, Any],
                             headers: Dict[str, str], data: bytes = None) -> Dict[str, str]:
        """阿里云OSS签名"""
        headers = headers.copy()
        # data参数用于未来扩展，当前OSS签名不需要payload
        
        # 添加Date头部
        headers['Date'] = time.strftime('%a, %d %b %Y %H:%M:%S GMT', time.gmtime())
        
        # 构建签名字符串
        content_md5 = headers.get('Content-MD5', '')
        content_type = headers.get('Content-Type', '')
        date = headers['Date']
        
        # OSS特殊头部
        oss_headers = {}
        for key, value in headers.items():
            if key.lower().startswith('x-oss-'):
                oss_headers[key.lower()] = value
        
        oss_headers_str = ''
        if oss_headers:
            oss_headers_str = '\n'.join([f"{k}:{v}" for k, v in sorted(oss_headers.items())]) + '\n'
        
        # 查询参数
        query_str = ''
        if query_params:
            query_str = '?' + '&'.join([f"{k}={v}" for k, v in sorted(query_params.items())])
        
        string_to_sign = f"{method}\n{content_md5}\n{content_type}\n{date}\n{oss_headers_str}{path}{query_str}"
        
        # 计算签名
        signature = base64.b64encode(
            hmac.new(self.config.secret_key.encode(), string_to_sign.encode(), hashlib.sha1).digest()
        ).decode()
        
        headers['Authorization'] = f"OSS {self.config.access_key}:{signature}"
        return headers

    def _parse_error_response(self, status: int, body: bytes) -> str:
        """解析错误响应"""
        try:
            if body:
                root = ET.fromstring(body)
                # 尝试不同的错误元素
                error_elements = ['Error', 'error']
                for error_elem_name in error_elements:
                    error_elem = root.find(f'.//{error_elem_name}')
                    if error_elem is not None:
                        code_elem = error_elem.find('.//Code') or error_elem.find('.//code')
                        message_elem = error_elem.find('.//Message') or error_elem.find('.//message')

                        if code_elem is not None:
                            error_code = code_elem.text
                            error_message = message_elem.text if message_elem is not None else ""
                            return f"{error_code}: {error_message}"

            return f"HTTP {status} 错误"
        except:
            return f"HTTP {status} 错误"

    def _make_request(self, method: str, key: str, query_params: Dict[str, Any] = None,
                     headers: Dict[str, str] = None, data: bytes = None) -> Tuple[int, Dict[str, str], bytes]:
        """发送HTTP请求"""
        if query_params is None:
            query_params = {}
        if headers is None:
            headers = {}
        
        host, path = self._get_host_and_path(key)
        
        if 'aliyuncs.com' in self.config.endpoint:
            # 阿里云OSS签名
            if method == 'GET' and not data:
                headers.pop('Content-Type', None)
            # 阿里云OSS签名路径需要包含bucket名称
            sign_path = f"/{self.config.bucket}/{key}" if key else f"/{self.config.bucket}/"
            headers = self._aliyun_oss_signature(method, sign_path, query_params, headers, data)
            return self._make_aliyun_request(host, path, method, query_params, headers, data)
        else:
            # AWS S3签名
            headers = self._aws_signature_v4(method, path, query_params, headers, data)
            return self._make_standard_request(host, path, method, query_params, headers, data)
    
    def _make_standard_request(self, host: str, path: str, method: str, 
                              query_params: Dict[str, Any], headers: Dict[str, str], 
                              data: bytes = None) -> Tuple[int, Dict[str, str], bytes]:
        """发送标准HTTP请求"""
        url = f"https://{host}{path}"
        if query_params:
            url += '?' + '&'.join([f"{k}={urllib.parse.quote_plus(str(v))}" for k, v in query_params.items()])
        
        req = urllib.request.Request(url, data=data, headers=headers, method=method)
        
        try:
            with urllib.request.urlopen(req) as response:
                return response.status, dict(response.headers), response.read()
        except urllib.error.HTTPError as e:
            return e.code, dict(e.headers), e.read()
    
    def _make_aliyun_request(self, host: str, path: str, method: str, 
                            query_params: Dict[str, Any], headers: Dict[str, str], 
                            data: bytes = None) -> Tuple[int, Dict[str, str], bytes]:
        """发送阿里云OSS请求"""
        import http.client
        import ssl
        
        # 创建HTTPS连接
        context = ssl.create_default_context()
        conn = http.client.HTTPSConnection(host, context=context)
        
        try:
            # 构建完整路径
            full_path = path
            if query_params:
                full_path += '?' + '&'.join([f"{k}={urllib.parse.quote_plus(str(v))}" for k, v in query_params.items()])
            
            # 发送请求
            conn.request(method, full_path, body=data, headers=headers)
            response = conn.getresponse()
            
            response_headers = dict(response.getheaders())
            response_body = response.read()
            
            return response.status, response_headers, response_body
        finally:
            conn.close()
    
    def test_connection(self) -> Tuple[bool, str]:
        """测试连接"""
        try:
            if not self.config.bucket:
                # 尝试列出存储桶
                buckets = self._list_buckets()
                if buckets:
                    return True, f"连接成功，发现 {len(buckets)} 个存储桶"
                else:
                    return False, "连接成功但无法列出存储桶"
            else:
                # 测试指定的存储桶
                try:
                    # 直接尝试列出文件来测试bucket和权限
                    result = self.list_files(max_keys=1)
                    return True, f"连接成功，存储桶 '{self.config.bucket}' 可访问"
                except Exception as list_error:
                    # 分析错误类型
                    error_msg = str(list_error).lower()
                    if 'nosuchbucket' in error_msg:
                        return False, f"存储桶 '{self.config.bucket}' 不存在"
                    elif 'accessdenied' in error_msg or 'forbidden' in error_msg:
                        return False, f"存储桶 '{self.config.bucket}' 存在但无访问权限"
                    elif 'invalidaccesskeyid' in error_msg:
                        return False, "Access Key ID 无效"
                    elif 'signaturemismatch' in error_msg:
                        return False, "Secret Access Key 无效或签名错误"
                    else:
                        return False, f"存储桶 '{self.config.bucket}' 连接失败: {str(list_error)}"
        except Exception as e:
            error_msg = str(e).lower()
            if 'invalidaccesskeyid' in error_msg:
                return False, "Access Key ID 无效"
            elif 'signaturemismatch' in error_msg:
                return False, "Secret Access Key 无效或签名错误"
            else:
                return False, f"连接失败: {str(e)}"
    
    def _bucket_exists(self) -> bool:
        """检查存储桶是否存在"""
        try:
            if 'aliyuncs.com' in self.config.endpoint:
                # 阿里云OSS使用list_objects检查
                try:
                    self.list_files(max_keys=1)
                    return True
                except Exception as e:
                    # 检查是否是权限问题还是bucket不存在
                    error_msg = str(e).lower()
                    if 'nosuchbucket' in error_msg or 'bucket' in error_msg:
                        return False
                    # 其他错误可能是权限问题，但bucket存在
                    return False
            else:
                # 标准S3使用HEAD请求
                status, _, _ = self._make_request('HEAD', '')
                return status == 200
        except Exception:
            return False
    
    def _list_buckets(self) -> List[str]:
        """列出存储桶"""
        try:
            # 临时移除bucket配置来列出所有bucket
            original_bucket = self.config.bucket
            self.config.bucket = ""
            
            status, _, body = self._make_request('GET', '')

            # 恢复bucket配置
            self.config.bucket = original_bucket

            if status == 200:
                root = ET.fromstring(body)
                buckets = []
                for bucket in root.findall('.//{http://s3.amazonaws.com/doc/2006-03-01/}Bucket'):
                    name_elem = bucket.find('{http://s3.amazonaws.com/doc/2006-03-01/}Name')
                    if name_elem is not None:
                        buckets.append(name_elem.text)
                return buckets
            return []
        except Exception:
            return []

    def list_files(self, prefix: str = "", max_keys: int = 1000,
                   continuation_token: Optional[str] = None) -> ListResult:
        """列出文件"""
        # 阿里云OSS和标准S3使用不同的参数
        if 'aliyuncs.com' in self.config.endpoint:
            # 阿里云OSS使用传统的list objects API
            query_params = {
                'max-keys': str(max_keys)
            }
            if prefix:
                query_params['prefix'] = prefix
            if continuation_token:
                query_params['marker'] = continuation_token
        else:
            # 标准S3使用list objects v2 API
            query_params = {
                'list-type': '2',
                'max-keys': str(max_keys)
            }
            if prefix:
                query_params['prefix'] = prefix
            if continuation_token:
                query_params['continuation-token'] = continuation_token

        status, _, body = self._make_request('GET', '', query_params)

        if status != 200:
            error_msg = self._parse_error_response(status, body)
            raise Exception(f"列出文件失败: {error_msg}")

        root = ET.fromstring(body)

        files = []
        # 尝试不同的XML命名空间
        namespaces = [
            '{http://s3.amazonaws.com/doc/2006-03-01/}',  # 标准S3
            ''  # 阿里云OSS通常不使用命名空间
        ]

        contents_found = False
        for ns in namespaces:
            contents = root.findall(f'.//{ns}Contents')
            if contents:
                contents_found = True
                for content in contents:
                    key_elem = content.find(f'{ns}Key')
                    size_elem = content.find(f'{ns}Size')
                    modified_elem = content.find(f'{ns}LastModified')
                    etag_elem = content.find(f'{ns}ETag')

                    if key_elem is not None and size_elem is not None and modified_elem is not None:
                        # 解析时间
                        modified_str = modified_elem.text
                        if modified_str.endswith('Z'):
                            modified_str = modified_str[:-1] + '+00:00'

                        try:
                            last_modified = datetime.fromisoformat(modified_str.replace('Z', '+00:00'))
                        except:
                            last_modified = datetime.now()

                        file_meta = FileMetadata(
                            key=key_elem.text,
                            size=int(size_elem.text),
                            last_modified=last_modified,
                            etag=etag_elem.text.strip('"') if etag_elem is not None else None
                        )
                        files.append(file_meta)
                break

        # 检查是否截断
        is_truncated = False
        next_token = None

        for ns in namespaces:
            is_truncated_elem = root.find(f'.//{ns}IsTruncated')
            if is_truncated_elem is not None:
                is_truncated = is_truncated_elem.text.lower() == 'true'

                if 'aliyuncs.com' in self.config.endpoint:
                    # 阿里云OSS使用NextMarker
                    next_marker_elem = root.find(f'.//{ns}NextMarker')
                    next_token = next_marker_elem.text if next_marker_elem is not None else None
                else:
                    # 标准S3使用NextContinuationToken
                    next_token_elem = root.find(f'.//{ns}NextContinuationToken')
                    next_token = next_token_elem.text if next_token_elem is not None else None
                break

        return ListResult(
            files=files,
            is_truncated=is_truncated,
            next_token=next_token
        )

    def get_file(self, key: str) -> Optional[bytes]:
        """下载文件"""
        try:
            status, _, body = self._make_request('GET', key)
            if status == 200:
                return body
            return None
        except Exception:
            return None

    def put_file(self, key: str, data: bytes,
                 content_type: str = 'binary/octet-stream',
                 metadata: Optional[Dict[str, str]] = None) -> bool:
        """上传文件"""
        try:
            headers = {'Content-Type': content_type}

            # 添加自定义元数据
            if metadata:
                for k, v in metadata.items():
                    headers[f'x-amz-meta-{k}'] = str(v)

            status, response_headers, body = self._make_request('PUT', key, headers=headers, data=data)
            return status == 200
        except Exception:
            return False

    def delete_file(self, key: str) -> bool:
        """删除文件"""
        try:
            status, headers, body = self._make_request('DELETE', key)
            return status == 204
        except Exception:
            return False

    def get_file_metadata(self, key: str) -> Optional[FileMetadata]:
        """获取文件元数据"""
        try:
            status, headers, body = self._make_request('HEAD', key)
            if status == 200:
                # 解析元数据
                size = int(headers.get('Content-Length', 0))
                content_type = headers.get('Content-Type', 'binary/octet-stream')
                etag = headers.get('ETag', '').strip('"')

                # 解析最后修改时间
                last_modified_str = headers.get('Last-Modified', '')
                try:
                    last_modified = datetime.strptime(last_modified_str, '%a, %d %b %Y %H:%M:%S %Z')
                except:
                    last_modified = datetime.now()

                # 提取自定义元数据
                custom_metadata = {}
                for k, v in headers.items():
                    if k.lower().startswith('x-amz-meta-'):
                        custom_metadata[k[11:]] = v  # 移除 'x-amz-meta-' 前缀

                return FileMetadata(
                    key=key,
                    size=size,
                    last_modified=last_modified,
                    etag=etag,
                    content_type=content_type,
                    custom_metadata=custom_metadata if custom_metadata else None
                )
            return None
        except Exception:
            return None

    def file_exists(self, key: str) -> bool:
        """检查文件是否存在"""
        try:
            status, headers, body = self._make_request('HEAD', key)
            return status == 200
        except Exception:
            return False

    def put_file_chunked(self, key: str, data: bytes, chunk_size_mb: int = 10,
                        content_type: str = 'binary/octet-stream',
                        metadata: Optional[Dict[str, str]] = None) -> bool:
        """分片上传大文件"""
        try:
            chunk_size = chunk_size_mb * 1024 * 1024
            total_size = len(data)

            if total_size <= chunk_size:
                return self.put_file(key, data, content_type, metadata)

            # 初始化分片上传
            upload_id = self._initiate_multipart_upload(key, content_type, metadata)
            if not upload_id:
                return False

            # 上传分片
            parts = []
            part_number = 1
            offset = 0

            while offset < total_size:
                chunk_data = data[offset:offset + chunk_size]
                etag = self._upload_part(key, upload_id, part_number, chunk_data)
                if not etag:
                    self._abort_multipart_upload(key, upload_id)
                    return False

                parts.append({'PartNumber': part_number, 'ETag': etag})
                part_number += 1
                offset += chunk_size

            # 完成分片上传
            return self._complete_multipart_upload(key, upload_id, parts)
        except Exception:
            return False

    def _initiate_multipart_upload(self, key: str, content_type: str,
                                  metadata: Optional[Dict[str, str]] = None) -> Optional[str]:
        """初始化分片上传"""
        try:
            query_params = {'uploads': ''}
            headers = {'Content-Type': content_type}

            if metadata:
                for k, v in metadata.items():
                    headers[f'x-amz-meta-{k}'] = str(v)

            status, response_headers, body = self._make_request('POST', key, query_params, headers)
            if status == 200:
                root = ET.fromstring(body)
                upload_id_elem = root.find('.//{http://s3.amazonaws.com/doc/2006-03-01/}UploadId')
                return upload_id_elem.text if upload_id_elem is not None else None
            return None
        except Exception:
            return None

    def _upload_part(self, key: str, upload_id: str, part_number: int, data: bytes) -> Optional[str]:
        """上传分片"""
        try:
            query_params = {'partNumber': str(part_number), 'uploadId': upload_id}
            status, headers, body = self._make_request('PUT', key, query_params, data=data)
            if status == 200:
                return headers.get('ETag', '').strip('"')
            return None
        except Exception:
            return None

    def _complete_multipart_upload(self, key: str, upload_id: str, parts: List[Dict]) -> bool:
        """完成分片上传"""
        try:
            query_params = {'uploadId': upload_id}

            # 构建XML
            xml_parts = []
            for part in parts:
                xml_parts.append(f'<Part><PartNumber>{part["PartNumber"]}</PartNumber><ETag>"{part["ETag"]}"</ETag></Part>')

            xml_data = f'<CompleteMultipartUpload>{"".join(xml_parts)}</CompleteMultipartUpload>'
            data = xml_data.encode('utf-8')

            headers = {'Content-Type': 'application/xml'}
            status, response_headers, body = self._make_request('POST', key, query_params, headers, data)
            return status == 200
        except Exception:
            return False

    def _abort_multipart_upload(self, key: str, upload_id: str) -> bool:
        """取消分片上传"""
        try:
            query_params = {'uploadId': upload_id}
            status, headers, body = self._make_request('DELETE', key, query_params)
            return status == 204
        except Exception:
            return False

    def copy_object(self, source_key: str, dest_key: str, source_adapter: 'S3StorageAdapter' = None) -> bool:
        """服务端复制对象，适用于同一存储服务内的复制"""
        try:
            # 如果没有指定源适配器，则使用自身
            if source_adapter is None:
                source_adapter = self

            # 检查是否为同一存储服务（阿里云OSS或AWS S3）
            if not self._is_same_storage_service(source_adapter):
                return False

            # 构建复制源路径
            if 'aliyuncs.com' in self.config.endpoint:
                # 阿里云OSS格式
                copy_source = f"/{source_adapter.config.bucket}/{source_key}"
            else:
                # AWS S3格式
                copy_source = f"{source_adapter.config.bucket}/{source_key}"

            # 设置复制头部
            headers = {}
            if 'aliyuncs.com' in self.config.endpoint:
                headers['x-oss-copy-source'] = copy_source
            else:
                headers['x-amz-copy-source'] = copy_source

            # 发送复制请求
            status, response_headers, body = self._make_request('PUT', dest_key, headers=headers)

            if status == 200:
                print(f"服务端复制成功: {source_key} -> {dest_key}")
                return True
            else:
                print(f"服务端复制失败: HTTP {status}")
                return False

        except Exception as e:
            print(f"服务端复制异常: {e}")
            return False

    def _is_same_storage_service(self, other_adapter: 'S3StorageAdapter') -> bool:
        """检查是否为同一存储服务"""
        # 检查是否都是阿里云OSS
        self_is_aliyun = 'aliyuncs.com' in self.config.endpoint
        other_is_aliyun = 'aliyuncs.com' in other_adapter.config.endpoint

        if self_is_aliyun and other_is_aliyun:
            # 都是阿里云OSS，检查是否为同一区域
            return self._extract_region_from_endpoint(self.config.endpoint) == \
                   self._extract_region_from_endpoint(other_adapter.config.endpoint)

        # 检查是否都是AWS S3
        self_is_aws = 'amazonaws.com' in self.config.endpoint
        other_is_aws = 'amazonaws.com' in other_adapter.config.endpoint

        if self_is_aws and other_is_aws:
            # 都是AWS S3，检查是否为同一区域
            return self.config.region == other_adapter.config.region

        return False

    def _extract_region_from_endpoint(self, endpoint: str) -> str:
        """从端点URL中提取区域信息"""
        if 'aliyuncs.com' in endpoint:
            # 阿里云OSS格式: https://oss-cn-shanghai.aliyuncs.com
            import re
            match = re.search(r'oss-([^.]+)\.aliyuncs\.com', endpoint)
            return match.group(1) if match else ''
        elif 'amazonaws.com' in endpoint:
            # AWS S3格式: https://s3.us-west-2.amazonaws.com
            import re
            match = re.search(r's3\.([^.]+)\.amazonaws\.com', endpoint)
            return match.group(1) if match else 'us-east-1'
        return ''


# 注册S3适配器
StorageFactory.register_adapter(StorageType.S3, S3StorageAdapter)
