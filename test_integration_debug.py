#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试集成测试问题
"""

import smb_storage_adapter
from storage_abstraction import StorageFactory, SMBStorageConfig, StorageType
import subprocess
import json
import os
import tempfile

def download_smb_file_with_subprocess(smb_config, file_key):
    """模拟任务管理器的子进程下载方法"""
    try:
        # 准备配置数据
        config_data = {
            'hostname': smb_config.hostname,
            'port': smb_config.port,
            'username': smb_config.username,
            'password': smb_config.password,
            'domain': smb_config.domain or '',
            'share_name': smb_config.share_name,
            'root_path': smb_config.root_path or ''
        }
        
        # 创建临时文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False, encoding='utf-8') as f:
            json.dump({
                'config': config_data,
                'file_key': file_key
            }, f)
            temp_file = f.name
        
        # 构建命令
        script_path = 'smb_downloader.py'
        cmd = ['python', script_path, temp_file]
        
        print(f"     调试: 执行命令 {cmd}")
        
        # 执行子进程
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=30,
            cwd=os.getcwd(),
            encoding='utf-8',
            errors='ignore'
        )
        
        print(f"     调试: 返回码 {result.returncode}")
        print(f"     调试: 标准输出: {repr(result.stdout)}")
        print(f"     调试: 标准错误: {repr(result.stderr)}")
        
        # 清理临时文件
        try:
            os.unlink(temp_file)
        except:
            pass
        
        if result.returncode == 0:
            output = result.stdout.strip()
            # 查找SUCCESS行
            success_line = None
            for line in output.split('\n'):
                if line.startswith("SUCCESS:"):
                    success_line = line
                    break
            
            print(f"     调试: SUCCESS行: {repr(success_line)}")
            
            if success_line:
                # 解析成功结果：SUCCESS:size:output_file
                parts = success_line.split(':', 2)
                if len(parts) == 3:
                    size = int(parts[1])
                    output_file = parts[2]
                    
                    print(f"     调试: 输出文件: {output_file}")
                    
                    # 从输出文件读取数据
                    try:
                        with open(output_file, 'rb') as f:
                            data = f.read()
                        # 清理输出文件
                        os.unlink(output_file)
                        print(f"     调试: 读取数据成功: {len(data)} bytes")
                        return data
                    except Exception as e:
                        print(f"     调试: 读取输出文件失败: {e}")
                        return None
            return None
        else:
            return None
            
    except Exception as e:
        print(f"     调试: 异常: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """主函数"""
    print("🧪 调试集成测试问题")
    
    # 创建SMB配置
    smb_config = SMBStorageConfig(
        storage_type=StorageType.SMB,
        hostname='Jayce',
        port=445,
        username='smb',
        password='smbsmb',
        domain='WORKGROUP',
        share_name='hlmj',
        root_path=''
    )
    
    # 测试单个文件下载
    test_file = '398.xml'
    print(f"测试下载: {test_file}")
    
    data = download_smb_file_with_subprocess(smb_config, test_file)
    
    if data:
        print(f"✅ 成功: {len(data)} bytes")
    else:
        print("❌ 失败")

if __name__ == "__main__":
    main()
