#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LightRek 统一存储同步工具 - 快速启动脚本
"""

import sys
import os

def main():
    """主函数"""
    print("🚀 LightRek 统一存储同步工具")
    print("=" * 50)
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ 错误: 需要Python 3.8或更高版本")
        print(f"   当前版本: {sys.version}")
        return 1
    
    # 检查必要的模块
    required_modules = [
        'unified_config_manager',
        'unified_task_manager', 
        'database_manager',
        'web_server'
    ]
    
    missing_modules = []
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        print("❌ 缺少必要的模块:")
        for module in missing_modules:
            print(f"   • {module}")
        print("\n请确保所有文件都在当前目录中")
        return 1
    
    # 检查配置文件
    config_file = 'lightrek_unified_config.json'
    if not os.path.exists(config_file):
        print(f"⚠️ 配置文件 {config_file} 不存在，将创建默认配置")
        create_default_config(config_file)
    
    print("✅ 环境检查通过")
    print("\n🌐 启动Web界面...")
    
    # 导入并启动主程序
    try:
        from main import main as main_func
        return main_func()
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


def create_default_config(config_file):
    """创建默认配置文件"""
    import json
    
    default_config = {
        "sources": {},
        "targets": {},
        "tasks": {},
        "optimization": {
            "max_workers": 20,
            "chunk_size_mb": 10,
            "retry_times": 3,
            "enable_compression": True,
            "enable_parallel_scan": True,
            "enable_cache": True,
            "cache_ttl_hours": 24
        }
    }
    
    try:
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(default_config, f, indent=2, ensure_ascii=False)
        print(f"✅ 已创建默认配置文件: {config_file}")
    except Exception as e:
        print(f"❌ 创建配置文件失败: {e}")


def show_quick_start():
    """显示快速开始指南"""
    guide = """
🚀 LightRek 快速开始指南

1. 启动程序:
   python start.py

2. 打开Web界面:
   http://localhost:8001

3. 配置数据源:
   - 点击"配置管理" -> "数据源"
   - 添加S3、SFTP、FTP等存储源

4. 配置目标存储:
   - 点击"配置管理" -> "目标存储"
   - 添加目标存储位置

5. 创建同步任务:
   - 点击"配置管理" -> "同步任务"
   - 选择源和目标，配置同步选项

6. 监控任务:
   - 在"仪表盘"查看任务状态
   - 在"任务日志"查看详细日志

支持的存储类型:
✅ S3对象存储 (AWS S3, 阿里云OSS, 腾讯云COS等)
✅ SFTP (安全文件传输协议)
✅ FTP (文件传输协议)
✅ SMB/CIFS (网络文件共享)
✅ 本地存储 (本地文件系统)

更多帮助请访问: http://localhost:8001/manual
"""
    print(guide)


if __name__ == "__main__":
    # 检查命令行参数
    if len(sys.argv) > 1:
        if sys.argv[1] in ['--help', '-h', 'help']:
            show_quick_start()
            sys.exit(0)
        elif sys.argv[1] in ['--guide', '-g', 'guide']:
            show_quick_start()
            sys.exit(0)
    
    # 运行主程序
    exit_code = main()
    
    if exit_code != 0:
        print("\n" + "=" * 50)
        print("💡 提示:")
        print("   • 运行 'python start.py --help' 查看帮助")
        print("   • 检查Python版本是否为3.8+")
        print("   • 确保所有依赖文件都在当前目录")
        print("   • 查看上方的错误信息进行排查")
    
    sys.exit(exit_code)
