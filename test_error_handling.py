#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
错误处理和边界条件测试
"""

import os
import tempfile
import json
from unified_config_manager import UnifiedConfigManager
from unified_task_manager import UnifiedTaskManager
from storage_abstraction import LocalStorageConfig, S3StorageConfig, SFTPStorageConfig
from local_storage_adapter import LocalStorageAdapter

def test_error_handling():
    """测试错误处理和边界条件"""
    print('=== 错误处理和边界条件测试 ===')
    
    # 创建临时配置文件
    temp_config = tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False)
    temp_config.close()
    config_file = temp_config.name
    
    try:
        print('\n--- 测试配置文件错误处理 ---')
        
        # 测试损坏的配置文件
        with open(config_file, 'w') as f:
            f.write('{"invalid": json}')  # 无效JSON
        
        try:
            config_manager = UnifiedConfigManager(config_file)
            print('损坏配置文件处理失败（应该抛出异常）')
        except Exception as e:
            print(f'损坏配置文件异常（预期）: {e}')
        
        # 重新创建有效配置文件
        with open(config_file, 'w') as f:
            json.dump({}, f)
        
        config_manager = UnifiedConfigManager(config_file)
        task_manager = UnifiedTaskManager(config_manager)
        
        print('\n--- 测试存储配置错误处理 ---')
        
        # 测试缺少必需参数的S3配置
        try:
            incomplete_s3_config = {
                'name': '不完整S3配置',
                'access_key': 'test_key'
                # 缺少secret_key, endpoint等必需参数
            }
            success = config_manager.add_source('incomplete_s3', 's3', incomplete_s3_config)
            print(f'不完整S3配置添加: {success}')
        except Exception as e:
            print(f'不完整S3配置异常（预期）: {e}')
        
        # 测试无效存储类型
        try:
            invalid_config = {
                'name': '无效存储类型',
                'some_param': 'value'
            }
            success = config_manager.add_source('invalid_storage', 'invalid_type', invalid_config)
            print(f'无效存储类型添加: {success}')
        except Exception as e:
            print(f'无效存储类型异常（预期）: {e}')
        
        # 测试重复ID
        local_config = {
            'name': '测试本地存储',
            'root_path': 'C:/temp'
        }
        success1 = config_manager.add_source('duplicate_id', 'local', local_config)
        success2 = config_manager.add_source('duplicate_id', 'local', local_config)
        print(f'重复ID测试: 第一次={success1}, 第二次={success2}')
        
        print('\n--- 测试任务配置错误处理 ---')
        
        # 测试无效源ID的任务
        try:
            invalid_task_id = task_manager.create_task(
                name='无效源任务',
                description='使用不存在源ID的任务',
                source_id='nonexistent_source',
                target_id='duplicate_id'  # 这个存在
            )
            print(f'无效源ID任务创建: {invalid_task_id}')
        except Exception as e:
            print(f'无效源ID任务异常（预期）: {e}')
        
        # 测试无效目标ID的任务
        try:
            invalid_task_id = task_manager.create_task(
                name='无效目标任务',
                description='使用不存在目标ID的任务',
                source_id='duplicate_id',  # 这个存在
                target_id='nonexistent_target'
            )
            print(f'无效目标ID任务创建: {invalid_task_id}')
        except Exception as e:
            print(f'无效目标ID任务异常（预期）: {e}')
        
        # 测试无效参数值
        try:
            invalid_param_task = task_manager.create_task(
                name='无效参数任务',
                description='使用无效参数的任务',
                source_id='duplicate_id',
                target_id='duplicate_id',
                max_workers=-5,  # 无效值
                retry_times=-1,  # 无效值
                chunk_size=-100  # 无效值
            )
            print(f'无效参数任务创建: {invalid_param_task}')
        except Exception as e:
            print(f'无效参数任务异常（预期）: {e}')
        
        print('\n--- 测试边界值处理 ---')
        
        # 测试极大值
        try:
            extreme_task = task_manager.create_task(
                name='极值测试任务',
                description='测试极大参数值',
                source_id='duplicate_id',
                target_id='duplicate_id',
                max_workers=1000,  # 极大值
                retry_times=100,   # 极大值
                chunk_size=10000,  # 极大值
                bandwidth_limit=99999  # 极大值
            )
            print(f'极值任务创建: {extreme_task is not None}')
            
            # 获取任务验证参数是否被正确处理
            extreme_task_obj = task_manager.get_task(extreme_task)
            if extreme_task_obj:
                print(f'极值任务参数: workers={extreme_task_obj.max_workers}, retry={extreme_task_obj.retry_times}')
        except Exception as e:
            print(f'极值任务异常: {e}')
        
        # 测试零值和空值
        try:
            zero_task = task_manager.create_task(
                name='零值测试任务',
                description='测试零值参数',
                source_id='duplicate_id',
                target_id='duplicate_id',
                max_workers=0,  # 零值
                retry_times=0,  # 零值
                chunk_size=0    # 零值
            )
            print(f'零值任务创建: {zero_task is not None}')
        except Exception as e:
            print(f'零值任务异常: {e}')
        
        print('\n--- 测试存储适配器错误处理 ---')
        
        # 创建临时目录进行测试
        temp_dir = tempfile.mkdtemp()
        
        try:
            # 测试本地存储适配器
            local_config = LocalStorageConfig(
                name='测试本地存储',
                root_path=temp_dir
            )
            local_adapter = LocalStorageAdapter(local_config)
            
            # 测试文件操作错误
            print('测试文件操作错误:')
            
            # 测试获取不存在的文件
            nonexistent_file = local_adapter.get_file('nonexistent.txt')
            print(f'  获取不存在文件: {nonexistent_file is None}')
            
            # 测试删除不存在的文件
            delete_result = local_adapter.delete_file('nonexistent.txt')
            print(f'  删除不存在文件: {delete_result}')
            
            # 测试获取不存在文件的元数据
            metadata = local_adapter.get_file_metadata('nonexistent.txt')
            print(f'  获取不存在文件元数据: {metadata is None}')
            
            # 测试文件存在性检查
            exists = local_adapter.file_exists('nonexistent.txt')
            print(f'  检查不存在文件: {exists}')
            
            # 测试上传到只读目录（模拟）
            import stat
            os.chmod(temp_dir, stat.S_IREAD)  # 设置为只读
            
            try:
                readonly_result = local_adapter.put_file('readonly_test.txt', b'test data')
                print(f'  只读目录上传: {readonly_result}')
            except Exception as e:
                print(f'  只读目录上传异常（预期）: {e}')
            finally:
                # 恢复权限
                os.chmod(temp_dir, stat.S_IWRITE | stat.S_IREAD)
            
        finally:
            # 清理临时目录
            import shutil
            shutil.rmtree(temp_dir, ignore_errors=True)
        
        print('\n--- 测试网络存储错误处理 ---')
        
        # 测试S3连接错误
        s3_config = S3StorageConfig(
            name='测试S3存储',
            access_key='invalid_key',
            secret_key='invalid_secret',
            endpoint='https://invalid.endpoint.com',
            region='invalid-region',
            bucket='invalid-bucket'
        )
        
        try:
            from s3_storage_adapter import S3StorageAdapter
            s3_adapter = S3StorageAdapter(s3_config)
            success, message = s3_adapter.test_connection()
            print(f'S3无效连接测试: {success}, 消息: {message}')
        except Exception as e:
            print(f'S3连接测试异常: {e}')
        
        # 测试SFTP连接错误
        sftp_config = SFTPStorageConfig(
            name='测试SFTP存储',
            hostname='invalid.hostname.com',
            port=22,
            username='invalid_user',
            password='invalid_pass',
            root_path='/invalid/path'
        )
        
        try:
            from sftp_storage_adapter import SFTPStorageAdapter
            sftp_adapter = SFTPStorageAdapter(sftp_config)
            success, message = sftp_adapter.test_connection()
            print(f'SFTP无效连接测试: {success}, 消息: {message}')
        except Exception as e:
            print(f'SFTP连接测试异常: {e}')
        
        print('\n--- 测试并发错误处理 ---')
        
        # 测试同时启动多个任务
        valid_source_config = {
            'name': '有效测试源',
            'root_path': 'C:/temp'
        }
        valid_target_config = {
            'name': '有效测试目标',
            'root_path': 'C:/temp/target'
        }
        
        config_manager.add_source('valid_source', 'local', valid_source_config)
        config_manager.add_target('valid_target', 'local', valid_target_config)
        
        # 创建多个任务
        task_ids = []
        for i in range(3):
            task_id = task_manager.create_task(
                name=f'并发测试任务{i+1}',
                description=f'并发测试任务{i+1}',
                source_id='valid_source',
                target_id='valid_target'
            )
            task_ids.append(task_id)
        
        print(f'创建了{len(task_ids)}个并发测试任务')
        
        # 尝试同时启动所有任务（可能会失败）
        start_results = []
        for task_id in task_ids:
            try:
                result = task_manager.start_task(task_id)
                start_results.append(result)
            except Exception as e:
                print(f'任务{task_id[:8]}启动异常: {e}')
                start_results.append(False)
        
        print(f'并发启动结果: {start_results}')
        
        # 停止所有任务
        for task_id in task_ids:
            try:
                task_manager.stop_task(task_id)
            except Exception as e:
                print(f'停止任务{task_id[:8]}异常: {e}')
        
        print('\n--- 测试内存和资源限制 ---')
        
        # 测试大量配置项
        print('测试大量配置项创建...')
        for i in range(100):
            try:
                config_manager.add_source(f'mass_source_{i}', 'local', {
                    'name': f'批量源{i}',
                    'root_path': f'C:/temp/mass_{i}'
                })
            except Exception as e:
                print(f'批量创建源{i}异常: {e}')
                break
        
        all_sources = config_manager.get_all_sources()
        print(f'成功创建源数量: {len(all_sources)}')
        
        print('\n--- 测试配置文件损坏恢复 ---')
        
        # 备份当前配置
        config_backup = config_manager.config_data.copy()
        
        # 模拟配置文件损坏
        with open(config_file, 'w') as f:
            f.write('corrupted data')
        
        try:
            # 尝试重新加载
            new_config_manager = UnifiedConfigManager(config_file)
            print('损坏配置文件恢复失败（应该使用默认配置）')
        except Exception as e:
            print(f'损坏配置文件处理异常: {e}')
        
        # 恢复配置文件
        with open(config_file, 'w') as f:
            json.dump(config_backup, f, indent=2)
        
    finally:
        # 清理临时文件
        try:
            if os.path.exists(config_file):
                os.unlink(config_file)
                print(f'\n清理临时配置文件: {config_file}')
        except Exception as e:
            print(f'\n清理配置文件失败: {e}')
    
    print('错误处理和边界条件测试完成')

if __name__ == '__main__':
    test_error_handling()
