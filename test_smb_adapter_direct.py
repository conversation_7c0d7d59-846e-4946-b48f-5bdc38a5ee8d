#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接测试SMB适配器
"""

import smb_storage_adapter
from storage_abstraction import StorageFactory, SMBStorageConfig, StorageType

def test_smb_adapter_direct():
    """直接测试SMB适配器"""
    print("🧪 直接测试SMB适配器")
    
    # 创建SMB配置
    config = SMBStorageConfig(
        storage_type=StorageType.SMB,
        hostname='Jay<PERSON>',
        port=445,
        username='smb',
        password='smbsmb',
        domain='WORKGROUP',
        share_name='hlmj',
        root_path=''
    )
    
    # 创建适配器
    adapter = StorageFactory.create_adapter(config)
    
    # 测试文件
    test_files = ['398.xml', 'TerSafe.dll', 'TP3Helper.exe']
    
    for file_key in test_files:
        print(f"\n测试文件: {file_key}")
        print(f"  文件名类型: {type(file_key)}")
        print(f"  文件名编码: {repr(file_key)}")
        
        try:
            # 直接调用get_file
            data = adapter.get_file(file_key)
            
            if data:
                print(f"  ✅ 成功: {len(data)} bytes")
            else:
                print(f"  ❌ 失败: 返回None")
                
        except Exception as e:
            print(f"  ❌ 异常: {e}")

if __name__ == "__main__":
    test_smb_adapter_direct()
