#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试HTML模板格式化
"""

import re

def test_html_template():
    """测试HTML模板中的格式化变量"""
    
    # 读取complete_web_interface.py文件
    with open('complete_web_interface.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找HTML模板部分
    template_start = content.find('html_template = """')
    template_end = content.find('"""', template_start + 20)
    
    if template_start == -1 or template_end == -1:
        print("❌ 未找到HTML模板")
        return
    
    html_template = content[template_start + 19:template_end]
    print(f"HTML模板长度: {len(html_template)} 字符")
    
    # 查找所有格式化变量
    format_vars = re.findall(r'(?<!\{)\{([a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)*(?::[^}]+)?)\}(?!\})', html_template)
    
    print(f"\\n找到 {len(format_vars)} 个格式化变量:")
    for var in sorted(set(format_vars)):
        print(f"  {var}")
    
    # 查找format调用
    format_start = content.find('html = html_template.format(')
    if format_start == -1:
        print("\\n❌ 未找到format调用")
        return
    
    format_end = content.find(')', format_start)
    format_call = content[format_start:format_end + 1]
    
    # 提取format参数
    format_params = re.findall(r'([a-zA-Z_][a-zA-Z0-9_]*)=', format_call)
    
    print(f"\\n找到 {len(format_params)} 个format参数:")
    for param in sorted(format_params):
        print(f"  {param}")
    
    # 检查缺失的变量
    template_vars = set()
    for var in format_vars:
        # 移除格式化部分 (如 :.1f)
        var_name = var.split(':')[0].split('.')[0]
        template_vars.add(var_name)
    
    format_params_set = set(format_params)
    
    missing_vars = template_vars - format_params_set
    extra_params = format_params_set - template_vars
    
    if missing_vars:
        print(f"\\n❌ 缺失的变量 ({len(missing_vars)} 个):")
        for var in sorted(missing_vars):
            print(f"  {var}")
    
    if extra_params:
        print(f"\\n⚠️ 多余的参数 ({len(extra_params)} 个):")
        for param in sorted(extra_params):
            print(f"  {param}")
    
    if not missing_vars and not extra_params:
        print("\\n✅ 所有变量都已正确定义")
    
    # 尝试创建一个测试格式化
    print("\\n=== 测试格式化 ===")
    try:
        test_stats = {
            'sources_count': 5,
            'targets_count': 3,
            'tasks_count': 2,
            'active_tasks': 1,
            'total_executions': 10,
            'today_executions': 2,
            'total_files': 100,
            'total_size_formatted': '1.5 GB',
            'success_rate': 95.5,
            'avg_speed_formatted': '10.5 MB/s',
            'running_tasks': 1
        }
        
        test_format = html_template.format(
            logo_base64='test_logo',
            sources_count=test_stats['sources_count'],
            targets_count=test_stats['targets_count'],
            tasks_count=test_stats['tasks_count'],
            active_tasks=test_stats['active_tasks'],
            total_executions=test_stats['total_executions'],
            today_executions=test_stats['today_executions'],
            total_files=test_stats['total_files'],
            total_size_formatted=test_stats['total_size_formatted'],
            success_rate=test_stats['success_rate'],
            avg_speed_formatted=test_stats['avg_speed_formatted'],
            running_tasks=test_stats['running_tasks'],
            recent_executions_html='<div>测试执行记录</div>',
            js_stats='{"test": true}'
        )
        
        print("✅ HTML模板格式化成功")
        print(f"格式化后长度: {len(test_format)} 字符")
        
    except KeyError as e:
        print(f"❌ 格式化失败，缺少变量: {e}")
    except Exception as e:
        print(f"❌ 格式化失败: {e}")

if __name__ == '__main__':
    test_html_template()
