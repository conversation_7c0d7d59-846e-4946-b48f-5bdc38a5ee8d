#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一配置管理器功能测试
"""

import os
import tempfile
from unified_config_manager import UnifiedConfigManager

def test_config_manager():
    """测试统一配置管理器功能"""
    print('=== 统一配置管理器测试 ===')

    # 创建临时配置文件
    temp_config = tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False)
    temp_config.close()
    config_file = temp_config.name

    print(f'使用临时配置文件: {config_file}')

    try:
        # 创建配置管理器
        config_manager = UnifiedConfigManager(config_file)

        print('\n--- 测试默认配置 ---')
        print(f'版本: {config_manager.config_data.get("version")}')
        print(f'数据源数量: {len(config_manager.get_all_sources())}')
        print(f'目标存储数量: {len(config_manager.get_all_targets())}')
        print(f'任务数量: {len(config_manager.get_all_tasks())}')

        print('\n--- 测试添加本地数据源 ---')
        local_source_config = {
            'name': '测试本地源',
            'description': '用于测试的本地数据源',
            'root_path': 'C:/temp/source'
        }

        success = config_manager.add_source('test_local_source', 'local', local_source_config)
        print(f'添加本地数据源: {success}')

        print('\n--- 测试添加S3目标存储 ---')
        s3_target_config = {
            'name': '测试S3目标',
            'description': '用于测试的S3目标存储',
            'access_key': 'test_access_key',
            'secret_key': 'test_secret_key',
            'endpoint': 'https://s3.amazonaws.com',
            'region': 'us-east-1',
            'bucket': 'test-bucket'
        }

        success = config_manager.add_target('test_s3_target', 's3', s3_target_config)
        print(f'添加S3目标存储: {success}')

        print('\n--- 测试获取配置 ---')
        source_config = config_manager.get_source('test_local_source')
        if source_config:
            print(f'本地源配置: type={source_config.storage_type}, name={source_config.name}, path={source_config.root_path}')
        else:
            print('获取本地源配置失败')

        target_config = config_manager.get_target('test_s3_target')
        if target_config:
            print(f'S3目标配置: type={target_config.storage_type}, name={target_config.name}, bucket={target_config.bucket}')
        else:
            print('获取S3目标配置失败')

        print('\n--- 测试获取所有配置 ---')
        all_sources = config_manager.get_all_sources()
        all_targets = config_manager.get_all_targets()
        print(f'所有数据源: {list(all_sources.keys())}')
        print(f'所有目标存储: {list(all_targets.keys())}')

        print('\n--- 测试添加同步任务 ---')
        task_config = {
            'name': '测试同步任务',
            'description': '从本地到S3的测试同步',
            'source_id': 'test_local_source',
            'target_id': 'test_s3_target',
            'sync_mode': 'incremental',
            'schedule': 'manual'
        }

        success = config_manager.add_task('test_sync_task', task_config)
        print(f'添加同步任务: {success}')

        print('\n--- 测试获取任务 ---')
        task = config_manager.get_task('test_sync_task')
        if task:
            print(f'任务配置: name={task.get("name")}, source={task.get("source_id")}, target={task.get("target_id")}')
        else:
            print('获取任务失败')

        print('\n--- 测试更新任务 ---')
        update_config = {
            'description': '更新后的任务描述',
            'sync_mode': 'full'
        }
        success = config_manager.update_task('test_sync_task', update_config)
        print(f'更新任务: {success}')

        updated_task = config_manager.get_task('test_sync_task')
        if updated_task:
            print(f'更新后任务: description={updated_task.get("description")}, sync_mode={updated_task.get("sync_mode")}')

        print('\n--- 测试优化配置 ---')
        optimization_config = config_manager.get_optimization_config()
        print(f'当前优化配置: {optimization_config}')

        new_optimization = {
            'max_workers': 30,
            'chunk_size_mb': 20,
            'enable_parallel_scan': True
        }
        success = config_manager.update_optimization_settings(new_optimization)
        print(f'更新优化配置: {success}')

        updated_optimization = config_manager.get_optimization_config()
        print(f'更新后优化配置: max_workers={updated_optimization.get("max_workers")}, chunk_size_mb={updated_optimization.get("chunk_size_mb")}')

        print('\n--- 测试存储类型信息 ---')
        try:
            storage_types = config_manager.get_storage_types()
            print(f'支持的存储类型数量: {len(storage_types)}')
            for storage_type in storage_types:
                print(f'  - {storage_type["name"]} ({storage_type["type"]}): {storage_type["description"]}')
        except AttributeError:
            print('get_storage_types方法不存在，跳过此测试')

        print('\n--- 测试连接测试功能 ---')
        try:
            # 测试本地存储连接
            success, message = config_manager.test_storage_connection('test_local_source', is_source=True)
            print(f'本地源连接测试: {success}, 消息: {message}')

            # 测试S3存储连接（预期失败）
            success, message = config_manager.test_storage_connection('test_s3_target', is_source=False)
            print(f'S3目标连接测试: {success}, 消息: {message}')
        except Exception as e:
            print(f'连接测试异常: {e}')

        print('\n--- 测试删除功能 ---')
        success = config_manager.remove_task('test_sync_task')
        print(f'删除任务: {success}')

        success = config_manager.remove_source('test_local_source')
        print(f'删除数据源: {success}')

        success = config_manager.remove_target('test_s3_target')
        print(f'删除目标存储: {success}')

        print('\n--- 测试最终状态 ---')
        print(f'剩余数据源: {len(config_manager.get_all_sources())}')
        print(f'剩余目标存储: {len(config_manager.get_all_targets())}')
        print(f'剩余任务: {len(config_manager.get_all_tasks())}')

    finally:
        # 清理临时文件
        if os.path.exists(config_file):
            os.unlink(config_file)
            print(f'\n清理临时配置文件: {config_file}')

    print('统一配置管理器测试完成')

if __name__ == '__main__':
    test_config_manager()
