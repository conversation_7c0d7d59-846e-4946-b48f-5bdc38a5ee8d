#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进的数据库管理器 - 解决并发访问问题
"""

import sqlite3
import threading
import time
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
import queue
import contextlib

class DatabaseManager:
    """改进的数据库管理器，支持高并发访问"""
    
    def __init__(self, db_path: str = "lightrek_data.db", max_connections: int = 10):
        self.db_path = db_path
        self.max_connections = max_connections
        self.logger = logging.getLogger(__name__)
        
        # 连接池
        self.connection_pool = queue.Queue(maxsize=max_connections)
        self.pool_lock = threading.Lock()
        
        # 重试配置
        self.max_retries = 5
        self.retry_delay = 0.1  # 100ms
        self.max_retry_delay = 2.0  # 2秒
        
        # 初始化数据库和连接池
        self._init_database()
        self._init_connection_pool()
    
    def _init_database(self):
        """初始化数据库表结构"""
        try:
            with sqlite3.connect(self.db_path, timeout=30.0) as conn:
                # 启用WAL模式以提高并发性能
                conn.execute('PRAGMA journal_mode=WAL')
                conn.execute('PRAGMA synchronous=NORMAL')
                conn.execute('PRAGMA cache_size=10000')
                conn.execute('PRAGMA temp_store=MEMORY')
                conn.execute('PRAGMA mmap_size=268435456')  # 256MB
                
                cursor = conn.cursor()
                
                # 文件哈希表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS file_hashes (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        storage_id TEXT NOT NULL,
                        file_key TEXT NOT NULL,
                        file_size INTEGER NOT NULL,
                        file_hash TEXT NOT NULL,
                        last_modified TEXT NOT NULL,
                        created_at TEXT NOT NULL,
                        updated_at TEXT NOT NULL,
                        UNIQUE(storage_id, file_key)
                    )
                ''')
                
                # 任务执行表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS task_executions (
                        id TEXT PRIMARY KEY,
                        task_id TEXT NOT NULL,
                        task_name TEXT NOT NULL,
                        source_id TEXT NOT NULL,
                        target_id TEXT NOT NULL,
                        status TEXT NOT NULL DEFAULT 'pending',
                        start_time TEXT NOT NULL,
                        end_time TEXT,
                        files_total INTEGER DEFAULT 0,
                        files_processed INTEGER DEFAULT 0,
                        files_skipped INTEGER DEFAULT 0,
                        files_failed INTEGER DEFAULT 0,
                        bytes_total INTEGER DEFAULT 0,
                        bytes_transferred INTEGER DEFAULT 0,
                        error_message TEXT,
                        created_at TEXT NOT NULL
                    )
                ''')
                
                # 任务日志表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS task_logs (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        execution_id TEXT NOT NULL,
                        timestamp TEXT NOT NULL,
                        level TEXT NOT NULL,
                        message TEXT NOT NULL,
                        details TEXT,
                        created_at TEXT NOT NULL,
                        FOREIGN KEY (execution_id) REFERENCES task_executions (id)
                    )
                ''')
                
                # 创建索引
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_file_hashes_storage_key ON file_hashes(storage_id, file_key)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_task_executions_task_id ON task_executions(task_id)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_task_logs_execution_id ON task_logs(execution_id)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_task_logs_timestamp ON task_logs(timestamp)')
                
                conn.commit()
                self.logger.info("数据库初始化成功")
                
        except Exception as e:
            self.logger.error(f"数据库初始化失败: {e}")
            raise
    
    def _init_connection_pool(self):
        """初始化连接池"""
        try:
            for _ in range(self.max_connections):
                conn = sqlite3.connect(
                    self.db_path, 
                    timeout=30.0,
                    check_same_thread=False
                )
                # 配置连接
                conn.execute('PRAGMA journal_mode=WAL')
                conn.execute('PRAGMA synchronous=NORMAL')
                conn.execute('PRAGMA cache_size=10000')
                conn.execute('PRAGMA temp_store=MEMORY')
                
                self.connection_pool.put(conn)
            
            self.logger.info(f"连接池初始化成功，连接数: {self.max_connections}")
            
        except Exception as e:
            self.logger.error(f"连接池初始化失败: {e}")
            raise
    
    @contextlib.contextmanager
    def get_connection(self):
        """获取数据库连接（上下文管理器）"""
        conn = None
        try:
            # 从连接池获取连接
            conn = self.connection_pool.get(timeout=10.0)
            conn.row_factory = sqlite3.Row
            yield conn
        except queue.Empty:
            # 连接池为空，创建临时连接
            self.logger.warning("连接池为空，创建临时连接")
            conn = sqlite3.connect(
                self.db_path, 
                timeout=30.0,
                check_same_thread=False
            )
            conn.row_factory = sqlite3.Row
            conn.execute('PRAGMA journal_mode=WAL')
            yield conn
        finally:
            if conn:
                try:
                    # 归还连接到池中
                    if not self.connection_pool.full():
                        self.connection_pool.put(conn)
                    else:
                        conn.close()
                except:
                    conn.close()
    
    def _execute_with_retry(self, operation, *args, **kwargs):
        """带重试的数据库操作"""
        last_exception = None
        delay = self.retry_delay
        
        for attempt in range(self.max_retries):
            try:
                return operation(*args, **kwargs)
            except sqlite3.OperationalError as e:
                last_exception = e
                if "database is locked" in str(e).lower() or "busy" in str(e).lower():
                    if attempt < self.max_retries - 1:
                        self.logger.warning(f"数据库锁定，重试 {attempt + 1}/{self.max_retries}，延迟 {delay:.2f}秒")
                        time.sleep(delay)
                        delay = min(delay * 2, self.max_retry_delay)  # 指数退避
                        continue
                raise
            except Exception as e:
                last_exception = e
                raise
        
        # 所有重试都失败了
        self.logger.error(f"数据库操作失败，已重试 {self.max_retries} 次: {last_exception}")
        raise last_exception
    
    def update_file_hash(self, storage_id: str, file_key: str, file_size: int, 
                        file_hash: str, last_modified: str) -> bool:
        """更新文件哈希记录（支持并发）"""
        def _update_operation():
            with self.get_connection() as conn:
                cursor = conn.cursor()
                now = datetime.now().isoformat()
                
                cursor.execute('''
                    INSERT OR REPLACE INTO file_hashes 
                    (storage_id, file_key, file_size, file_hash, last_modified, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (storage_id, file_key, file_size, file_hash, last_modified, now, now))
                
                conn.commit()
                return True
        
        try:
            return self._execute_with_retry(_update_operation)
        except Exception as e:
            self.logger.error(f"更新文件哈希失败: {e}")
            return False
    
    def get_file_hash(self, storage_id: str, file_key: str) -> Optional[Dict[str, Any]]:
        """获取文件哈希记录"""
        def _get_operation():
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT * FROM file_hashes 
                    WHERE storage_id = ? AND file_key = ?
                ''', (storage_id, file_key))
                
                row = cursor.fetchone()
                return dict(row) if row else None
        
        try:
            return self._execute_with_retry(_get_operation)
        except Exception as e:
            self.logger.error(f"获取文件哈希失败: {e}")
            return None
    
    def batch_update_file_hashes(self, hash_records: List[Dict[str, Any]]) -> bool:
        """批量更新文件哈希记录"""
        def _batch_update_operation():
            with self.get_connection() as conn:
                cursor = conn.cursor()
                now = datetime.now().isoformat()
                
                # 准备批量插入数据
                batch_data = []
                for record in hash_records:
                    batch_data.append((
                        record['storage_id'],
                        record['file_key'],
                        record['file_size'],
                        record['file_hash'],
                        record['last_modified'],
                        now,
                        now
                    ))
                
                cursor.executemany('''
                    INSERT OR REPLACE INTO file_hashes 
                    (storage_id, file_key, file_size, file_hash, last_modified, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', batch_data)
                
                conn.commit()
                return True
        
        try:
            return self._execute_with_retry(_batch_update_operation)
        except Exception as e:
            self.logger.error(f"批量更新文件哈希失败: {e}")
            return False
    
    def add_task_log(self, execution_id: str, level: str, message: str, details: str = None) -> bool:
        """添加任务日志"""
        def _add_log_operation():
            with self.get_connection() as conn:
                cursor = conn.cursor()
                now = datetime.now().isoformat()
                
                cursor.execute('''
                    INSERT INTO task_logs (execution_id, timestamp, level, message, details, created_at)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (execution_id, now, level, message, details, now))
                
                conn.commit()
                return True
        
        try:
            return self._execute_with_retry(_add_log_operation)
        except Exception as e:
            self.logger.error(f"添加任务日志失败: {e}")
            return False
    
    def close(self):
        """关闭数据库管理器"""
        try:
            # 关闭连接池中的所有连接
            while not self.connection_pool.empty():
                try:
                    conn = self.connection_pool.get_nowait()
                    conn.close()
                except queue.Empty:
                    break
                except Exception as e:
                    self.logger.error(f"关闭连接失败: {e}")
            
            self.logger.info("数据库管理器已关闭")
            
        except Exception as e:
            self.logger.error(f"关闭数据库管理器失败: {e}")


    def start_task_execution(self, execution_id: str, task_id: str, task_name: str,
                           source_id: str, target_id: str, task_config: Dict[str, Any] = None) -> bool:
        """开始任务执行"""
        def _start_execution_operation():
            with self.get_connection() as conn:
                cursor = conn.cursor()
                now = datetime.now().isoformat()

                cursor.execute('''
                    INSERT INTO task_executions
                    (id, task_id, task_name, source_id, target_id, status, start_time, created_at)
                    VALUES (?, ?, ?, ?, ?, 'running', ?, ?)
                ''', (execution_id, task_id, task_name, source_id, target_id, now, now))

                conn.commit()
                return True

        try:
            return self._execute_with_retry(_start_execution_operation)
        except Exception as e:
            self.logger.error(f"开始任务执行失败: {e}")
            return False

    def complete_task_execution(self, execution_id: str, status: str,
                              files_processed: int = 0, files_failed: int = 0,
                              bytes_transferred: int = 0, error_message: str = None) -> bool:
        """完成任务执行"""
        def _complete_execution_operation():
            with self.get_connection() as conn:
                cursor = conn.cursor()
                now = datetime.now().isoformat()

                cursor.execute('''
                    UPDATE task_executions
                    SET status = ?, end_time = ?, files_processed = ?, files_failed = ?,
                        bytes_transferred = ?, error_message = ?
                    WHERE id = ?
                ''', (status, now, files_processed, files_failed, bytes_transferred, error_message, execution_id))

                conn.commit()
                return True

        try:
            return self._execute_with_retry(_complete_execution_operation)
        except Exception as e:
            self.logger.error(f"完成任务执行失败: {e}")
            return False

    def get_recent_executions(self, limit: int = 20) -> List[Dict[str, Any]]:
        """获取最近的任务执行记录"""
        def _get_executions_operation():
            with self.get_connection() as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    SELECT * FROM task_executions
                    ORDER BY start_time DESC
                    LIMIT ?
                ''', (limit,))

                return [dict(row) for row in cursor.fetchall()]

        try:
            return self._execute_with_retry(_get_executions_operation)
        except Exception as e:
            self.logger.error(f"获取任务执行记录失败: {e}")
            return []

    def get_execution_logs(self, execution_id: str, limit: int = 1000) -> List[Dict[str, Any]]:
        """获取任务执行日志"""
        def _get_logs_operation():
            with self.get_connection() as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    SELECT * FROM task_logs
                    WHERE execution_id = ?
                    ORDER BY timestamp DESC
                    LIMIT ?
                ''', (execution_id, limit))

                return [dict(row) for row in cursor.fetchall()]

        try:
            return self._execute_with_retry(_get_logs_operation)
        except Exception as e:
            self.logger.error(f"获取任务日志失败: {e}")
            return []

    def clean_old_data(self, days: int = 30) -> bool:
        """清理旧数据"""
        def _clean_operation():
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cutoff_date = datetime.now().replace(day=datetime.now().day - days).isoformat()

                # 清理旧的执行记录
                cursor.execute('DELETE FROM task_executions WHERE created_at < ?', (cutoff_date,))

                # 清理旧的日志记录
                cursor.execute('DELETE FROM task_logs WHERE created_at < ?', (cutoff_date,))

                # 清理旧的文件哈希记录
                cursor.execute('DELETE FROM file_hashes WHERE updated_at < ?', (cutoff_date,))

                conn.commit()
                return True

        try:
            return self._execute_with_retry(_clean_operation)
        except Exception as e:
            self.logger.error(f"清理旧数据失败: {e}")
            return False

    def delete_execution(self, execution_id: str) -> bool:
        """删除指定的执行记录及其相关日志"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 删除相关的任务日志
                cursor.execute('DELETE FROM task_logs WHERE execution_id = ?', (execution_id,))

                # 删除执行记录
                cursor.execute('DELETE FROM task_executions WHERE id = ?', (execution_id,))

                conn.commit()

                # 检查是否真的删除了
                cursor.execute('SELECT COUNT(*) FROM task_executions WHERE id = ?', (execution_id,))
                count = cursor.fetchone()[0]

                if count == 0:
                    self.logger.info(f"成功删除执行记录: {execution_id}")
                    return True
                else:
                    self.logger.warning(f"删除执行记录失败: {execution_id}")
                    return False

        except Exception as e:
            self.logger.error(f"删除执行记录失败: {e}")
            return False

    def delete_multiple_executions(self, execution_ids: List[str]) -> Dict[str, bool]:
        """批量删除执行记录"""
        results = {}

        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                for execution_id in execution_ids:
                    try:
                        # 删除相关的任务日志
                        cursor.execute('DELETE FROM task_logs WHERE execution_id = ?', (execution_id,))

                        # 删除执行记录
                        cursor.execute('DELETE FROM task_executions WHERE id = ?', (execution_id,))

                        results[execution_id] = True

                    except Exception as e:
                        self.logger.error(f"删除执行记录失败 {execution_id}: {e}")
                        results[execution_id] = False

                conn.commit()

                success_count = sum(1 for success in results.values() if success)
                self.logger.info(f"批量删除完成: {success_count}/{len(execution_ids)} 成功")

        except Exception as e:
            self.logger.error(f"批量删除执行记录失败: {e}")
            # 如果整个操作失败，标记所有为失败
            for execution_id in execution_ids:
                results[execution_id] = False

        return results

    def update_task_execution(self, execution_id: str, **kwargs) -> bool:
        """更新任务执行记录"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 构建更新语句
                update_fields = []
                values = []

                # 支持的更新字段
                allowed_fields = [
                    'status', 'files_total', 'files_processed', 'files_skipped',
                    'files_failed', 'bytes_total', 'bytes_transferred',
                    'error_message', 'end_time'
                ]

                for field, value in kwargs.items():
                    if field in allowed_fields:
                        update_fields.append(f"{field} = ?")
                        values.append(value)

                if not update_fields:
                    self.logger.warning("没有有效的更新字段")
                    return True

                values.append(execution_id)

                sql = f"UPDATE task_executions SET {', '.join(update_fields)} WHERE id = ?"
                cursor.execute(sql, values)

                conn.commit()

                if cursor.rowcount > 0:
                    self.logger.debug(f"更新任务执行记录成功: {execution_id}")
                    return True
                else:
                    self.logger.warning(f"任务执行记录不存在: {execution_id}")
                    return False

        except Exception as e:
            self.logger.error(f"更新任务执行记录失败: {e}")
            return False

    def get_task_execution(self, execution_id: str) -> Optional[Dict[str, Any]]:
        """获取单个任务执行记录"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                cursor.execute('SELECT * FROM task_executions WHERE id = ?', (execution_id,))
                row = cursor.fetchone()

                return dict(row) if row else None

        except Exception as e:
            self.logger.error(f"获取任务执行记录失败: {e}")
            return None

    def clean_invalid_logs(self) -> int:
        """清理无效日志（30天前的记录）"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 计算30天前的日期
                from datetime import datetime, timedelta
                cutoff_date = (datetime.now() - timedelta(days=30)).isoformat()

                # 清理旧的执行记录
                cursor.execute('SELECT COUNT(*) FROM task_executions WHERE start_time < ?', (cutoff_date,))
                old_executions_count = cursor.fetchone()[0]

                cursor.execute('DELETE FROM task_executions WHERE start_time < ?', (cutoff_date,))

                # 清理旧的日志记录
                cursor.execute('SELECT COUNT(*) FROM task_logs WHERE timestamp < ?', (cutoff_date,))
                old_logs_count = cursor.fetchone()[0]

                cursor.execute('DELETE FROM task_logs WHERE timestamp < ?', (cutoff_date,))

                # 清理孤立的日志记录（没有对应执行记录的日志）
                cursor.execute('''
                    SELECT COUNT(*) FROM task_logs
                    WHERE execution_id NOT IN (SELECT id FROM task_executions)
                ''')
                orphan_logs_count = cursor.fetchone()[0]

                cursor.execute('''
                    DELETE FROM task_logs
                    WHERE execution_id NOT IN (SELECT id FROM task_executions)
                ''')

                conn.commit()

                total_cleaned = old_executions_count + old_logs_count + orphan_logs_count
                self.logger.info(f"清理完成: 执行记录 {old_executions_count} 条，日志 {old_logs_count} 条，孤立日志 {orphan_logs_count} 条")

                return total_cleaned

        except Exception as e:
            self.logger.error(f"清理无效日志失败: {e}")
            return 0


# 创建全局实例
db_manager = DatabaseManager()
