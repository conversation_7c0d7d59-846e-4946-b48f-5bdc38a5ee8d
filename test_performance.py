#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能和优化功能测试
"""

import os
import tempfile
import time
import threading
from concurrent.futures import ThreadPoolExecutor
from unified_config_manager import UnifiedConfigManager
from storage_abstraction import LocalStorageConfig
from local_storage_adapter import LocalStorageAdapter
from efficient_sync_engine import EfficientSyncEngine

def create_performance_test_files(directory, file_count=100, size_range=(1024, 100*1024)):
    """创建性能测试文件"""
    os.makedirs(directory, exist_ok=True)
    created_files = []
    
    for i in range(file_count):
        # 创建不同类型的文件
        if i % 10 == 0:
            # 大文件
            filename = f"large_file_{i:04d}.dat"
            size = 1024 * 1024  # 1MB
        elif i % 5 == 0:
            # 中等文件
            filename = f"medium_file_{i:04d}.txt"
            size = 50 * 1024  # 50KB
        else:
            # 小文件
            filename = f"small_file_{i:04d}.txt"
            size = 1024  # 1KB
        
        filepath = os.path.join(directory, filename)
        
        # 生成测试内容
        content = f"Performance test file {i}\n" + "x" * (size - len(f"Performance test file {i}\n"))
        
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(content)
        
        created_files.append(filename)
    
    return created_files

def test_performance():
    """测试性能和优化功能"""
    print('=== 性能和优化功能测试 ===')
    
    # 创建临时目录
    temp_base = tempfile.mkdtemp(prefix="lightrek_perf_test_")
    source_dir = os.path.join(temp_base, "source")
    target_dir = os.path.join(temp_base, "target")
    
    try:
        print(f'测试目录: {temp_base}')
        
        print('\n--- 准备性能测试数据 ---')
        # 创建大量测试文件
        start_time = time.time()
        created_files = create_performance_test_files(source_dir, 200)  # 200个文件
        creation_time = time.time() - start_time
        print(f'创建了 {len(created_files)} 个测试文件，耗时: {creation_time:.2f}秒')
        
        # 创建目标目录
        os.makedirs(target_dir, exist_ok=True)
        
        # 创建存储适配器
        source_config = LocalStorageConfig(
            name='性能测试源',
            root_path=source_dir
        )
        source_adapter = LocalStorageAdapter(source_config)
        
        target_config = LocalStorageConfig(
            name='性能测试目标',
            root_path=target_dir
        )
        target_adapter = LocalStorageAdapter(target_config)
        
        print('\n--- 测试文件扫描性能 ---')
        
        # 测试单线程扫描
        start_time = time.time()
        source_result = source_adapter.list_files()
        single_scan_time = time.time() - start_time
        print(f'单线程扫描 {len(source_result.files)} 个文件，耗时: {single_scan_time:.3f}秒')
        
        # 计算文件大小统计
        total_size = sum(f.size for f in source_result.files)
        avg_size = total_size / len(source_result.files) if source_result.files else 0
        print(f'文件统计: 总大小 {total_size:,} 字节, 平均大小 {avg_size:.0f} 字节')
        
        print('\n--- 测试并发同步性能 ---')
        
        # 测试不同并发数的性能
        worker_counts = [1, 2, 4, 8, 16]
        performance_results = []
        
        for workers in worker_counts:
            print(f'\\n测试 {workers} 个并发线程:')
            
            # 清空目标目录
            import shutil
            if os.path.exists(target_dir):
                shutil.rmtree(target_dir)
            os.makedirs(target_dir)
            
            # 创建同步引擎
            sync_engine = EfficientSyncEngine(max_workers=workers, chunk_size=1024*1024)
            
            # 记录开始时间
            start_time = time.time()
            
            # 执行同步
            sync_result = sync_engine.sync_files(
                source_adapter, target_adapter, source_result.files,
                f'perf_test_{workers}', f'perf_execution_{workers}'
            )
            
            elapsed_time = time.time() - start_time
            
            # 计算性能指标
            files_per_second = sync_result.completed_files / elapsed_time if elapsed_time > 0 else 0
            mb_per_second = (sync_result.transferred_bytes / (1024*1024)) / elapsed_time if elapsed_time > 0 else 0
            
            performance_results.append({
                'workers': workers,
                'time': elapsed_time,
                'files_per_sec': files_per_second,
                'mb_per_sec': mb_per_second,
                'success_rate': sync_result.completed_files / sync_result.total_files if sync_result.total_files > 0 else 0
            })
            
            print(f'  耗时: {elapsed_time:.2f}秒')
            print(f'  成功率: {sync_result.completed_files}/{sync_result.total_files} ({sync_result.completed_files/sync_result.total_files*100:.1f}%)')
            print(f'  文件速度: {files_per_second:.1f} 文件/秒')
            print(f'  传输速度: {mb_per_second:.2f} MB/秒')
        
        print('\\n--- 并发性能对比 ---')
        print('并发数\\t耗时(秒)\\t文件/秒\\tMB/秒\\t成功率')
        for result in performance_results:
            print(f'{result["workers"]}\\t{result["time"]:.2f}\\t\\t{result["files_per_sec"]:.1f}\\t{result["mb_per_sec"]:.2f}\\t{result["success_rate"]*100:.1f}%')
        
        # 找出最佳性能配置
        best_result = max(performance_results, key=lambda x: x['files_per_sec'])
        print(f'\\n最佳性能配置: {best_result["workers"]} 个线程，{best_result["files_per_sec"]:.1f} 文件/秒')
        
        print('\\n--- 测试分块传输性能 ---')
        
        # 创建大文件进行分块测试
        large_file_path = os.path.join(source_dir, "large_test_file.dat")
        large_file_size = 10 * 1024 * 1024  # 10MB
        
        with open(large_file_path, 'wb') as f:
            f.write(b'x' * large_file_size)
        
        print(f'创建大文件: {large_file_size // (1024*1024)}MB')
        
        # 测试不同分块大小
        chunk_sizes = [512*1024, 1024*1024, 2*1024*1024, 5*1024*1024]  # 512KB, 1MB, 2MB, 5MB
        
        for chunk_size in chunk_sizes:
            # 清空目标目录
            if os.path.exists(target_dir):
                shutil.rmtree(target_dir)
            os.makedirs(target_dir)
            
            # 创建同步引擎
            sync_engine = EfficientSyncEngine(max_workers=4, chunk_size=chunk_size)
            
            # 重新扫描文件（包含大文件）
            source_result_large = source_adapter.list_files()
            large_files = [f for f in source_result_large.files if f.size >= 5*1024*1024]  # 只同步大文件
            
            if large_files:
                start_time = time.time()
                sync_result = sync_engine.sync_files(
                    source_adapter, target_adapter, large_files,
                    f'chunk_test_{chunk_size}', f'chunk_execution_{chunk_size}'
                )
                elapsed_time = time.time() - start_time
                
                mb_per_second = (sync_result.transferred_bytes / (1024*1024)) / elapsed_time if elapsed_time > 0 else 0
                print(f'分块大小 {chunk_size//(1024*1024)}MB: {elapsed_time:.2f}秒, {mb_per_second:.2f} MB/秒')
        
        print('\\n--- 测试内存使用优化 ---')
        
        # 模拟内存使用监控
        import psutil
        process = psutil.Process()
        
        # 记录初始内存使用
        initial_memory = process.memory_info().rss / (1024*1024)  # MB
        print(f'初始内存使用: {initial_memory:.1f} MB')
        
        # 执行大量文件同步
        sync_engine = EfficientSyncEngine(max_workers=8, chunk_size=1024*1024)
        
        # 清空目标目录
        if os.path.exists(target_dir):
            shutil.rmtree(target_dir)
        os.makedirs(target_dir)
        
        start_time = time.time()
        sync_result = sync_engine.sync_files(
            source_adapter, target_adapter, source_result.files,
            'memory_test', 'memory_execution'
        )
        elapsed_time = time.time() - start_time
        
        # 记录峰值内存使用
        peak_memory = process.memory_info().rss / (1024*1024)  # MB
        memory_increase = peak_memory - initial_memory
        
        print(f'同步完成: {elapsed_time:.2f}秒')
        print(f'峰值内存使用: {peak_memory:.1f} MB')
        print(f'内存增长: {memory_increase:.1f} MB')
        print(f'每文件内存开销: {memory_increase/len(source_result.files)*1024:.1f} KB/文件')
        
        print('\\n--- 测试缓存机制 ---')
        
        # 测试重复扫描的缓存效果
        print('第一次扫描（无缓存）:')
        start_time = time.time()
        first_scan = source_adapter.list_files()
        first_scan_time = time.time() - start_time
        print(f'  耗时: {first_scan_time:.3f}秒, 文件数: {len(first_scan.files)}')
        
        print('第二次扫描（可能有缓存）:')
        start_time = time.time()
        second_scan = source_adapter.list_files()
        second_scan_time = time.time() - start_time
        print(f'  耗时: {second_scan_time:.3f}秒, 文件数: {len(second_scan.files)}')
        
        if second_scan_time < first_scan_time:
            improvement = (first_scan_time - second_scan_time) / first_scan_time * 100
            print(f'  缓存效果: 提升 {improvement:.1f}%')
        else:
            print('  无明显缓存效果')
        
        print('\\n--- 测试并发安全性 ---')
        
        # 测试多线程同时访问存储适配器
        def concurrent_file_access(thread_id, adapter, file_list):
            """并发文件访问测试函数"""
            success_count = 0
            error_count = 0
            
            for i, file_meta in enumerate(file_list[:10]):  # 每个线程处理前10个文件
                try:
                    # 读取文件
                    data = adapter.get_file(file_meta.key)
                    if data:
                        success_count += 1
                    
                    # 检查文件存在性
                    exists = adapter.file_exists(file_meta.key)
                    if exists:
                        success_count += 1
                    
                    # 获取文件元数据
                    metadata = adapter.get_file_metadata(file_meta.key)
                    if metadata:
                        success_count += 1
                        
                except Exception as e:
                    error_count += 1
                    print(f'线程{thread_id}文件{i}操作异常: {e}')
            
            return success_count, error_count
        
        # 启动多个线程并发访问
        thread_count = 5
        threads = []
        results = []
        
        start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=thread_count) as executor:
            futures = []
            for i in range(thread_count):
                future = executor.submit(concurrent_file_access, i, source_adapter, source_result.files)
                futures.append(future)
            
            for future in futures:
                result = future.result()
                results.append(result)
        
        concurrent_time = time.time() - start_time
        
        total_success = sum(r[0] for r in results)
        total_errors = sum(r[1] for r in results)
        
        print(f'并发访问测试: {thread_count}个线程, {concurrent_time:.2f}秒')
        print(f'成功操作: {total_success}, 错误: {total_errors}')
        print(f'并发安全性: {"通过" if total_errors == 0 else "失败"}')
        
        print('\\n--- 测试资源清理 ---')
        
        # 测试资源清理效果
        print('清理前目标目录大小:')
        target_size_before = sum(os.path.getsize(os.path.join(root, file))
                                for root, dirs, files in os.walk(target_dir)
                                for file in files)
        print(f'  {target_size_before:,} 字节')
        
        # 执行清理
        if os.path.exists(target_dir):
            shutil.rmtree(target_dir)
        
        print('清理完成')
        
    finally:
        # 清理测试目录
        if os.path.exists(temp_base):
            try:
                import shutil
                shutil.rmtree(temp_base)
                print(f'\\n清理测试目录: {temp_base}')
            except Exception as e:
                print(f'\\n清理测试目录失败: {e}')
    
    print('性能和优化功能测试完成')

if __name__ == '__main__':
    test_performance()
