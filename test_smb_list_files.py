#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试SMB文件列表功能
"""

import logging

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def test_smb_list_files():
    """测试SMB文件列表"""
    logger = setup_logging()
    
    try:
        # 导入SMB适配器以确保注册
        import smb_storage_adapter
        from storage_abstraction import StorageFactory, SMBStorageConfig, StorageType
        
        logger.info("🧪 测试SMB文件列表功能")
        
        # 创建SMB配置
        config = SMBStorageConfig(
            storage_type=StorageType.SMB,
            hostname="<PERSON><PERSON>",
            port=445,
            username="smb",
            password="smbsmb",
            domain="WORKGROUP",
            share_name="hlmj",
            root_path=""  # 空根路径
        )
        
        # 创建SMB适配器
        adapter = StorageFactory.create_adapter(config)
        logger.info("✅ SMB适配器创建成功")
        
        # 测试连接
        logger.info("🔗 测试连接...")
        success, message = adapter.test_connection()
        
        if not success:
            logger.error(f"❌ 连接失败: {message}")
            return False
        
        logger.info(f"✅ 连接成功: {message}")
        
        # 测试列出文件
        logger.info("📁 列出文件...")
        try:
            result = adapter.list_files("", max_keys=20)
            logger.info(f"✅ 文件列表获取成功")
            logger.info(f"📊 找到 {len(result.files)} 个文件/文件夹")
            logger.info(f"🔄 是否截断: {result.is_truncated}")
            logger.info(f"🔗 下一页令牌: {result.next_token}")
            
            if len(result.files) > 0:
                logger.info("📋 文件列表:")
                for i, file_meta in enumerate(result.files[:10]):  # 只显示前10个
                    file_type = "📁" if file_meta.key.endswith('/') else "📄"
                    logger.info(f"  {i+1}. {file_type} {file_meta.key} ({file_meta.size} bytes, {file_meta.last_modified})")
                
                if len(result.files) > 10:
                    logger.info(f"  ... 还有 {len(result.files) - 10} 个文件")
            else:
                logger.warning("⚠️ 共享目录为空或无法访问文件")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 列出文件失败: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            return False
        
    except Exception as e:
        logger.error(f"❌ 测试SMB文件列表失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False

def test_smb_with_different_patterns():
    """测试不同的文件模式"""
    logger = logging.getLogger(__name__)
    
    try:
        # 导入SMB适配器以确保注册
        import smb_storage_adapter
        from storage_abstraction import StorageFactory, SMBStorageConfig, StorageType
        
        logger.info("🧪 测试不同的文件模式")
        
        # 创建SMB配置
        config = SMBStorageConfig(
            storage_type=StorageType.SMB,
            hostname="Jayce",
            port=445,
            username="smb",
            password="smbsmb",
            domain="WORKGROUP",
            share_name="hlmj",
            root_path=""
        )
        
        adapter = StorageFactory.create_adapter(config)
        
        # 测试不同的前缀
        test_prefixes = [
            ("", "根目录"),
            ("test", "test前缀"),
            ("doc", "doc前缀"),
            ("backup", "backup前缀"),
        ]
        
        for prefix, description in test_prefixes:
            logger.info(f"\n📋 测试 {description} (前缀: '{prefix}')")
            try:
                result = adapter.list_files(prefix, max_keys=5)
                logger.info(f"  ✅ 找到 {len(result.files)} 个匹配项")
                for i, file_meta in enumerate(result.files):
                    logger.info(f"    {i+1}. {file_meta.key}")
            except Exception as e:
                logger.error(f"  ❌ 失败: {e}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试不同模式失败: {e}")
        return False

def test_smb_file_operations():
    """测试SMB文件操作"""
    logger = logging.getLogger(__name__)
    
    try:
        # 导入SMB适配器以确保注册
        import smb_storage_adapter
        from storage_abstraction import StorageFactory, SMBStorageConfig, StorageType
        
        logger.info("🧪 测试SMB文件操作")
        
        # 创建SMB配置
        config = SMBStorageConfig(
            storage_type=StorageType.SMB,
            hostname="Jayce",
            port=445,
            username="smb",
            password="smbsmb",
            domain="WORKGROUP",
            share_name="hlmj",
            root_path=""
        )
        
        adapter = StorageFactory.create_adapter(config)
        
        # 首先列出一些文件
        logger.info("📁 获取文件列表...")
        result = adapter.list_files("", max_keys=5)
        
        if len(result.files) == 0:
            logger.warning("⚠️ 没有文件可以测试")
            return True
        
        # 测试文件操作
        test_file = result.files[0]
        logger.info(f"🎯 测试文件: {test_file.key}")
        
        # 测试文件是否存在
        logger.info("🔍 测试文件存在性...")
        exists = adapter.file_exists(test_file.key)
        logger.info(f"  文件存在: {exists}")
        
        # 测试获取文件元数据
        logger.info("📋 测试获取文件元数据...")
        metadata = adapter.get_file_metadata(test_file.key)
        if metadata:
            logger.info(f"  ✅ 元数据获取成功: {metadata.key}, {metadata.size} bytes")
        else:
            logger.warning("  ⚠️ 无法获取元数据")
        
        # 如果是小文件，尝试下载
        if test_file.size < 1024 * 1024:  # 小于1MB
            logger.info("📥 测试文件下载...")
            try:
                data = adapter.get_file(test_file.key)
                if data:
                    logger.info(f"  ✅ 文件下载成功: {len(data)} bytes")
                else:
                    logger.warning("  ⚠️ 文件下载返回空")
            except Exception as e:
                logger.error(f"  ❌ 文件下载失败: {e}")
        else:
            logger.info("  ⏭️ 文件太大，跳过下载测试")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试文件操作失败: {e}")
        return False

def main():
    """主函数"""
    logger = setup_logging()
    
    logger.info("🧪 SMB文件列表功能测试")
    logger.info("=" * 60)
    
    tests = [
        ("SMB文件列表测试", test_smb_list_files),
        ("不同模式测试", test_smb_with_different_patterns),
        ("文件操作测试", test_smb_file_operations),
    ]
    
    passed_tests = 0
    
    for test_name, test_func in tests:
        logger.info(f"\n📋 执行测试: {test_name}")
        
        try:
            if test_func():
                logger.info(f"✅ {test_name} 通过")
                passed_tests += 1
            else:
                logger.error(f"❌ {test_name} 失败")
        except Exception as e:
            logger.error(f"❌ {test_name} 异常: {e}")
    
    logger.info(f"\n📊 测试结果: {passed_tests}/{len(tests)} 通过")
    
    if passed_tests >= 1:  # 至少1个测试通过
        logger.info("🎉 SMB文件列表功能基本正常！")
        logger.info("")
        logger.info("📋 现在您可以:")
        logger.info("  1. 在同步任务中使用SMB作为数据源")
        logger.info("  2. 浏览SMB共享中的文件和文件夹")
        logger.info("  3. 执行文件同步操作")
        
        return 0
    else:
        logger.error("❌ SMB文件列表功能存在问题，需要进一步修复")
        return 1

if __name__ == "__main__":
    exit(main())
