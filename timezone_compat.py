#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
时区兼容性模块 - 解决不同Python版本的timezone问题
"""

import sys
from datetime import datetime

# 创建兼容的timezone对象
class CompatTimezone:
    """兼容的时区类"""
    
    @property
    def utc(self):
        """获取UTC时区"""
        try:
            # Python 3.6+
            from datetime import timezone
            return timezone.utc
        except ImportError:
            # Python < 3.6 - 创建一个简单的UTC时区类
            import datetime as dt
            
            class UTC(dt.tzinfo):
                def utcoffset(self, dt):
                    return dt.timedelta(0)
                
                def tzname(self, dt):
                    return "UTC"
                
                def dst(self, dt):
                    return dt.timedelta(0)
            
            return UTC()

# 创建全局实例
timezone = CompatTimezone()

def now_utc():
    """获取当前UTC时间"""
    try:
        # Python 3.6+
        from datetime import timezone as tz
        return datetime.now(tz.utc)
    except ImportError:
        # Python < 3.6
        return datetime.utcnow()

def safe_replace_timezone(dt, tz=None):
    """安全地替换时区信息"""
    if dt is None:
        return None
    
    if not isinstance(dt, datetime):
        return dt
    
    try:
        if tz is None:
            tz = timezone.utc
        return dt.replace(tzinfo=tz)
    except:
        # 如果替换失败，返回原始对象
        return dt

def safe_fromtimestamp(timestamp, tz=None):
    """安全地从时间戳创建datetime对象"""
    try:
        if tz is None:
            tz = timezone.utc
        return datetime.fromtimestamp(timestamp, tz=tz)
    except:
        # 如果失败，使用utcfromtimestamp
        return datetime.utcfromtimestamp(timestamp)
