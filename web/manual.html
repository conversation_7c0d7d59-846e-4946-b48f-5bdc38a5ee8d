<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LightRek 用户手册</title>
    <link rel="stylesheet" href="style.css">
    <style>
        .manual-container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .manual-header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e0e0e0;
        }
        
        .manual-header h1 {
            color: #ff6b35;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .manual-toc {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
        }
        
        .manual-toc h3 {
            color: #333;
            margin-bottom: 15px;
        }
        
        .manual-toc ul {
            list-style: none;
            padding: 0;
        }
        
        .manual-toc li {
            margin-bottom: 8px;
        }
        
        .manual-toc a {
            color: #ff6b35;
            text-decoration: none;
            font-weight: 500;
        }
        
        .manual-toc a:hover {
            text-decoration: underline;
        }
        
        .manual-section {
            margin-bottom: 40px;
            padding: 20px;
            border-left: 4px solid #ff6b35;
            background: #fafafa;
            border-radius: 0 10px 10px 0;
        }
        
        .manual-section h3 {
            color: #ff6b35;
            font-size: 1.5rem;
            margin-bottom: 15px;
        }
        
        .manual-section h4 {
            color: #333;
            margin: 20px 0 10px 0;
        }
        
        .config-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .config-table th,
        .config-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .config-table th {
            background: #ff6b35;
            color: white;
            font-weight: 600;
        }
        
        .config-table tr:hover {
            background: #f5f5f5;
        }
        
        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .feature-item {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border-left: 4px solid #ff6b35;
        }
        
        .feature-item h5 {
            color: #ff6b35;
            margin-bottom: 10px;
            font-size: 1.1rem;
        }
        
        .back-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            background: #ff6b35;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
            box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
            transition: all 0.3s ease;
        }
        
        .back-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 107, 53, 0.4);
        }
    </style>
</head>
<body style="background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%); min-height: 100vh; padding: 20px;">
    <a href="/" class="back-btn">← 返回主页</a>
    
    <div class="manual-container">
        <div class="manual-header">
            <h1>📖 LightRek 用户手册</h1>
            <p>统一存储同步工具 - 完整使用指南</p>
        </div>
        
        <div class="manual-toc">
            <h3>📋 目录</h3>
            <ul>
                <li><a href="#overview">🔍 系统概述</a></li>
                <li><a href="#features">✨ 功能特性</a></li>
                <li><a href="#storage-config">💾 存储配置</a></li>
                <li><a href="#task-config">📋 任务配置</a></li>
                <li><a href="#optimization">🚀 性能优化</a></li>
                <li><a href="#troubleshooting">🔧 故障排除</a></li>
            </ul>
        </div>
        
        <div class="manual-section" id="overview">
            <h3>🔍 系统概述</h3>
            <p>LightRek是一个功能强大的统一存储同步工具，支持多种存储类型之间的数据同步。无论是云存储、本地存储还是网络存储，LightRek都能提供高效、可靠的同步解决方案。</p>
            
            <h4>支持的存储类型</h4>
            <ul>
                <li><strong>S3对象存储</strong>：Amazon S3、阿里云OSS、腾讯云COS、华为云OBS等</li>
                <li><strong>SFTP</strong>：安全文件传输协议，支持SSH密钥认证</li>
                <li><strong>FTP</strong>：文件传输协议，支持TLS加密</li>
                <li><strong>SMB/CIFS</strong>：Windows网络文件共享</li>
                <li><strong>本地存储</strong>：本地文件系统</li>
            </ul>
        </div>
        
        <div class="manual-section" id="features">
            <h3>✨ 功能特性</h3>
            <div class="feature-list">
                <div class="feature-item">
                    <h5>🔄 增量同步</h5>
                    <p>智能检测文件变化，只同步修改过的文件，大幅提升同步效率。</p>
                </div>
                <div class="feature-item">
                    <h5>⚡ 并发处理</h5>
                    <p>支持多线程并发传输，充分利用网络带宽，加速同步过程。</p>
                </div>
                <div class="feature-item">
                    <h5>🗜️ 压缩传输</h5>
                    <p>支持数据压缩传输，减少网络流量，节省传输时间。</p>
                </div>
                <div class="feature-item">
                    <h5>🛡️ 完整性验证</h5>
                    <p>支持文件哈希校验，确保数据传输的完整性和准确性。</p>
                </div>
                <div class="feature-item">
                    <h5>📊 实时监控</h5>
                    <p>提供详细的同步进度和状态监控，实时掌握同步情况。</p>
                </div>
                <div class="feature-item">
                    <h5>🔧 灵活配置</h5>
                    <p>支持文件过滤、排除规则、带宽限制等丰富的配置选项。</p>
                </div>
            </div>
        </div>
        
        <div class="manual-section" id="storage-config">
            <h3>💾 存储配置</h3>
            
            <h4>S3对象存储配置</h4>
            <table class="config-table">
                <thead>
                    <tr>
                        <th>参数</th>
                        <th>说明</th>
                        <th>示例</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>endpoint</td>
                        <td>S3服务端点URL</td>
                        <td>https://oss-cn-hangzhou.aliyuncs.com</td>
                    </tr>
                    <tr>
                        <td>access_key</td>
                        <td>访问密钥ID</td>
                        <td>LTAI4G...</td>
                    </tr>
                    <tr>
                        <td>secret_key</td>
                        <td>访问密钥Secret</td>
                        <td>xxx...</td>
                    </tr>
                    <tr>
                        <td>bucket</td>
                        <td>存储桶名称</td>
                        <td>my-bucket</td>
                    </tr>
                    <tr>
                        <td>region</td>
                        <td>区域</td>
                        <td>cn-hangzhou</td>
                    </tr>
                    <tr>
                        <td>prefix</td>
                        <td>对象前缀（可选）</td>
                        <td>backup/</td>
                    </tr>
                </tbody>
            </table>
            
            <h4>SFTP配置</h4>
            <table class="config-table">
                <thead>
                    <tr>
                        <th>参数</th>
                        <th>说明</th>
                        <th>示例</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>hostname</td>
                        <td>SFTP服务器地址</td>
                        <td>*************</td>
                    </tr>
                    <tr>
                        <td>port</td>
                        <td>端口号</td>
                        <td>22</td>
                    </tr>
                    <tr>
                        <td>username</td>
                        <td>用户名</td>
                        <td>admin</td>
                    </tr>
                    <tr>
                        <td>password</td>
                        <td>密码</td>
                        <td>password123</td>
                    </tr>
                    <tr>
                        <td>root_path</td>
                        <td>根路径</td>
                        <td>/home/<USER>/data</td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <div class="manual-section" id="task-config">
            <h3>📋 任务配置</h3>
            <p>同步任务是LightRek的核心功能，通过配置任务可以实现不同存储之间的数据同步。</p>
            
            <h4>基本配置</h4>
            <ul>
                <li><strong>任务名称</strong>：任务的唯一标识，便于管理和识别</li>
                <li><strong>数据源</strong>：选择已配置的数据源存储</li>
                <li><strong>目标存储</strong>：选择已配置的目标存储</li>
                <li><strong>同步模式</strong>：增量同步或全量同步</li>
                <li><strong>调度设置</strong>：定时执行或手动执行</li>
            </ul>
            
            <h4>高级配置</h4>
            <ul>
                <li><strong>文件过滤</strong>：支持通配符和正则表达式过滤文件</li>
                <li><strong>排除规则</strong>：排除不需要同步的文件或目录</li>
                <li><strong>并发设置</strong>：调整并发线程数以优化性能</li>
                <li><strong>重试机制</strong>：设置失败重试次数和间隔</li>
                <li><strong>完整性验证</strong>：启用文件哈希校验</li>
            </ul>
        </div>
        
        <div class="manual-section" id="optimization">
            <h3>🚀 性能优化</h3>
            <p>LightRek提供多种性能优化选项，帮助您获得最佳的同步性能。</p>
            
            <h4>优化建议</h4>
            <ul>
                <li><strong>网络环境良好</strong>：增加并发线程数（20-50）</li>
                <li><strong>网络环境一般</strong>：使用中等并发数（10-20）</li>
                <li><strong>网络环境较差</strong>：减少并发数（5-10），启用压缩</li>
                <li><strong>大文件传输</strong>：增大分块大小（50-100MB）</li>
                <li><strong>小文件传输</strong>：减小分块大小（5-10MB）</li>
            </ul>
            
            <h4>预设配置</h4>
            <ul>
                <li><strong>高性能</strong>：适用于高速网络环境</li>
                <li><strong>平衡</strong>：适用于一般网络环境</li>
                <li><strong>兼容</strong>：适用于低速或不稳定网络</li>
            </ul>
        </div>
        
        <div class="manual-section" id="troubleshooting">
            <h3>🔧 故障排除</h3>
            
            <h4>常见问题</h4>
            <div class="feature-list">
                <div class="feature-item">
                    <h5>连接测试失败</h5>
                    <p>检查网络连接、认证信息和防火墙设置。确保端口开放且凭据正确。</p>
                </div>
                <div class="feature-item">
                    <h5>同步速度慢</h5>
                    <p>调整并发线程数和分块大小，启用压缩传输，检查网络带宽。</p>
                </div>
                <div class="feature-item">
                    <h5>任务执行失败</h5>
                    <p>查看任务日志获取详细错误信息，检查存储配置和权限设置。</p>
                </div>
                <div class="feature-item">
                    <h5>文件同步不完整</h5>
                    <p>启用完整性验证，检查存储空间是否充足，确认文件权限。</p>
                </div>
            </div>
            
            <h4>日志分析</h4>
            <p>通过查看任务日志可以获取详细的执行信息：</p>
            <ul>
                <li><strong>INFO</strong>：正常执行信息</li>
                <li><strong>WARNING</strong>：警告信息，需要注意</li>
                <li><strong>ERROR</strong>：错误信息，需要处理</li>
                <li><strong>SUCCESS</strong>：成功完成信息</li>
            </ul>
        </div>
        
        <div style="text-align: center; margin-top: 40px; padding-top: 20px; border-top: 2px solid #e0e0e0;">
            <p style="color: #666;">© 2024 LightRek 统一存储同步工具 v2.0.0</p>
            <p style="color: #999; font-size: 0.9rem;">如需更多帮助，请查看项目文档或联系技术支持</p>
        </div>
    </div>
</body>
</html>
