<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LightRek 用户手册</title>
    <link rel="stylesheet" href="style.css">
    <style>
        .manual-container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .manual-header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e0e0e0;
        }
        
        .manual-header h1 {
            color: #ff6b35;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .manual-toc {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
        }
        
        .manual-toc h3 {
            color: #333;
            margin-bottom: 15px;
        }
        
        .manual-toc ul {
            list-style: none;
            padding: 0;
        }
        
        .manual-toc li {
            margin-bottom: 8px;
        }
        
        .manual-toc a {
            color: #ff6b35;
            text-decoration: none;
            font-weight: 500;
        }
        
        .manual-toc a:hover {
            text-decoration: underline;
        }
        
        .manual-section {
            margin-bottom: 40px;
            padding: 20px;
            border-left: 4px solid #ff6b35;
            background: #fafafa;
            border-radius: 0 10px 10px 0;
        }
        
        .manual-section h3 {
            color: #ff6b35;
            font-size: 1.5rem;
            margin-bottom: 15px;
        }
        
        .manual-section h4 {
            color: #333;
            margin: 20px 0 10px 0;
        }
        
        .config-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .config-table th,
        .config-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .config-table th {
            background: #ff6b35;
            color: white;
            font-weight: 600;
        }
        
        .config-table tr:hover {
            background: #f5f5f5;
        }
        
        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .feature-item {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border-left: 4px solid #ff6b35;
        }
        
        .feature-item h5 {
            color: #ff6b35;
            margin-bottom: 10px;
            font-size: 1.1rem;
        }
        
        .back-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            background: #ff6b35;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
            box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
            transition: all 0.3s ease;
        }
        
        .back-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 107, 53, 0.4);
        }
    </style>
</head>
<body style="background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%); min-height: 100vh; padding: 20px;">
    <a href="/" class="back-btn">← 返回主页</a>
    
    <div class="manual-container">
        <div class="manual-header">
            <h1>📖 LightRek 用户手册</h1>
            <p>统一存储同步工具 - 完整使用指南</p>
        </div>
        
        <div class="manual-toc">
            <h3>📋 目录</h3>
            <ul>
                <li><a href="#overview">🔍 系统概述</a></li>
                <li><a href="#features">✨ 功能特性</a></li>
                <li><a href="#storage-config">💾 存储配置</a></li>
                <li><a href="#task-config">📋 任务配置</a></li>
                <li><a href="#optimization">🚀 性能优化</a></li>
                <li><a href="#troubleshooting">🔧 故障排除</a></li>
            </ul>
        </div>
        
        <div class="manual-section" id="overview">
            <h3>🔍 系统概述</h3>
            <p>LightRek v2.0.0 是一个功能强大的统一存储同步工具，支持多种存储类型之间的高效数据同步。采用现代化架构设计，提供企业级的数据同步解决方案。</p>

            <h4>支持的存储类型</h4>
            <ul>
                <li><strong>S3对象存储</strong>：Amazon S3、阿里云OSS、腾讯云COS、华为云OBS、MinIO等</li>
                <li><strong>高性能SFTP</strong>：基于AsyncSSH的高并发SFTP传输，支持连接池和智能重连</li>
                <li><strong>FTP</strong>：文件传输协议，支持TLS加密</li>
                <li><strong>SMB/CIFS</strong>：Windows网络文件共享</li>
                <li><strong>本地存储</strong>：本地文件系统</li>
            </ul>

            <h4>🚀 v2.0.0 新特性</h4>
            <ul>
                <li><strong>高性能SFTP适配器</strong>：基于AsyncSSH，传输效率提升数倍</li>
                <li><strong>智能文件分组</strong>：自动按文件大小优化传输策略</li>
                <li><strong>数据库自动优化</strong>：定期清理和压缩，保持系统高效</li>
                <li><strong>增强的Web界面</strong>：更直观的监控和管理界面</li>
                <li><strong>文件浏览功能</strong>：支持在线浏览存储内容</li>
            </ul>
        </div>
        
        <div class="manual-section" id="features">
            <h3>✨ 功能特性</h3>
            <div class="feature-list">
                <div class="feature-item">
                    <h5>🚀 高性能传输</h5>
                    <p>基于AsyncSSH的SFTP连接池，支持8个并发连接，传输效率比传统SFTP提升数倍。</p>
                </div>
                <div class="feature-item">
                    <h5>🔄 智能增量同步</h5>
                    <p>基于文件大小和修改时间的智能增量检测，只同步变更文件，大幅提升效率。</p>
                </div>
                <div class="feature-item">
                    <h5>⚡ 智能文件分组</h5>
                    <p>自动将文件按大小分为小、中、大文件，采用不同的传输策略优化性能。</p>
                </div>
                <div class="feature-item">
                    <h5>📊 实时监控</h5>
                    <p>Web界面实时显示传输进度、速度、并发数等关键指标，全面掌握同步状态。</p>
                </div>
                <div class="feature-item">
                    <h5>⏰ 灵活调度</h5>
                    <p>支持手动执行、分钟级、小时级、天级调度，满足各种同步需求。</p>
                </div>
                <div class="feature-item">
                    <h5>🛡️ 数据完整性</h5>
                    <p>文件大小和修改时间校验，确保数据传输的完整性和准确性。</p>
                </div>
                <div class="feature-item">
                    <h5>📁 文件浏览</h5>
                    <p>支持在线浏览存储内容，直观查看文件结构和详细信息。</p>
                </div>
                <div class="feature-item">
                    <h5>🔧 数据库优化</h5>
                    <p>自动数据库清理和压缩，定期优化系统性能，保持高效运行。</p>
                </div>
            </div>
        </div>
        
        <div class="manual-section" id="storage-config">
            <h3>💾 存储配置</h3>
            
            <h4>🗂️ S3对象存储配置</h4>
            <p>支持所有S3兼容的对象存储服务，包括阿里云OSS、腾讯云COS、AWS S3、MinIO等。</p>
            <table class="config-table">
                <thead>
                    <tr>
                        <th>参数</th>
                        <th>说明</th>
                        <th>示例</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>名称</td>
                        <td>存储配置的显示名称</td>
                        <td>阿里云OSS-生产环境</td>
                    </tr>
                    <tr>
                        <td>endpoint</td>
                        <td>S3服务端点URL</td>
                        <td>https://oss-cn-hangzhou.aliyuncs.com</td>
                    </tr>
                    <tr>
                        <td>access_key</td>
                        <td>访问密钥ID</td>
                        <td>LTAI4G...</td>
                    </tr>
                    <tr>
                        <td>secret_key</td>
                        <td>访问密钥Secret</td>
                        <td>xxx...</td>
                    </tr>
                    <tr>
                        <td>bucket</td>
                        <td>存储桶名称</td>
                        <td>my-production-bucket</td>
                    </tr>
                    <tr>
                        <td>region</td>
                        <td>存储区域</td>
                        <td>cn-hangzhou</td>
                    </tr>
                    <tr>
                        <td>prefix</td>
                        <td>对象前缀（可选）</td>
                        <td>backup/2024/</td>
                    </tr>
                </tbody>
            </table>

            <div class="feature-item" style="margin: 20px 0; padding: 15px; background: #e3f2fd; border-left: 4px solid #2196f3; border-radius: 5px;">
                <strong>💡 提示：</strong>配置完成后，点击"测试连接"按钮验证配置是否正确，系统会自动检测存储桶的可访问性。
            </div>
            
            <h4>🚀 高性能SFTP配置</h4>
            <p>基于AsyncSSH的高性能SFTP适配器，支持连接池和并发传输，传输效率比传统SFTP提升数倍。</p>
            <table class="config-table">
                <thead>
                    <tr>
                        <th>参数</th>
                        <th>说明</th>
                        <th>示例</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>名称</td>
                        <td>SFTP配置的显示名称</td>
                        <td>生产服务器SFTP</td>
                    </tr>
                    <tr>
                        <td>hostname</td>
                        <td>SFTP服务器地址</td>
                        <td>************* 或 sftp.example.com</td>
                    </tr>
                    <tr>
                        <td>port</td>
                        <td>SSH端口号</td>
                        <td>22（默认）</td>
                    </tr>
                    <tr>
                        <td>username</td>
                        <td>SSH用户名</td>
                        <td>root 或 admin</td>
                    </tr>
                    <tr>
                        <td>password</td>
                        <td>SSH密码</td>
                        <td>password123</td>
                    </tr>
                    <tr>
                        <td>private_key_path</td>
                        <td>私钥文件路径（可选）</td>
                        <td>/path/to/id_rsa</td>
                    </tr>
                    <tr>
                        <td>root_path</td>
                        <td>远程根目录</td>
                        <td>/opt/ 或 /home/<USER>/data/</td>
                    </tr>
                </tbody>
            </table>

            <div class="feature-item" style="margin: 20px 0; padding: 15px; background: #fff3e0; border-left: 4px solid #ff9800; border-radius: 5px;">
                <strong>🔥 性能特性：</strong>
                <ul style="margin: 10px 0 0 0; padding-left: 20px;">
                    <li>连接池管理：自动维护8个并发连接</li>
                    <li>智能重连：连接断开时自动重连</li>
                    <li>并行传输：小文件并行处理，大文件分块传输</li>
                    <li>文件浏览：支持在线浏览SFTP目录结构</li>
                </ul>
            </div>
        </div>
        
        <div class="manual-section" id="task-config">
            <h3>📋 任务配置</h3>
            <p>LightRek支持灵活的同步任务配置，满足各种数据同步需求。</p>

            <h4>🎯 基本配置</h4>
            <ul>
                <li><strong>任务名称</strong>：任务的唯一标识，建议使用有意义的名称</li>
                <li><strong>数据源</strong>：选择已配置的源存储（支持所有存储类型）</li>
                <li><strong>目标存储</strong>：选择已配置的目标存储（支持所有存储类型）</li>
                <li><strong>源前缀</strong>：限制同步的源文件范围（可选）</li>
                <li><strong>目标前缀</strong>：指定目标存储的路径前缀（可选）</li>
            </ul>

            <h4>⏰ 调度配置</h4>
            <ul>
                <li><strong>手动执行</strong>：需要手动点击运行按钮</li>
                <li><strong>分钟级调度</strong>：每N分钟执行一次（1-59分钟）</li>
                <li><strong>小时级调度</strong>：每N小时执行一次（1-23小时）</li>
                <li><strong>天级调度</strong>：每天指定时间执行</li>
            </ul>

            <h4>🔧 高级选项</h4>
            <ul>
                <li><strong>增量同步</strong>：只同步新增或修改的文件（推荐）</li>
                <li><strong>删除多余文件</strong>：删除目标中源不存在的文件</li>
                <li><strong>文件过滤</strong>：支持文件名模式匹配</li>
                <li><strong>并发控制</strong>：调整同步的并发线程数</li>
            </ul>

            <div class="feature-item" style="margin: 20px 0; padding: 15px; background: #e8f5e8; border-left: 4px solid #4caf50; border-radius: 5px;">
                <strong>💡 最佳实践：</strong>
                <ul style="margin: 10px 0 0 0; padding-left: 20px;">
                    <li>首次同步建议先手动执行，确认无误后再启用定时调度</li>
                    <li>大量文件同步时，建议使用增量模式以提高效率</li>
                    <li>跨网络同步时，适当调整并发数以平衡速度和稳定性</li>
                </ul>
            </div>
        </div>

        <div class="manual-section" id="optimization">
            <h3>🚀 性能优化</h3>
            <p>LightRek内置多种性能优化技术，确保高效的数据同步。</p>

            <h4>⚡ 自动优化特性</h4>
            <ul>
                <li><strong>智能文件分组</strong>：自动将文件按大小分为小、中、大文件，采用不同传输策略</li>
                <li><strong>并发传输</strong>：小文件并行传输，大文件分块传输</li>
                <li><strong>连接池管理</strong>：SFTP连接池复用，减少连接开销</li>
                <li><strong>增量同步</strong>：基于文件大小和修改时间的智能增量检测</li>
                <li><strong>断点续传</strong>：大文件传输支持断点续传</li>
            </ul>

            <h4>📊 性能监控</h4>
            <ul>
                <li><strong>实时进度</strong>：Web界面实时显示传输进度和速度</li>
                <li><strong>详细日志</strong>：记录每个文件的传输状态和耗时</li>
                <li><strong>错误统计</strong>：统计传输成功率和失败原因</li>
                <li><strong>性能指标</strong>：显示传输速度、并发数等关键指标</li>
            </ul>

            <h4>🔧 数据库优化</h4>
            <p>LightRek v2.0.0 新增了自动数据库优化功能：</p>
            <ul>
                <li><strong>自动清理</strong>：定期清理旧的文件哈希和执行记录</li>
                <li><strong>数据库压缩</strong>：定期压缩数据库，回收存储空间</li>
                <li><strong>性能监控</strong>：监控数据库大小和性能指标</li>
                <li><strong>手动优化</strong>：支持通过Web界面手动触发优化</li>
            </ul>
            
        </div>

        <div class="manual-section" id="troubleshooting">
            <h3>🔧 故障排除</h3>

            <h4>🔍 常见问题解决</h4>
            <div class="feature-list">
                <div class="feature-item">
                    <h5>❌ S3连接测试失败</h5>
                    <ul>
                        <li>检查endpoint URL是否正确（如：https://oss-cn-hangzhou.aliyuncs.com）</li>
                        <li>验证access_key和secret_key是否有效</li>
                        <li>确认存储桶名称是否存在且有访问权限</li>
                        <li>检查网络连接和防火墙设置</li>
                    </ul>
                </div>
                <div class="feature-item">
                    <h5>❌ SFTP连接失败</h5>
                    <ul>
                        <li>确认SSH服务器地址和端口是否正确</li>
                        <li>验证用户名和密码是否正确</li>
                        <li>检查SSH服务是否启用（端口22）</li>
                        <li>确认防火墙是否允许SSH连接</li>
                        <li>如使用私钥，检查私钥文件路径和权限</li>
                    </ul>
                </div>
                <div class="feature-item">
                    <h5>⚠️ 同步任务执行失败</h5>
                    <ul>
                        <li>查看任务执行日志获取详细错误信息</li>
                        <li>检查源和目标存储的连接状态</li>
                        <li>确认文件权限和路径访问权限</li>
                        <li>检查磁盘空间是否充足</li>
                    </ul>
                </div>
                <div class="feature-item">
                    <h5>🐌 同步速度慢</h5>
                    <ul>
                        <li>网络带宽限制：检查网络连接质量</li>
                        <li>文件数量过多：考虑使用文件过滤减少同步范围</li>
                        <li>SFTP性能：系统已自动启用高性能SFTP适配器</li>
                        <li>并发调优：系统自动优化并发数，无需手动调整</li>
                    </ul>
                </div>
                <div class="feature-item">
                    <h5>📁 文件浏览失败</h5>
                    <ul>
                        <li>确认存储连接正常</li>
                        <li>检查目录路径是否存在</li>
                        <li>验证用户是否有目录访问权限</li>
                        <li>对于SFTP，确认root_path配置正确</li>
                    </ul>
                </div>
                <div class="feature-item">
                    <h5>💾 数据库文件过大</h5>
                    <ul>
                        <li>系统已启用自动数据库优化（每24小时执行）</li>
                        <li>可通过Web界面手动触发数据库优化</li>
                        <li>使用命令行工具：python3 database_optimizer.py --optimize</li>
                        <li>定期清理会自动删除7天前的文件哈希记录</li>
                    </ul>
                </div>
            </div>

            <h4>📋 日志查看</h4>
            <p>系统提供详细的日志记录，帮助诊断问题：</p>
            <ul>
                <li><strong>任务日志</strong>：在Web界面的"任务执行"页面查看</li>
                <li><strong>系统日志</strong>：程序运行时在终端显示</li>
                <li><strong>错误详情</strong>：每个失败的操作都有详细的错误描述</li>
            </ul>

            <h4>📊 数据库优化工具</h4>
            <p>LightRek v2.0.0 提供了专门的数据库优化工具：</p>
            <div style="background: #f5f5f5; padding: 15px; border-radius: 5px; margin: 15px 0;">
                <strong>命令行使用：</strong><br>
                <code style="background: #333; color: #fff; padding: 5px; border-radius: 3px; display: block; margin: 10px 0;">
                # 查看数据库统计<br>
                python3 database_optimizer.py --stats<br><br>
                # 执行完整优化<br>
                python3 database_optimizer.py --optimize<br><br>
                # 清理7天前的文件哈希<br>
                python3 database_optimizer.py --clean-hashes 7
                </code>
            </div>

            <div class="feature-item" style="margin: 20px 0; padding: 15px; background: #fff3cd; border-left: 4px solid #ffc107; border-radius: 5px;">
                <strong>🆘 获取帮助：</strong>如果问题仍未解决，请记录错误信息和操作步骤，便于技术支持分析。
            </div>
                <li><strong>ERROR</strong>：错误信息，需要处理</li>
                <li><strong>SUCCESS</strong>：成功完成信息</li>
            </ul>
        </div>
        
        <div style="text-align: center; margin-top: 40px; padding-top: 20px; border-top: 2px solid #e0e0e0;">
            <p style="color: #666;">© 2024 LightRek 统一存储同步工具 v2.0.0</p>
            <p style="color: #999; font-size: 0.9rem;">如需更多帮助，请查看项目文档或联系技术支持</p>
        </div>
    </div>
</body>
</html>
