<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lightrek 统一存储同步工具</title>
    <link rel="stylesheet" href="style.css">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🚀</text></svg>">
</head>
<body>
    <div class="sidebar">
        <div class="logo">
            <div class="logo-img">🚀</div>
            <h1>Lightrek</h1>
            <p>统一存储同步工具</p>
        </div>
        
        <nav>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a class="nav-link active" onclick="showPage('dashboard')">
                        <span class="nav-icon">📊</span>
                        仪表盘
                    </a>
                </li>
                
                <li class="nav-item">
                    <div class="nav-link nav-group-header" onclick="toggleSubmenu('config-menu')">
                        <span class="nav-icon">⚙️</span>
                        配置管理
                        <span class="nav-arrow">▼</span>
                    </div>
                    <ul class="nav-submenu expanded" id="config-menu">
                        <li class="nav-subitem">
                            <a class="nav-sublink" onclick="showPage('sources')">
                                <span class="nav-icon">📁</span>
                                数据源
                            </a>
                        </li>
                        <li class="nav-subitem">
                            <a class="nav-sublink" onclick="showPage('targets')">
                                <span class="nav-icon">🎯</span>
                                目标存储
                            </a>
                        </li>
                        <li class="nav-subitem">
                            <a class="nav-sublink" onclick="showPage('tasks')">
                                <span class="nav-icon">📋</span>
                                同步任务
                            </a>
                        </li>
                    </ul>
                </li>
                
                <li class="nav-item">
                    <a class="nav-link" onclick="showPage('logs')">
                        <span class="nav-icon">📄</span>
                        任务日志
                    </a>
                </li>
                
                <li class="nav-item">
                    <a class="nav-link" onclick="showPage('optimization')">
                        <span class="nav-icon">🚀</span>
                        性能优化
                    </a>
                </li>
                
                <li class="nav-item">
                    <a class="nav-link" href="manual.html" target="_blank">
                        <span class="nav-icon">📖</span>
                        用户手册
                    </a>
                </li>
            </ul>
        </nav>
    </div>
    
    <div class="main-content">
        <!-- 仪表盘页面 -->
        <div id="dashboard-page" class="page-content active">
            <div class="page-header">
                <h2>📊 系统仪表盘</h2>
                <p>实时监控同步任务状态和系统性能</p>
            </div>
            
            <div class="stats-grid" id="stats-grid">
                <!-- 统计卡片将通过JavaScript动态生成 -->
            </div>
            
            <div class="chart-section">
                <div class="chart-card">
                    <div class="chart-title">📈 任务执行趋势</div>
                    <div id="execution-chart">
                        <div class="chart-placeholder">暂无数据</div>
                    </div>
                </div>
                <div class="chart-card">
                    <div class="chart-title">💾 存储使用情况</div>
                    <div id="storage-chart">
                        <div class="chart-placeholder">暂无数据</div>
                    </div>
                </div>
            </div>
            
            <div class="recent-section">
                <div class="section-title">
                    <span>🕒 最近执行记录</span>
                    <button class="btn btn-secondary" onclick="refreshExecutions()">🔄 刷新</button>
                </div>
                <div id="recent-executions">
                    <!-- 最近执行记录将通过JavaScript动态加载 -->
                </div>
            </div>
        </div>
        
        <!-- 数据源页面 -->
        <div id="sources-page" class="page-content">
            <div class="page-header">
                <h2>📁 数据源管理</h2>
                <p>配置和管理各种类型的数据源</p>
            </div>
            
            <div class="page-actions">
                <button class="btn btn-primary" onclick="showAddSourceModal()">
                    ➕ 添加数据源
                </button>
                <button class="btn btn-secondary" onclick="loadSources()">
                    🔄 刷新列表
                </button>
                <button class="btn btn-info" onclick="showFileBrowserModal('source')">
                    📁 浏览文件
                </button>
            </div>
            
            <div id="sources-container" class="config-container">
                <!-- 数据源列表将通过JavaScript动态加载 -->
            </div>
        </div>
        
        <!-- 目标存储页面 -->
        <div id="targets-page" class="page-content">
            <div class="page-header">
                <h2>🎯 目标存储管理</h2>
                <p>配置和管理目标存储位置</p>
            </div>
            
            <div class="page-actions">
                <button class="btn btn-primary" onclick="showAddTargetModal()">
                    ➕ 添加目标存储
                </button>
                <button class="btn btn-secondary" onclick="loadTargets()">
                    🔄 刷新列表
                </button>
                <button class="btn btn-info" onclick="showFileBrowserModal('target')">
                    📁 浏览文件
                </button>
            </div>
            
            <div id="targets-container" class="config-container">
                <!-- 目标存储列表将通过JavaScript动态加载 -->
            </div>
        </div>
        
        <!-- 同步任务页面 -->
        <div id="tasks-page" class="page-content">
            <div class="page-header">
                <h2>📋 同步任务管理</h2>
                <p>创建和管理同步任务</p>
            </div>
            
            <div class="page-actions">
                <button class="btn btn-primary" onclick="showAddTaskModal()">
                    ➕ 创建同步任务
                </button>
                <button class="btn btn-secondary" onclick="loadTasks()">
                    🔄 刷新列表
                </button>
            </div>
            
            <div id="tasks-container" class="config-container">
                <!-- 同步任务列表将通过JavaScript动态加载 -->
            </div>
        </div>
        
        <!-- 任务日志页面 -->
        <div id="logs-page" class="page-content">
            <div class="page-header">
                <h2>📄 任务日志</h2>
                <p>查看任务执行日志和状态</p>
            </div>
            
            <div class="page-actions">
                <select id="taskLogFilter" onchange="filterTaskLogs()">
                    <option value="">所有任务</option>
                </select>
                <button class="btn btn-secondary" onclick="loadTaskLogs()">
                    🔄 刷新日志
                </button>
                <button class="btn btn-danger" onclick="batchDeleteExecutions()">
                    🗑️ 批量删除
                </button>
                <button class="btn btn-warning" onclick="cleanOldLogs()">
                    🧹 清理旧日志
                </button>
            </div>
            
            <div id="logs-container" class="logs-container">
                <!-- 任务日志将通过JavaScript动态加载 -->
            </div>
        </div>
        
        <!-- 性能优化页面 -->
        <div id="optimization-page" class="page-content">
            <div class="page-header">
                <h2>🚀 性能优化</h2>
                <p>调整系统性能参数</p>
            </div>
            
            <div class="optimization-container">
                <div class="optimization-presets">
                    <h3>快速配置</h3>
                    <div class="preset-buttons">
                        <button class="btn btn-secondary" onclick="applyOptimizationPreset('high_performance')">
                            🚀 高性能
                        </button>
                        <button class="btn btn-secondary" onclick="applyOptimizationPreset('balanced')">
                            ⚖️ 平衡
                        </button>
                        <button class="btn btn-secondary" onclick="applyOptimizationPreset('compatible')">
                            🛡️ 兼容
                        </button>
                    </div>
                </div>
                
                <form id="optimization-form" class="optimization-form">
                    <div class="form-section">
                        <h3>并发设置</h3>
                        <div class="form-group">
                            <label for="max_workers">最大并发线程数</label>
                            <input type="number" id="max_workers" name="max_workers" min="1" max="100" value="20">
                        </div>
                    </div>
                    
                    <div class="form-section">
                        <h3>传输设置</h3>
                        <div class="form-group">
                            <label for="chunk_size_mb">分块大小 (MB)</label>
                            <input type="number" id="chunk_size_mb" name="chunk_size_mb" min="1" max="1000" value="10">
                        </div>
                        <div class="form-group">
                            <label for="retry_times">重试次数</label>
                            <input type="number" id="retry_times" name="retry_times" min="0" max="10" value="3">
                        </div>
                        <div class="form-group">
                            <label for="retry_delay">重试延迟 (秒)</label>
                            <input type="number" id="retry_delay" name="retry_delay" min="0" max="60" step="0.1" value="2">
                        </div>
                    </div>
                    
                    <div class="form-section">
                        <h3>功能开关</h3>
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="enable_compression" name="enable_compression" checked>
                                启用压缩传输
                            </label>
                        </div>
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="enable_parallel_scan" name="enable_parallel_scan" checked>
                                启用并行扫描
                            </label>
                        </div>
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="enable_cache" name="enable_cache" checked>
                                启用缓存
                            </label>
                        </div>
                        <div class="form-group">
                            <label for="cache_ttl_hours">缓存有效期 (小时)</label>
                            <input type="number" id="cache_ttl_hours" name="cache_ttl_hours" min="1" max="168" value="24">
                        </div>
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="verify_integrity" name="verify_integrity" checked>
                                启用完整性验证
                            </label>
                        </div>
                    </div>
                    
                    <div class="form-actions">
                        <button type="button" class="btn btn-primary" onclick="saveOptimizationConfig()">
                            💾 保存配置
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="resetOptimizationConfig()">
                            🔄 重置默认
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- 通知容器 -->
    <div id="notification-container"></div>
    
    <!-- 模态框容器 -->
    <div id="modal-container"></div>

    <!-- 文件浏览器模态框 -->
    <div id="fileBrowserModal" class="modal">
        <div class="modal-content file-browser-modal">
            <div class="modal-header">
                <h3 id="fileBrowserTitle">📁 文件浏览器</h3>
                <span class="close" onclick="closeFileBrowserModal()">&times;</span>
            </div>
            <div class="modal-body">
                <div class="file-browser-toolbar">
                    <div class="storage-selector">
                        <label>选择存储:</label>
                        <select id="fileBrowserStorageSelect" onchange="onStorageChange()">
                            <option value="">请选择存储</option>
                        </select>
                    </div>
                    <div class="path-navigation">
                        <label>当前路径:</label>
                        <div class="breadcrumb" id="fileBrowserBreadcrumb">
                            <span class="breadcrumb-item" onclick="navigateToPath('')">🏠 根目录</span>
                        </div>
                    </div>
                    <div class="file-actions">
                        <button class="btn btn-secondary" onclick="refreshFileBrowser()">🔄 刷新</button>
                        <button class="btn btn-success" onclick="downloadSelectedFiles()" id="downloadBtn" disabled>
                            📦 下载选中文件
                        </button>
                    </div>
                </div>

                <div class="file-browser-content">
                    <div class="file-list-header">
                        <label class="checkbox-container">
                            <input type="checkbox" id="selectAllFiles" onchange="toggleAllFiles(this)">
                            <span class="checkmark"></span>
                            全选
                        </label>
                        <span class="file-count" id="fileCount">0 个项目</span>
                    </div>

                    <div class="file-list" id="fileList">
                        <div class="loading-placeholder">请选择存储以浏览文件</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="app.js"></script>
</body>
</html>
