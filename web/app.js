// LightRek 统一存储同步工具 - 前端JavaScript

// 全局变量
let currentPage = 'dashboard';
let allSources = {};
let allTargets = {};
let allTasks = {};
let allTasksForLogs = {};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('LightRek Web界面初始化');
    
    // 初始化仪表盘
    loadDashboard();
    
    // 初始化子菜单状态
    const configMenu = document.getElementById('config-menu');
    if (configMenu) {
        configMenu.classList.add('expanded');
        const arrow = document.querySelector('.nav-group-header .nav-arrow');
        if (arrow) {
            arrow.classList.add('expanded');
        }
    }
    
    // 检查URL hash
    const hash = window.location.hash.substring(1);
    if (hash) {
        showPage(hash);
    }
});

// 页面切换功能
function showPage(page) {
    console.log('切换到页面:', page);
    currentPage = page;
    
    // 隐藏所有页面
    document.querySelectorAll('.page-content').forEach(content => {
        content.classList.remove('active');
    });
    
    // 显示目标页面
    const targetPage = document.getElementById(page + '-page');
    if (targetPage) {
        targetPage.classList.add('active');
    }
    
    // 更新导航状态
    document.querySelectorAll('.nav-link, .nav-sublink').forEach(link => {
        link.classList.remove('active');
    });
    
    const currentLink = document.querySelector(`[onclick*="showPage('${page}')"]`);
    if (currentLink) {
        currentLink.classList.add('active');
    }
    
    // 根据页面类型加载内容
    switch(page) {
        case 'dashboard':
            loadDashboard();
            break;
        case 'sources':
            loadSources();
            break;
        case 'targets':
            loadTargets();
            break;
        case 'tasks':
            loadTasks();
            break;
        case 'logs':
            loadTaskLogs();
            break;
        case 'optimization':
            loadOptimizationConfig();
            break;
    }
    
    // 更新URL hash
    window.location.hash = page;
}

// 子菜单切换
function toggleSubmenu(menuId) {
    console.log('切换子菜单:', menuId);
    const submenu = document.getElementById(menuId);
    const arrow = submenu.previousElementSibling.querySelector('.nav-arrow');
    
    if (submenu && arrow) {
        if (submenu.classList.contains('expanded')) {
            submenu.classList.remove('expanded');
            arrow.classList.remove('expanded');
        } else {
            submenu.classList.add('expanded');
            arrow.classList.add('expanded');
        }
    }
}

// 加载仪表盘数据
function loadDashboard() {
    console.log('加载仪表盘数据');
    
    // 加载统计数据
    fetch('/api/statistics')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                generateStatsCards(data.statistics);
            } else {
                console.error('获取统计数据失败:', data.message);
                generateStatsCards({
                    sources: 0,
                    targets: 0,
                    tasks: 0,
                    executions: 0
                });
            }
        })
        .catch(error => {
            console.error('获取统计数据失败:', error);
            generateStatsCards({
                sources: 0,
                targets: 0,
                tasks: 0,
                executions: 0
            });
        });
    
    // 加载最近执行记录
    loadRecentExecutions();
}

// 生成统计卡片
function generateStatsCards(stats) {
    const statsGrid = document.getElementById('stats-grid');
    if (!statsGrid) {
        console.error('找不到stats-grid元素');
        return;
    }
    
    const cards = [
        {
            icon: '📁',
            iconClass: 'sources',
            title: '数据源',
            value: stats.sources || 0,
            description: '已配置的数据源数量'
        },
        {
            icon: '🎯',
            iconClass: 'targets',
            title: '目标存储',
            value: stats.targets || 0,
            description: '已配置的目标存储数量'
        },
        {
            icon: '📋',
            iconClass: 'tasks',
            title: '同步任务',
            value: stats.tasks || 0,
            description: '已创建的同步任务数量'
        },
        {
            icon: '🔄',
            iconClass: 'executions',
            title: '执行次数',
            value: stats.executions || 0,
            description: '总任务执行次数'
        }
    ];
    
    statsGrid.innerHTML = cards.map(card => `
        <div class="stat-card">
            <div class="stat-header">
                <div class="stat-icon ${card.iconClass}">${card.icon}</div>
                <div class="stat-title">${card.title}</div>
            </div>
            <div class="stat-value">${card.value}</div>
            <div class="stat-description">${card.description}</div>
        </div>
    `).join('');
    
    console.log('统计卡片已生成');
}

// 加载最近执行记录
function loadRecentExecutions() {
    console.log('加载最近执行记录');
    fetch('/api/task-executions')
        .then(response => response.json())
        .then(data => {
            console.log('执行记录数据:', data);
            const container = document.getElementById('recent-executions');
            if (!container) {
                console.error('找不到recent-executions元素');
                return;
            }
            
            if (data.success && data.executions && data.executions.length > 0) {
                container.innerHTML = data.executions.slice(0, 5).map(exec => `
                    <div class="recent-item">
                        <div class="recent-info">
                            <div class="recent-name">${exec.task_name || '未知任务'}</div>
                            <div class="recent-details">
                                执行时间: ${exec.start_time ? new Date(exec.start_time).toLocaleString() : '未知'} | 
                                耗时: ${exec.duration || '未知'}
                            </div>
                        </div>
                        <div class="recent-status status-${exec.status || 'idle'}">${getStatusText(exec.status)}</div>
                    </div>
                `).join('');
            } else {
                container.innerHTML = '<div class="empty-state"><h3>暂无执行记录</h3><p>还没有任务执行记录</p></div>';
            }
        })
        .catch(error => {
            console.error('加载执行记录失败:', error);
            const container = document.getElementById('recent-executions');
            if (container) {
                container.innerHTML = '<div class="empty-state"><h3>加载失败</h3><p>无法获取执行记录</p></div>';
            }
        });
}

// 刷新执行记录
function refreshExecutions() {
    loadRecentExecutions();
    showNotification('执行记录已刷新', 'success');
}

// 加载数据源
function loadSources() {
    console.log('加载数据源');
    fetch('/api/sources')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                allSources = data.sources;
                renderSources(data.sources);
            } else {
                console.error('获取数据源失败:', data.message);
                showNotification('获取数据源失败: ' + data.message, 'error');
            }
        })
        .catch(error => {
            console.error('获取数据源失败:', error);
            showNotification('获取数据源失败: ' + error.message, 'error');
        });
}

// 渲染数据源列表
function renderSources(sources) {
    const container = document.getElementById('sources-container');
    if (!container) return;
    
    if (Object.keys(sources).length === 0) {
        container.innerHTML = '<div class="empty-state"><h3>暂无数据源</h3><p>点击上方按钮添加第一个数据源</p></div>';
        return;
    }
    
    container.innerHTML = Object.entries(sources).map(([id, source]) => `
        <div class="config-item">
            <div class="config-header">
                <h4>${source.name}</h4>
                <div class="config-actions">
                    <button onclick="editSource('${id}')" class="btn btn-edit">编辑</button>
                    <button onclick="deleteSource('${id}')" class="btn btn-delete">删除</button>
                </div>
            </div>
            <p class="config-desc">${source.description || '无描述'}</p>
            <div class="config-details">
                <span>类型: ${getStorageTypeLabel(source.storage_type || 's3')}</span>
                ${getStorageDetailsHtml(source)}
            </div>
        </div>
    `).join('');
}

// 加载目标存储
function loadTargets() {
    console.log('加载目标存储');
    fetch('/api/targets')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                allTargets = data.targets;
                renderTargets(data.targets);
            } else {
                console.error('获取目标存储失败:', data.message);
                showNotification('获取目标存储失败: ' + data.message, 'error');
            }
        })
        .catch(error => {
            console.error('获取目标存储失败:', error);
            showNotification('获取目标存储失败: ' + error.message, 'error');
        });
}

// 渲染目标存储列表
function renderTargets(targets) {
    const container = document.getElementById('targets-container');
    if (!container) return;
    
    if (Object.keys(targets).length === 0) {
        container.innerHTML = '<div class="empty-state"><h3>暂无目标存储</h3><p>点击上方按钮添加第一个目标存储</p></div>';
        return;
    }
    
    container.innerHTML = Object.entries(targets).map(([id, target]) => `
        <div class="config-item">
            <div class="config-header">
                <h4>${target.name}</h4>
                <div class="config-actions">
                    <button onclick="editTarget('${id}')" class="btn btn-edit">编辑</button>
                    <button onclick="deleteTarget('${id}')" class="btn btn-delete">删除</button>
                </div>
            </div>
            <p class="config-desc">${target.description || '无描述'}</p>
            <div class="config-details">
                <span>类型: ${getStorageTypeLabel(target.storage_type || 's3')}</span>
                ${getStorageDetailsHtml(target)}
            </div>
        </div>
    `).join('');
}

// 加载同步任务
function loadTasks() {
    console.log('加载同步任务');
    fetch('/api/tasks')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                allTasks = data.tasks;
                renderTasks(data.tasks);
            } else {
                console.error('获取同步任务失败:', data.message);
                showNotification('获取同步任务失败: ' + data.message, 'error');
            }
        })
        .catch(error => {
            console.error('获取同步任务失败:', error);
            showNotification('获取同步任务失败: ' + error.message, 'error');
        });
}

// 渲染同步任务列表
function renderTasks(tasks) {
    const container = document.getElementById('tasks-container');
    if (!container) return;
    
    if (Object.keys(tasks).length === 0) {
        container.innerHTML = '<div class="empty-state"><h3>暂无同步任务</h3><p>点击上方按钮创建第一个同步任务</p></div>';
        return;
    }
    
    container.innerHTML = Object.entries(tasks).map(([id, task]) => `
        <div class="config-item">
            <div class="config-header">
                <h4>${task.name}</h4>
                <div class="config-actions">
                    <button onclick="viewTask('${id}')" class="btn btn-view">查看</button>
                    <button onclick="runTask('${id}')" class="btn btn-run">运行</button>
                    <button onclick="editTask('${id}')" class="btn btn-edit">编辑</button>
                    <button onclick="deleteTask('${id}')" class="btn btn-delete">删除</button>
                </div>
            </div>
            <p class="config-desc">${task.description || '无描述'}</p>
            <div class="config-details">
                <span>同步模式: ${task.sync_mode === 'incremental' ? '增量同步' : '完全同步'}</span>
                <span>调度: ${getScheduleTypeLabel(task.schedule_type)}</span>
                <span>状态: ${getTaskStatusLabel(task.status || 'idle')}</span>
                <span>启用: ${task.enabled ? '是' : '否'}</span>
            </div>
        </div>
    `).join('');
}

// 工具函数
function getStatusText(status) {
    const statusMap = {
        'idle': '空闲',
        'running': '运行中',
        'completed': '已完成',
        'failed': '失败',
        'stopped': '已停止'
    };
    return statusMap[status] || status || '未知';
}

function getStorageTypeLabel(type) {
    const typeMap = {
        's3': 'S3对象存储',
        'sftp': 'SFTP',
        'ftp': 'FTP',
        'smb': 'SMB/CIFS',
        'local': '本地存储'
    };
    return typeMap[type] || type;
}

function getStorageDetailsHtml(storage) {
    const storageType = storage.storage_type || 's3';

    switch(storageType) {
        case 's3':
            return `
                <span>端点: ${storage.endpoint || '未设置'}</span>
                <span>存储桶: ${storage.bucket || '未设置'}</span>
                <span>区域: ${storage.region || '未设置'}</span>
            `;
        case 'sftp':
            return `
                <span>主机: ${storage.hostname || storage.host || '未设置'}</span>
                <span>端口: ${storage.port || 22}</span>
                <span>用户: ${storage.username || '未设置'}</span>
                <span>路径: ${storage.root_path || '/'}</span>
            `;
        case 'smb':
            return `
                <span>主机: ${storage.hostname || storage.host || '未设置'}</span>
                <span>共享: ${storage.share_name || '未设置'}</span>
                <span>用户: ${storage.username || '未设置'}</span>
                <span>路径: ${storage.root_path || '/'}</span>
            `;
        case 'ftp':
            return `
                <span>主机: ${storage.hostname || storage.host || '未设置'}</span>
                <span>端口: ${storage.port || 21}</span>
                <span>用户: ${storage.username || '未设置'}</span>
                <span>路径: ${storage.root_path || '/'}</span>
            `;
        case 'local':
            return `
                <span>根路径: ${storage.root_path || '未设置'}</span>
            `;
        default:
            return '<span>配置信息不可用</span>';
    }
}

function getScheduleTypeLabel(type) {
    const typeMap = {
        'manual': '手动执行',
        'interval': '定时执行',
        'cron': 'Cron表达式'
    };
    return typeMap[type] || '手动执行';
}

function getTaskStatusLabel(status) {
    return getStatusText(status);
}

// 通知功能
function showNotification(message, type = 'info') {
    const container = document.getElementById('notification-container');
    if (!container) return;

    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <span>${message}</span>
        <button onclick="this.parentElement.remove()">×</button>
    `;

    container.appendChild(notification);

    // 3秒后自动移除
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 3000);
}

// 删除数据源
function deleteSource(id) {
    if (confirm('确定要删除这个数据源吗？')) {
        fetch(`/api/sources/${id}`, { method: 'DELETE' })
            .then(r => r.json())
            .then(result => {
                if (result.success) {
                    showNotification('数据源删除成功', 'success');
                    loadSources();
                } else {
                    showNotification('删除失败: ' + result.message, 'error');
                }
            })
            .catch(error => {
                showNotification('删除失败: ' + error.message, 'error');
            });
    }
}

// 删除目标存储
function deleteTarget(id) {
    if (confirm('确定要删除这个目标存储吗？')) {
        fetch(`/api/targets/${id}`, { method: 'DELETE' })
            .then(r => r.json())
            .then(result => {
                if (result.success) {
                    showNotification('目标存储删除成功', 'success');
                    loadTargets();
                } else {
                    showNotification('删除失败: ' + result.message, 'error');
                }
            })
            .catch(error => {
                showNotification('删除失败: ' + error.message, 'error');
            });
    }
}

// 删除同步任务
function deleteTask(id) {
    if (confirm('确定要删除这个同步任务吗？')) {
        fetch(`/api/tasks/${id}`, { method: 'DELETE' })
            .then(r => r.json())
            .then(result => {
                if (result.success) {
                    showNotification('同步任务删除成功', 'success');
                    loadTasks();
                } else {
                    showNotification('删除失败: ' + result.message, 'error');
                }
            })
            .catch(error => {
                showNotification('删除失败: ' + error.message, 'error');
            });
    }
}

// 运行任务
function runTask(id) {
    if (confirm('确定要运行这个任务吗？')) {
        fetch(`/api/tasks/${id}/run`, { method: 'POST' })
            .then(r => r.json())
            .then(result => {
                if (result.success) {
                    showNotification('任务已启动', 'success');
                    loadTasks();
                } else {
                    showNotification('启动失败: ' + result.message, 'error');
                }
            })
            .catch(error => {
                showNotification('启动失败: ' + error.message, 'error');
            });
    }
}

// 查看任务详情
function viewTask(id) {
    const task = allTasks[id];
    if (!task) {
        showNotification('任务不存在', 'error');
        return;
    }

    showModal('任务详情', `
        <div class="task-details">
            <div class="detail-item">
                <strong>任务名称:</strong> ${task.name}
            </div>
            <div class="detail-item">
                <strong>描述:</strong> ${task.description || '无描述'}
            </div>
            <div class="detail-item">
                <strong>同步模式:</strong> ${task.sync_mode === 'incremental' ? '增量同步' : '完全同步'}
            </div>
            <div class="detail-item">
                <strong>调度类型:</strong> ${getScheduleTypeLabel(task.schedule_type)}
            </div>
            <div class="detail-item">
                <strong>状态:</strong> ${getTaskStatusLabel(task.status || 'idle')}
            </div>
            <div class="detail-item">
                <strong>启用状态:</strong> ${task.enabled ? '已启用' : '已禁用'}
            </div>
        </div>
    `);
}

// 显示模态框
function showModal(title, content) {
    const modalContainer = document.getElementById('modal-container');
    if (!modalContainer) return;

    const modal = document.createElement('div');
    modal.className = 'modal show'; // 添加show类来显示
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title">${title}</div>
                <button class="close-btn" onclick="closeModal()">&times;</button>
            </div>
            <div class="modal-body">
                ${content}
            </div>
        </div>
    `;

    modalContainer.appendChild(modal);

    // 点击背景关闭模态框
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeModal();
        }
    });
}

// 关闭模态框
function closeModal() {
    const modalContainer = document.getElementById('modal-container');
    if (modalContainer) {
        modalContainer.innerHTML = '';
    }
}

// 添加和编辑功能
function showAddSourceModal() {
    showSourceModal('添加数据源', {}, false);
}

function showAddTargetModal() {
    showTargetModal('添加目标存储', {}, false);
}

function showAddTaskModal() {
    // 确保数据源和目标存储数据已加载
    Promise.all([
        loadSourcesIfNeeded(),
        loadTargetsIfNeeded()
    ]).then(() => {
        showTaskModal('创建同步任务', {}, false);
    }).catch(error => {
        console.error('加载存储配置失败:', error);
        showNotification('加载存储配置失败，请稍后重试', 'error');
    });
}

function editSource(id) {
    const source = allSources[id];
    if (!source) {
        showNotification('数据源不存在', 'error');
        return;
    }
    showSourceModal('编辑数据源', source, true, id);
}

function editTarget(id) {
    const target = allTargets[id];
    if (!target) {
        showNotification('目标存储不存在', 'error');
        return;
    }
    showTargetModal('编辑目标存储', target, true, id);
}

function editTask(id) {
    const task = allTasks[id];
    if (!task) {
        showNotification('同步任务不存在', 'error');
        return;
    }

    // 确保数据源和目标存储数据已加载
    Promise.all([
        loadSourcesIfNeeded(),
        loadTargetsIfNeeded()
    ]).then(() => {
        showTaskModal('编辑同步任务', task, true, id);
    }).catch(error => {
        console.error('加载存储配置失败:', error);
        showNotification('加载存储配置失败，请稍后重试', 'error');
    });
}

// 确保数据源已加载
function loadSourcesIfNeeded() {
    return new Promise((resolve, reject) => {
        if (Object.keys(allSources).length > 0) {
            resolve();
            return;
        }

        fetch('/api/sources')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                allSources = data.sources || {};
                resolve();
            } else {
                reject(new Error(data.message || '加载数据源失败'));
            }
        })
        .catch(reject);
    });
}

// 确保目标存储已加载
function loadTargetsIfNeeded() {
    return new Promise((resolve, reject) => {
        if (Object.keys(allTargets).length > 0) {
            resolve();
            return;
        }

        fetch('/api/targets')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                allTargets = data.targets || {};
                resolve();
            } else {
                reject(new Error(data.message || '加载目标存储失败'));
            }
        })
        .catch(reject);
    });
}

// 数据源模态框
function showSourceModal(title, data, isEdit, sourceId) {
    const storageType = data.storage_type || 's3';

    const content = `
        <form id="source-form" class="config-form">
            <div class="form-group">
                <label for="source-name">名称 *</label>
                <input type="text" id="source-name" name="name" value="${data.name || ''}" required>
            </div>

            <div class="form-group">
                <label for="source-description">描述</label>
                <input type="text" id="source-description" name="description" value="${data.description || ''}">
            </div>

            <div class="form-group">
                <label for="source-type">存储类型 *</label>
                <select id="source-type" name="storage_type" onchange="updateSourceFields()" required>
                    <option value="s3" ${storageType === 's3' ? 'selected' : ''}>S3对象存储</option>
                    <option value="sftp" ${storageType === 'sftp' ? 'selected' : ''}>SFTP</option>
                    <option value="ftp" ${storageType === 'ftp' ? 'selected' : ''}>FTP</option>
                    <option value="smb" ${storageType === 'smb' ? 'selected' : ''}>SMB/CIFS</option>
                    <option value="local" ${storageType === 'local' ? 'selected' : ''}>本地存储</option>
                </select>
            </div>

            <div id="source-fields">
                ${getSourceFieldsHtml(storageType, data)}
            </div>

            <div class="form-actions">
                <button type="button" class="btn btn-secondary" onclick="closeModal()">取消</button>
                <button type="button" class="btn btn-primary" onclick="testSourceConnection()">测试连接</button>
                <button type="submit" class="btn btn-primary">${isEdit ? '更新' : '添加'}</button>
            </div>
        </form>
    `;

    showModal(title, content);

    // 绑定表单提交事件
    document.getElementById('source-form').addEventListener('submit', function(e) {
        e.preventDefault();
        saveSource(isEdit, sourceId);
    });
}

// 目标存储模态框
function showTargetModal(title, data, isEdit, targetId) {
    const storageType = data.storage_type || 's3';

    const content = `
        <form id="target-form" class="config-form">
            <div class="form-group">
                <label for="target-name">名称 *</label>
                <input type="text" id="target-name" name="name" value="${data.name || ''}" required>
            </div>

            <div class="form-group">
                <label for="target-description">描述</label>
                <input type="text" id="target-description" name="description" value="${data.description || ''}">
            </div>

            <div class="form-group">
                <label for="target-type">存储类型 *</label>
                <select id="target-type" name="storage_type" onchange="updateTargetFields()" required>
                    <option value="s3" ${storageType === 's3' ? 'selected' : ''}>S3对象存储</option>
                    <option value="sftp" ${storageType === 'sftp' ? 'selected' : ''}>SFTP</option>
                    <option value="ftp" ${storageType === 'ftp' ? 'selected' : ''}>FTP</option>
                    <option value="smb" ${storageType === 'smb' ? 'selected' : ''}>SMB/CIFS</option>
                    <option value="local" ${storageType === 'local' ? 'selected' : ''}>本地存储</option>
                </select>
            </div>

            <div id="target-fields">
                ${getTargetFieldsHtml(storageType, data)}
            </div>

            <div class="form-actions">
                <button type="button" class="btn btn-secondary" onclick="closeModal()">取消</button>
                <button type="button" class="btn btn-primary" onclick="testTargetConnection()">测试连接</button>
                <button type="submit" class="btn btn-primary">${isEdit ? '更新' : '添加'}</button>
            </div>
        </form>
    `;

    showModal(title, content);

    // 绑定表单提交事件
    document.getElementById('target-form').addEventListener('submit', function(e) {
        e.preventDefault();
        saveTarget(isEdit, targetId);
    });
}

// 同步任务模态框
function showTaskModal(title, data, isEdit, taskId) {
    const content = `
        <form id="task-form" class="config-form advanced-task-form">
            <!-- 基本信息 -->
            <div class="form-section">
                <h3>📋 基本信息</h3>
                <div class="form-group">
                    <label for="task-name">任务名称 *</label>
                    <input type="text" id="task-name" name="name" value="${data.name || ''}" required>
                </div>

                <div class="form-group">
                    <label for="task-description">任务描述</label>
                    <textarea id="task-description" name="description" rows="2" placeholder="描述此同步任务的用途和特点">${data.description || ''}</textarea>
                </div>
            </div>

            <!-- 存储配置 -->
            <div class="form-section">
                <h3>💾 存储配置</h3>
                <div class="form-group">
                    <label for="task-source">数据源 *</label>
                    <select id="task-source" name="source_id" required>
                        <option value="">请选择数据源</option>
                        ${Object.entries(allSources).map(([id, source]) =>
                            `<option value="${id}" ${data.source_id === id ? 'selected' : ''}>${source.name} (${getStorageTypeLabel(source.storage_type)})</option>`
                        ).join('')}
                    </select>
                </div>

                <div class="form-group">
                    <label for="task-target">目标存储 *</label>
                    <select id="task-target" name="target_id" required>
                        <option value="">请选择目标存储</option>
                        ${Object.entries(allTargets).map(([id, target]) =>
                            `<option value="${id}" ${data.target_id === id ? 'selected' : ''}>${target.name} (${getStorageTypeLabel(target.storage_type)})</option>`
                        ).join('')}
                    </select>
                </div>

                <div class="form-group">
                    <label for="task-prefix">路径前缀</label>
                    <input type="text" id="task-prefix" name="prefix" value="${data.prefix || ''}"
                           placeholder="data/ 或 backup/2024/">
                    <small>只同步指定前缀路径下的文件，留空表示同步所有文件</small>
                </div>
            </div>

            <!-- 同步设置 -->
            <div class="form-section">
                <h3>🔄 同步设置</h3>
                <div class="form-group">
                    <label for="task-sync-mode">同步模式 *</label>
                    <select id="task-sync-mode" name="sync_mode">
                        <option value="incremental" ${data.sync_mode === 'incremental' ? 'selected' : ''}>增量同步 - 只同步变化的文件</option>
                        <option value="full" ${data.sync_mode === 'full' ? 'selected' : ''}>完全同步 - 同步所有文件</option>
                        <option value="mirror" ${data.sync_mode === 'mirror' ? 'selected' : ''}>镜像同步 - 完全镜像源到目标</option>
                    </select>
                </div>

                <div class="form-group">
                    <label>
                        <input type="checkbox" id="task-delete-extra" name="delete_extra" ${data.delete_extra ? 'checked' : ''}>
                        删除目标中多余的文件
                    </label>
                    <small>启用后会删除目标存储中源存储没有的文件（谨慎使用）</small>
                </div>

                <div class="form-group">
                    <label>
                        <input type="checkbox" id="task-verify-integrity" name="verify_integrity" ${data.verify_integrity !== false ? 'checked' : ''}>
                        启用完整性验证
                    </label>
                    <small>通过文件哈希校验确保传输完整性</small>
                </div>
            </div>

            <!-- 文件过滤 -->
            <div class="form-section">
                <h3>🔍 文件过滤</h3>
                <div class="form-group">
                    <label for="task-file-filter">包含文件规则</label>
                    <input type="text" id="task-file-filter" name="file_filter" value="${data.file_filter || ''}"
                           placeholder="*.jpg,*.png,*.pdf">
                    <small>只同步匹配的文件类型，支持通配符，多个规则用逗号分隔</small>
                </div>

                <div class="form-group">
                    <label for="task-exclude-filter">排除文件规则</label>
                    <input type="text" id="task-exclude-filter" name="exclude_filter" value="${data.exclude_filter || ''}"
                           placeholder="*.tmp,*.log,*.cache">
                    <small>排除不需要同步的文件，支持通配符，多个规则用逗号分隔</small>
                </div>
            </div>

            <!-- 性能设置 -->
            <div class="form-section">
                <h3>⚡ 性能设置</h3>
                <div class="form-row">
                    <div class="form-group">
                        <label for="task-max-workers">并发线程数</label>
                        <input type="number" id="task-max-workers" name="max_workers" value="${data.max_workers || 20}"
                               min="1" max="100">
                        <small>同时处理的文件数量</small>
                    </div>

                    <div class="form-group">
                        <label for="task-bandwidth-limit">带宽限制 (MB/s)</label>
                        <input type="number" id="task-bandwidth-limit" name="bandwidth_limit" value="${data.bandwidth_limit || 0}"
                               min="0">
                        <small>0表示无限制</small>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="task-chunk-threshold">大文件阈值 (MB)</label>
                        <input type="number" id="task-chunk-threshold" name="chunk_threshold" value="${data.chunk_threshold || 100}"
                               min="1">
                        <small>超过此大小的文件将分片传输</small>
                    </div>

                    <div class="form-group">
                        <label for="task-chunk-size">分片大小 (MB)</label>
                        <input type="number" id="task-chunk-size" name="chunk_size" value="${data.chunk_size || 10}"
                               min="1" max="1000">
                        <small>每个分片的大小</small>
                    </div>
                </div>
            </div>

            <!-- 重试设置 -->
            <div class="form-section">
                <h3>🔄 重试设置</h3>
                <div class="form-row">
                    <div class="form-group">
                        <label for="task-retry-times">重试次数</label>
                        <input type="number" id="task-retry-times" name="retry_times" value="${data.retry_times || 5}"
                               min="0" max="20">
                        <small>失败后的重试次数</small>
                    </div>

                    <div class="form-group">
                        <label for="task-retry-delay">重试延迟 (秒)</label>
                        <input type="number" id="task-retry-delay" name="retry_delay" value="${data.retry_delay || 3}"
                               min="1" max="300">
                        <small>重试之间的等待时间</small>
                    </div>
                </div>
            </div>

            <!-- 调度设置 -->
            <div class="form-section">
                <h3>⏰ 调度设置</h3>
                <div class="form-group">
                    <label for="task-schedule-type">调度类型</label>
                    <select id="task-schedule-type" name="schedule_type" onchange="updateScheduleFields()">
                        <option value="manual" ${data.schedule_type === 'manual' ? 'selected' : ''}>手动执行</option>
                        <option value="minutely" ${data.schedule_type === 'minutely' ? 'selected' : ''}>按分钟执行</option>
                        <option value="hourly" ${data.schedule_type === 'hourly' ? 'selected' : ''}>按小时执行</option>
                        <option value="daily" ${data.schedule_type === 'daily' ? 'selected' : ''}>每日执行</option>
                        <option value="weekly" ${data.schedule_type === 'weekly' ? 'selected' : ''}>每周执行</option>
                    </select>
                </div>

                <div id="schedule-fields" style="display: ${data.schedule_type !== 'manual' ? 'block' : 'none'}">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="task-schedule-interval">执行间隔</label>
                            <input type="number" id="task-schedule-interval" name="schedule_interval" value="${data.schedule_interval || 1}"
                                   min="1">
                            <small>每隔多少个时间单位执行一次</small>
                        </div>

                        <div class="form-group">
                            <label for="task-schedule-time">执行时间</label>
                            <input type="time" id="task-schedule-time" name="schedule_time" value="${data.schedule_time || '00:00'}">
                            <small>具体的执行时间点</small>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label>
                        <input type="checkbox" id="task-enabled" name="enabled" ${data.enabled !== false ? 'checked' : ''}>
                        启用任务
                    </label>
                    <small>禁用的任务不会自动执行</small>
                </div>
            </div>

            <div class="form-actions">
                <button type="button" class="btn btn-secondary" onclick="closeModal()">取消</button>
                <button type="button" class="btn btn-secondary" onclick="previewTaskConfig()">预览配置</button>
                <button type="submit" class="btn btn-primary">${isEdit ? '更新任务' : '创建任务'}</button>
            </div>
        </form>
    `;

    showModal(title, content);

    // 绑定表单提交事件
    document.getElementById('task-form').addEventListener('submit', function(e) {
        e.preventDefault();
        saveTask(isEdit, taskId);
    });
}

function loadTaskLogs() {
    console.log('加载任务日志');

    // 加载任务列表到过滤器
    const taskFilter = document.getElementById('taskLogFilter');
    if (taskFilter) {
        taskFilter.innerHTML = '<option value="">所有任务</option>';
        Object.entries(allTasks).forEach(([id, task]) => {
            const option = document.createElement('option');
            option.value = id;
            option.textContent = task.name;
            taskFilter.appendChild(option);
        });
    }

    // 加载执行记录
    fetch('/api/task-executions')
        .then(response => response.json())
        .then(data => {
            const container = document.getElementById('logs-container');
            if (!container) return;

            if (data.success && data.executions && data.executions.length > 0) {
                container.innerHTML = `
                    <div class="log-header-actions">
                        <label class="checkbox-container">
                            <input type="checkbox" id="selectAllExecutions" onchange="toggleAllExecutions(this)">
                            <span class="checkmark"></span>
                            全选
                        </label>
                        <span class="log-count">共 ${data.executions.length} 条记录</span>
                    </div>
                ` + data.executions.map(exec => `
                    <div class="log-execution-item">
                        <div class="log-execution-checkbox">
                            <label class="checkbox-container">
                                <input type="checkbox" class="execution-checkbox" value="${exec.id}">
                                <span class="checkmark"></span>
                            </label>
                        </div>
                        <div class="log-execution-content">
                            <div class="log-execution-header">
                                <h4>${exec.task_name || '未知任务'}</h4>
                                <span class="log-execution-status status-${exec.status || 'idle'}">${getStatusText(exec.status)}</span>
                            </div>
                            <div class="log-execution-details">
                                <span>开始时间: ${exec.start_time ? new Date(exec.start_time).toLocaleString() : '未知'}</span>
                                <span>结束时间: ${exec.end_time ? new Date(exec.end_time).toLocaleString() : '未完成'}</span>
                                <span>耗时: ${calculateDuration(exec.start_time, exec.end_time)}</span>
                                <span>处理文件: ${exec.files_processed || 0} / ${exec.files_total || 0}</span>
                                <span>传输数据: ${formatBytes(exec.bytes_transferred || 0)}</span>
                            </div>
                            <div class="log-execution-actions">
                                <button class="btn btn-view" onclick="viewExecutionLogs('${escapeJsString(exec.id)}')">查看日志</button>
                                <button class="btn btn-danger" onclick="deleteExecution('${escapeJsString(exec.id)}', '${escapeJsString(exec.task_name)}')">删除</button>
                            </div>
                        </div>
                    </div>
                `).join('');
            } else {
                container.innerHTML = '<div class="empty-state"><h3>暂无日志记录</h3><p>还没有任务执行日志</p></div>';
            }
        })
        .catch(error => {
            console.error('加载任务日志失败:', error);
            const container = document.getElementById('logs-container');
            if (container) {
                container.innerHTML = '<div class="empty-state"><h3>加载失败</h3><p>无法获取任务日志</p></div>';
            }
        });
}

function filterTaskLogs() {
    const taskFilter = document.getElementById('taskLogFilter');
    const selectedTaskId = taskFilter ? taskFilter.value : '';

    if (selectedTaskId && selectedTaskId !== 'all') {
        // 过滤特定任务的日志
        fetch(`/api/task-executions?task_id=${selectedTaskId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayTaskExecutions(data.executions);
                    showNotification(`已过滤任务: ${selectedTaskId}`, 'success');
                } else {
                    showNotification('过滤任务日志失败', 'error');
                }
            })
            .catch(error => {
                console.error('过滤任务日志错误:', error);
                showNotification('过滤任务日志时发生错误', 'error');
            });
    } else {
        // 显示所有任务日志
        loadTaskLogs();
    }
}

function viewExecutionLogs(executionId) {
    fetch(`/api/task-logs/${executionId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.logs) {
                const logsHtml = data.logs.map(log => `
                    <div class="log-entry log-${log.level || 'info'}">
                        <span class="log-time">${new Date(log.timestamp).toLocaleString()}</span>
                        <span class="log-message">${log.message}</span>
                    </div>
                `).join('');

                showModal('任务执行日志', `
                    <div class="logs-container">
                        ${logsHtml || '<p>暂无日志内容</p>'}
                    </div>
                `);
            } else {
                showNotification('获取日志失败: ' + (data.message || '未知错误'), 'error');
            }
        })
        .catch(error => {
            showNotification('获取日志失败: ' + error.message, 'error');
        });
}

function deleteExecution(executionId, taskName) {
    if (!confirm(`确定要删除任务"${taskName}"的执行记录吗？\n\n此操作将删除该执行记录及其所有日志，且无法恢复。`)) {
        return;
    }

    fetch(`/api/executions/${executionId}/delete`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('执行记录删除成功', 'success');
            // 重新加载任务日志列表
            loadTaskLogs();
        } else {
            showNotification('删除失败: ' + (data.message || '未知错误'), 'error');
        }
    })
    .catch(error => {
        showNotification('删除失败: ' + error.message, 'error');
    });
}

function batchDeleteExecutions() {
    const checkboxes = document.querySelectorAll('.execution-checkbox:checked');
    if (checkboxes.length === 0) {
        showNotification('请选择要删除的执行记录', 'warning');
        return;
    }

    if (!confirm(`确定要删除选中的 ${checkboxes.length} 条执行记录吗？\n\n此操作将删除这些执行记录及其所有日志，且无法恢复。`)) {
        return;
    }

    const executionIds = Array.from(checkboxes).map(cb => cb.value);

    // 逐个删除（因为后端API是单个删除）
    let deletedCount = 0;
    let failedCount = 0;

    const deleteNext = (index) => {
        if (index >= executionIds.length) {
            showNotification(`批量删除完成: 成功 ${deletedCount} 个，失败 ${failedCount} 个`,
                           failedCount === 0 ? 'success' : 'warning');
            loadTaskLogs();
            return;
        }

        fetch(`/api/executions/${executionIds[index]}/delete`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                deletedCount++;
            } else {
                failedCount++;
            }
            deleteNext(index + 1);
        })
        .catch(error => {
            failedCount++;
            deleteNext(index + 1);
        });
    };

    deleteNext(0);
}

function toggleAllExecutions(checkbox) {
    const executionCheckboxes = document.querySelectorAll('.execution-checkbox');
    executionCheckboxes.forEach(cb => {
        cb.checked = checkbox.checked;
    });
}

function calculateDuration(startTime, endTime) {
    if (!startTime) return '未知';
    if (!endTime) return '进行中';

    try {
        const start = new Date(startTime);
        const end = new Date(endTime);
        const duration = end - start;

        if (duration < 1000) return '< 1秒';
        if (duration < 60000) return `${Math.round(duration / 1000)}秒`;
        if (duration < 3600000) return `${Math.round(duration / 60000)}分钟`;
        return `${Math.round(duration / 3600000)}小时`;
    } catch (e) {
        return '未知';
    }
}

function formatBytes(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function cleanOldLogs() {
    if (!confirm('确定要清理30天前的旧日志吗？\n\n此操作将删除30天前的执行记录和日志，且无法恢复。')) {
        return;
    }

    fetch('/api/clean-logs', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('旧日志清理成功', 'success');
            loadTaskLogs();
        } else {
            showNotification('清理失败: ' + (data.message || '未知错误'), 'error');
        }
    })
    .catch(error => {
        showNotification('清理失败: ' + error.message, 'error');
    });
}

// 文件浏览器功能
let currentBrowserStorageType = '';
let currentBrowserStorageId = '';
let currentBrowserPath = '';

// 安全转义JavaScript字符串
function escapeJsString(str) {
    if (!str) return '';
    return str.replace(/\\/g, '\\\\')
              .replace(/'/g, "\\'")
              .replace(/"/g, '\\"')
              .replace(/\n/g, '\\n')
              .replace(/\r/g, '\\r')
              .replace(/\t/g, '\\t');
}

function showFileBrowserModal(storageType) {
    console.log('显示文件浏览器模态框:', storageType);

    currentBrowserStorageType = storageType;

    const modal = document.getElementById('fileBrowserModal');
    const title = document.getElementById('fileBrowserTitle');
    const storageSelect = document.getElementById('fileBrowserStorageSelect');

    if (!modal) {
        console.error('找不到文件浏览器模态框元素');
        showNotification('文件浏览器初始化失败', 'error');
        return;
    }

    if (!title || !storageSelect) {
        console.error('找不到文件浏览器必要元素');
        showNotification('文件浏览器初始化失败', 'error');
        return;
    }

    title.textContent = `📁 ${storageType === 'source' ? '数据源' : '目标存储'} 文件浏览器`;

    // 确保数据已加载
    const loadPromise = storageType === 'source' ? loadSourcesIfNeeded() : loadTargetsIfNeeded();

    loadPromise.then(() => {
        // 清空并填充存储选择器
        storageSelect.innerHTML = '<option value="">请选择存储</option>';

        const storages = storageType === 'source' ? allSources : allTargets;
        console.log('可用存储:', Object.keys(storages).length);

        Object.entries(storages).forEach(([id, storage]) => {
            const option = document.createElement('option');
            option.value = id;
            option.textContent = `${storage.name} (${storage.storage_type || storage.type})`;
            storageSelect.appendChild(option);
        });

        // 重置状态
        currentBrowserStorageId = '';
        currentBrowserPath = '';

        const fileList = document.getElementById('fileList');
        const downloadBtn = document.getElementById('downloadBtn');

        if (fileList) {
            fileList.innerHTML = '<div class="loading-placeholder">请选择存储以浏览文件</div>';
        }

        if (downloadBtn) {
            downloadBtn.disabled = true;
        }

        modal.classList.add('show');
        console.log('文件浏览器模态框已显示');

    }).catch(error => {
        console.error('加载存储数据失败:', error);
        showNotification('加载存储数据失败: ' + error.message, 'error');
    });
}

function closeFileBrowserModal() {
    document.getElementById('fileBrowserModal').classList.remove('show');
}

function onStorageChange() {
    const storageSelect = document.getElementById('fileBrowserStorageSelect');
    currentBrowserStorageId = storageSelect.value;

    if (currentBrowserStorageId) {
        currentBrowserPath = '';
        loadFileBrowser();
    } else {
        document.getElementById('fileList').innerHTML = '<div class="loading-placeholder">请选择存储以浏览文件</div>';
    }
}

function loadFileBrowser(path = '') {
    if (!currentBrowserStorageId) return;

    currentBrowserPath = path;

    const fileList = document.getElementById('fileList');
    fileList.innerHTML = '<div class="loading-placeholder">正在加载文件列表...</div>';

    fetch(`/api/browse/${currentBrowserStorageType}/${currentBrowserStorageId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ path: path })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayFiles(data.files, data.current_path, data.parent_path);
            updateBreadcrumb(data.current_path);
        } else {
            fileList.innerHTML = `<div class="error-placeholder">加载失败: ${data.message}</div>`;
        }
    })
    .catch(error => {
        fileList.innerHTML = `<div class="error-placeholder">加载失败: ${error.message}</div>`;
    });
}

function displayFiles(files, currentPath, parentPath) {
    const fileList = document.getElementById('fileList');
    const fileCount = document.getElementById('fileCount');

    fileCount.textContent = `${files.length} 个项目`;

    if (files.length === 0) {
        fileList.innerHTML = '<div class="empty-placeholder">此目录为空</div>';
        return;
    }

    let html = '';

    // 添加返回上级目录选项
    if (parentPath !== null) {
        html += `
            <div class="file-item directory-item" onclick="navigateToPath('${escapeJsString(parentPath)}')">
                <div class="file-icon">📁</div>
                <div class="file-info">
                    <div class="file-name">.. (返回上级)</div>
                    <div class="file-details">目录</div>
                </div>
            </div>
        `;
    }

    // 显示文件和目录
    files.forEach(file => {
        const isDirectory = file.type === 'directory';
        const icon = isDirectory ? '📁' : getFileIcon(file.name);
        const size = isDirectory ? '' : formatBytes(file.size);
        const lastModified = file.last_modified ? new Date(file.last_modified).toLocaleString() : '';

        html += `
            <div class="file-item ${isDirectory ? 'directory-item' : 'file-item'}">
                <div class="file-checkbox" onclick="event.stopPropagation();">
                    <label class="checkbox-container">
                        <input type="checkbox" class="file-checkbox-input" value="${escapeJsString(file.path)}"
                               ${isDirectory ? 'disabled' : ''} onchange="updateDownloadButton()">
                        <span class="checkmark"></span>
                    </label>
                </div>
                <div class="file-content" ${isDirectory ? `onclick="navigateToPath('${escapeJsString(file.path)}')"` : ''}>
                    <div class="file-icon">${icon}</div>
                    <div class="file-info">
                        <div class="file-name" title="${escapeJsString(file.name)}">${escapeJsString(file.name)}</div>
                        <div class="file-details">
                            ${size ? `大小: ${size}` : ''}
                            ${lastModified ? ` | 修改时间: ${lastModified}` : ''}
                            ${file.mime_type ? ` | 类型: ${escapeJsString(file.mime_type)}` : ''}
                        </div>
                    </div>
                </div>
            </div>
        `;
    });

    fileList.innerHTML = html;
}

function getFileIcon(filename) {
    const ext = filename.split('.').pop().toLowerCase();
    const iconMap = {
        'txt': '📄', 'doc': '📄', 'docx': '📄', 'pdf': '📄',
        'jpg': '🖼️', 'jpeg': '🖼️', 'png': '🖼️', 'gif': '🖼️', 'bmp': '🖼️',
        'mp4': '🎬', 'avi': '🎬', 'mov': '🎬', 'wmv': '🎬',
        'mp3': '🎵', 'wav': '🎵', 'flac': '🎵',
        'zip': '📦', 'rar': '📦', '7z': '📦', 'tar': '📦',
        'js': '📜', 'html': '📜', 'css': '📜', 'py': '📜', 'java': '📜',
        'xlsx': '📊', 'xls': '📊', 'csv': '📊'
    };
    return iconMap[ext] || '📄';
}

function updateBreadcrumb(currentPath) {
    const breadcrumb = document.getElementById('fileBrowserBreadcrumb');

    let html = '<span class="breadcrumb-item" onclick="navigateToPath(\'\')">🏠 根目录</span>';

    if (currentPath) {
        const parts = currentPath.split('/').filter(part => part);
        let path = '';

        parts.forEach((part, index) => {
            path += (path ? '/' : '') + part;
            html += ` <span class="breadcrumb-separator">/</span> `;
            html += `<span class="breadcrumb-item" onclick="navigateToPath('${escapeJsString(path)}')">${escapeJsString(part)}</span>`;
        });
    }

    breadcrumb.innerHTML = html;
}

function navigateToPath(path) {
    loadFileBrowser(path);
}

function refreshFileBrowser() {
    loadFileBrowser(currentBrowserPath);
}

function toggleAllFiles(checkbox) {
    const fileCheckboxes = document.querySelectorAll('.file-checkbox-input:not(:disabled)');
    fileCheckboxes.forEach(cb => {
        cb.checked = checkbox.checked;
    });
    updateDownloadButton();
}

function updateDownloadButton() {
    const selectedFiles = document.querySelectorAll('.file-checkbox-input:checked');
    const downloadBtn = document.getElementById('downloadBtn');
    downloadBtn.disabled = selectedFiles.length === 0;
    downloadBtn.textContent = selectedFiles.length > 0 ?
        `📦 下载选中文件 (${selectedFiles.length})` : '📦 下载选中文件';
}

function downloadSelectedFiles() {
    const selectedFiles = Array.from(document.querySelectorAll('.file-checkbox-input:checked'))
                               .map(cb => cb.value);

    if (selectedFiles.length === 0) {
        showNotification('请选择要下载的文件', 'warning');
        return;
    }

    const downloadName = prompt('请输入下载文件名（不含扩展名）:',
                               `download_${new Date().toISOString().slice(0, 10)}`);

    if (!downloadName) return;

    showNotification('正在准备下载，请稍候...', 'info');

    fetch(`/api/download/${currentBrowserStorageType}/${currentBrowserStorageId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            file_paths: selectedFiles,
            download_name: downloadName + '.zip'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(`文件打包成功！已下载 ${data.downloaded_count} 个文件`, 'success');

            // 创建下载链接
            const link = document.createElement('a');
            link.href = data.download_url;
            link.download = data.zip_filename;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            // 清除选择
            document.getElementById('selectAllFiles').checked = false;
            toggleAllFiles(document.getElementById('selectAllFiles'));
        } else {
            showNotification('下载失败: ' + data.message, 'error');
        }
    })
    .catch(error => {
        showNotification('下载失败: ' + error.message, 'error');
    });
}

// 获取存储字段HTML
function getSourceFieldsHtml(storageType, data) {
    return getStorageFieldsHtml(storageType, data, 'source');
}

function getTargetFieldsHtml(storageType, data) {
    return getStorageFieldsHtml(storageType, data, 'target');
}

function getStorageFieldsHtml(storageType, data, prefix) {
    switch(storageType) {
        case 's3':
            return `
                <div class="form-group">
                    <label for="${prefix}-endpoint">端点URL *</label>
                    <input type="text" id="${prefix}-endpoint" name="endpoint" value="${data.endpoint || ''}"
                           placeholder="https://s3.amazonaws.com 或 https://oss-cn-hangzhou.aliyuncs.com" required>
                    <small>支持AWS S3、阿里云OSS、腾讯云COS等S3兼容存储</small>
                </div>
                <div class="form-group">
                    <label for="${prefix}-access-key">访问密钥ID *</label>
                    <input type="text" id="${prefix}-access-key" name="access_key" value="${data.access_key || ''}" required>
                </div>
                <div class="form-group">
                    <label for="${prefix}-secret-key">访问密钥Secret *</label>
                    <input type="password" id="${prefix}-secret-key" name="secret_key" value="${data.secret_key || ''}" required>
                </div>
                <div class="form-group">
                    <label for="${prefix}-bucket">存储桶名称 *</label>
                    <div style="display: flex; gap: 10px;">
                        <input type="text" id="${prefix}-bucket" name="bucket" value="${data.bucket || ''}" required style="flex: 1;">
                        <button type="button" onclick="listBuckets('${prefix}')" class="btn btn-secondary" style="white-space: nowrap;">📋 列出桶</button>
                    </div>
                </div>
                <div class="form-group">
                    <label for="${prefix}-region">区域</label>
                    <input type="text" id="${prefix}-region" name="region" value="${data.region || ''}"
                           placeholder="us-east-1 或 cn-hangzhou">
                </div>
            `;
        case 'sftp':
            return `
                <div class="form-group">
                    <label for="${prefix}-hostname">主机名/IP地址 *</label>
                    <input type="text" id="${prefix}-hostname" name="hostname" value="${data.hostname || ''}"
                           placeholder="************* 或 sftp.example.com" required>
                </div>
                <div class="form-group">
                    <label for="${prefix}-port">端口</label>
                    <input type="number" id="${prefix}-port" name="port" value="${data.port || 22}" min="1" max="65535">
                </div>
                <div class="form-group">
                    <label for="${prefix}-username">用户名 *</label>
                    <input type="text" id="${prefix}-username" name="username" value="${data.username || ''}" required>
                </div>
                <div class="form-group">
                    <label for="${prefix}-password">密码</label>
                    <input type="password" id="${prefix}-password" name="password" value="${data.password || ''}">
                    <small>如果使用私钥认证，密码可以为空</small>
                </div>
                <div class="form-group">
                    <label for="${prefix}-private-key-path">私钥文件路径</label>
                    <input type="text" id="${prefix}-private-key-path" name="private_key_path" value="${data.private_key_path || ''}"
                           placeholder="/path/to/private_key">
                </div>
                <div class="form-group">
                    <label for="${prefix}-private-key-passphrase">私钥密码</label>
                    <input type="password" id="${prefix}-private-key-passphrase" name="private_key_passphrase" value="${data.private_key_passphrase || ''}">
                </div>
                <div class="form-group">
                    <label for="${prefix}-root-path">根路径</label>
                    <input type="text" id="${prefix}-root-path" name="root_path" value="${data.root_path || '/'}"
                           placeholder="/home/<USER>/data">
                </div>
            `;
        case 'ftp':
            return `
                <div class="form-group">
                    <label for="${prefix}-hostname">主机名/IP地址 *</label>
                    <input type="text" id="${prefix}-hostname" name="hostname" value="${data.hostname || ''}"
                           placeholder="************* 或 ftp.example.com" required>
                </div>
                <div class="form-group">
                    <label for="${prefix}-port">端口</label>
                    <input type="number" id="${prefix}-port" name="port" value="${data.port || 21}" min="1" max="65535">
                </div>
                <div class="form-group">
                    <label for="${prefix}-username">用户名 *</label>
                    <input type="text" id="${prefix}-username" name="username" value="${data.username || ''}" required>
                </div>
                <div class="form-group">
                    <label for="${prefix}-password">密码</label>
                    <input type="password" id="${prefix}-password" name="password" value="${data.password || ''}">
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="${prefix}-use-tls" name="use_tls" ${data.use_tls ? 'checked' : ''}>
                        使用TLS加密 (FTPS)
                    </label>
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="${prefix}-passive-mode" name="passive_mode" ${data.passive_mode !== false ? 'checked' : ''}>
                        被动模式
                    </label>
                </div>
                <div class="form-group">
                    <label for="${prefix}-root-path">根路径</label>
                    <input type="text" id="${prefix}-root-path" name="root_path" value="${data.root_path || '/'}"
                           placeholder="/data">
                </div>
            `;
        case 'smb':
            return `
                <div class="form-group">
                    <label for="${prefix}-hostname">主机名/IP地址 *</label>
                    <input type="text" id="${prefix}-hostname" name="hostname" value="${data.hostname || ''}"
                           placeholder="************* 或 server.domain.com" required>
                </div>
                <div class="form-group">
                    <label for="${prefix}-port">端口</label>
                    <input type="number" id="${prefix}-port" name="port" value="${data.port || 445}" min="1" max="65535">
                </div>
                <div class="form-group">
                    <label for="${prefix}-username">用户名 *</label>
                    <input type="text" id="${prefix}-username" name="username" value="${data.username || ''}"
                           placeholder="user 或 domain\\user" required>
                </div>
                <div class="form-group">
                    <label for="${prefix}-password">密码 *</label>
                    <input type="password" id="${prefix}-password" name="password" value="${data.password || ''}" required>
                </div>
                <div class="form-group">
                    <label for="${prefix}-domain">域</label>
                    <input type="text" id="${prefix}-domain" name="domain" value="${data.domain || ''}"
                           placeholder="WORKGROUP 或 DOMAIN">
                </div>
                <div class="form-group">
                    <label for="${prefix}-share-name">共享名称 *</label>
                    <input type="text" id="${prefix}-share-name" name="share_name" value="${data.share_name || ''}"
                           placeholder="SharedFolder" required>
                </div>
                <div class="form-group">
                    <label for="${prefix}-root-path">根路径</label>
                    <input type="text" id="${prefix}-root-path" name="root_path" value="${data.root_path || '/'}"
                           placeholder="/backup">
                </div>
            `;
        case 'local':
            return `
                <div class="form-group">
                    <label for="${prefix}-root-path">根路径 *</label>
                    <input type="text" id="${prefix}-root-path" name="root_path" value="${data.root_path || ''}"
                           placeholder="C:\\Data 或 /mnt/nas/data" required>
                    <small>支持本地文件系统和挂载的NAS存储</small>
                </div>
            `;
        default:
            return '<p>请选择存储类型</p>';
    }
}

// 更新字段
function updateSourceFields() {
    const storageType = document.getElementById('source-type').value;
    const fieldsContainer = document.getElementById('source-fields');
    fieldsContainer.innerHTML = getSourceFieldsHtml(storageType, {});
}

function updateTargetFields() {
    const storageType = document.getElementById('target-type').value;
    const fieldsContainer = document.getElementById('target-fields');
    fieldsContainer.innerHTML = getTargetFieldsHtml(storageType, {});
}

// 保存数据源
function saveSource(isEdit, sourceId) {
    const form = document.getElementById('source-form');
    const formData = new FormData(form);
    const data = {};

    // 处理普通字段
    for (let [key, value] of formData.entries()) {
        data[key] = value;
    }

    // 处理复选框字段（FTP类型）
    const storageType = data.storage_type;
    if (storageType === 'ftp') {
        data.use_tls = document.getElementById('source-use-tls')?.checked || false;
        data.passive_mode = document.getElementById('source-passive-mode')?.checked !== false;
    }

    // 转换数字字段
    if (data.port) {
        data.port = parseInt(data.port);
    }

    const url = isEdit ? `/api/sources/${sourceId}` : '/api/sources';
    const method = isEdit ? 'PUT' : 'POST';

    fetch(url, {
        method: method,
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            showNotification(result.message, 'success');
            closeModal();
            loadSources();
        } else {
            showNotification(result.message, 'error');
        }
    })
    .catch(error => {
        showNotification('保存失败: ' + error.message, 'error');
    });
}

// 保存目标存储
function saveTarget(isEdit, targetId) {
    const form = document.getElementById('target-form');
    const formData = new FormData(form);
    const data = {};

    // 处理普通字段
    for (let [key, value] of formData.entries()) {
        data[key] = value;
    }

    // 处理复选框字段（FTP类型）
    const storageType = data.storage_type;
    if (storageType === 'ftp') {
        data.use_tls = document.getElementById('target-use-tls')?.checked || false;
        data.passive_mode = document.getElementById('target-passive-mode')?.checked !== false;
    }

    // 转换数字字段
    if (data.port) {
        data.port = parseInt(data.port);
    }

    const url = isEdit ? `/api/targets/${targetId}` : '/api/targets';
    const method = isEdit ? 'PUT' : 'POST';

    fetch(url, {
        method: method,
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            showNotification(result.message, 'success');
            closeModal();
            loadTargets();
        } else {
            showNotification(result.message, 'error');
        }
    })
    .catch(error => {
        showNotification('保存失败: ' + error.message, 'error');
    });
}

// 保存同步任务
function saveTask(isEdit, taskId) {
    const form = document.getElementById('task-form');
    const formData = new FormData(form);
    const data = {};

    // 处理普通字段
    for (let [key, value] of formData.entries()) {
        data[key] = value;
    }

    // 处理复选框字段
    data.enabled = document.getElementById('task-enabled')?.checked !== false;
    data.delete_extra = document.getElementById('task-delete-extra')?.checked || false;
    data.verify_integrity = document.getElementById('task-verify-integrity')?.checked !== false;

    // 转换数字字段
    const numberFields = ['max_workers', 'bandwidth_limit', 'chunk_threshold', 'chunk_size', 'retry_times', 'retry_delay', 'schedule_interval'];
    numberFields.forEach(field => {
        if (data[field]) {
            data[field] = parseInt(data[field]);
        }
    });

    // 设置默认值
    if (!data.prefix) data.prefix = '';
    if (!data.file_filter) data.file_filter = '';
    if (!data.exclude_filter) data.exclude_filter = '';
    if (!data.sync_mode) data.sync_mode = 'incremental';
    if (!data.schedule_type) data.schedule_type = 'manual';
    if (!data.schedule_time) data.schedule_time = '00:00';

    // 添加时间戳
    if (!isEdit) {
        data.created_at = new Date().toISOString();
    }
    data.updated_at = new Date().toISOString();

    const url = isEdit ? `/api/tasks/${taskId}` : '/api/tasks';
    const method = isEdit ? 'PUT' : 'POST';

    fetch(url, {
        method: method,
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            showNotification(result.message, 'success');
            closeModal();
            loadTasks();
        } else {
            showNotification(result.message, 'error');
        }
    })
    .catch(error => {
        showNotification('保存失败: ' + error.message, 'error');
    });
}

// 更新调度字段显示
function updateScheduleFields() {
    const scheduleType = document.getElementById('task-schedule-type').value;
    const scheduleFields = document.getElementById('schedule-fields');

    if (scheduleType === 'manual') {
        scheduleFields.style.display = 'none';
    } else {
        scheduleFields.style.display = 'block';
    }
}

// 预览任务配置
function previewTaskConfig() {
    const form = document.getElementById('task-form');
    const formData = new FormData(form);
    const data = {};

    for (let [key, value] of formData.entries()) {
        data[key] = value;
    }

    // 处理复选框
    data.enabled = document.getElementById('task-enabled')?.checked !== false;
    data.delete_extra = document.getElementById('task-delete-extra')?.checked || false;
    data.verify_integrity = document.getElementById('task-verify-integrity')?.checked !== false;

    const sourceName = allSources[data.source_id]?.name || '未选择';
    const targetName = allTargets[data.target_id]?.name || '未选择';

    const previewHtml = `
        <div class="task-preview">
            <h4>📋 任务配置预览</h4>
            <div class="preview-section">
                <h5>基本信息</h5>
                <p><strong>任务名称:</strong> ${data.name || '未设置'}</p>
                <p><strong>描述:</strong> ${data.description || '无'}</p>
                <p><strong>数据源:</strong> ${sourceName}</p>
                <p><strong>目标存储:</strong> ${targetName}</p>
            </div>

            <div class="preview-section">
                <h5>同步设置</h5>
                <p><strong>同步模式:</strong> ${getSyncModeLabel(data.sync_mode)}</p>
                <p><strong>路径前缀:</strong> ${data.prefix || '全部文件'}</p>
                <p><strong>删除多余文件:</strong> ${data.delete_extra ? '是' : '否'}</p>
                <p><strong>完整性验证:</strong> ${data.verify_integrity ? '是' : '否'}</p>
            </div>

            <div class="preview-section">
                <h5>文件过滤</h5>
                <p><strong>包含规则:</strong> ${data.file_filter || '全部文件'}</p>
                <p><strong>排除规则:</strong> ${data.exclude_filter || '无'}</p>
            </div>

            <div class="preview-section">
                <h5>性能设置</h5>
                <p><strong>并发线程:</strong> ${data.max_workers || 20}</p>
                <p><strong>带宽限制:</strong> ${data.bandwidth_limit || 0} MB/s ${data.bandwidth_limit == 0 ? '(无限制)' : ''}</p>
                <p><strong>大文件阈值:</strong> ${data.chunk_threshold || 100} MB</p>
                <p><strong>分片大小:</strong> ${data.chunk_size || 10} MB</p>
            </div>

            <div class="preview-section">
                <h5>调度设置</h5>
                <p><strong>调度类型:</strong> ${getScheduleTypeLabel(data.schedule_type)}</p>
                ${data.schedule_type !== 'manual' ? `
                    <p><strong>执行间隔:</strong> ${data.schedule_interval || 1}</p>
                    <p><strong>执行时间:</strong> ${data.schedule_time || '00:00'}</p>
                ` : ''}
                <p><strong>任务状态:</strong> ${data.enabled ? '启用' : '禁用'}</p>
            </div>
        </div>
    `;

    showModal('任务配置预览', previewHtml);
}

function getSyncModeLabel(mode) {
    const modeMap = {
        'incremental': '增量同步',
        'full': '完全同步',
        'mirror': '镜像同步'
    };
    return modeMap[mode] || mode;
}

// 测试连接
function testSourceConnection() {
    testConnection('source');
}

function testTargetConnection() {
    testConnection('target');
}

function testConnection(type) {
    const form = document.getElementById(`${type}-form`);
    const formData = new FormData(form);
    const data = {};

    for (let [key, value] of formData.entries()) {
        data[key] = value;
    }

    fetch('/api/test-connection', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            showNotification(result.message, 'success');
        } else {
            showNotification(result.message, 'error');
        }
    })
    .catch(error => {
        showNotification('连接测试失败: ' + error.message, 'error');
    });
}

function loadOptimizationConfig() {
    fetch('/api/optimization-config')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.config) {
                const config = data.config;

                // 更新表单字段
                if (document.getElementById('max_workers')) {
                    document.getElementById('max_workers').value = config.max_workers || 8;
                }
                if (document.getElementById('chunk_size_mb')) {
                    document.getElementById('chunk_size_mb').value = config.chunk_size_mb || 20;
                }
                if (document.getElementById('retry_times')) {
                    document.getElementById('retry_times').value = config.retry_times || 3;
                }
                if (document.getElementById('retry_delay')) {
                    document.getElementById('retry_delay').value = config.retry_delay || 2;
                }
                if (document.getElementById('enable_parallel_scan')) {
                    document.getElementById('enable_parallel_scan').checked = config.enable_parallel_scan !== false;
                }
                if (document.getElementById('enable_cache')) {
                    document.getElementById('enable_cache').checked = config.enable_cache !== false;
                }
                if (document.getElementById('cache_ttl_hours')) {
                    document.getElementById('cache_ttl_hours').value = config.cache_ttl_hours || 24;
                }
                if (document.getElementById('verify_integrity')) {
                    document.getElementById('verify_integrity').checked = config.verify_integrity !== false;
                }

                console.log('优化配置加载成功:', config);
            } else {
                console.error('加载优化配置失败:', data);
                showNotification('加载优化配置失败', 'error');
            }
        })
        .catch(error => {
            console.error('加载优化配置错误:', error);
            showNotification('加载优化配置时发生错误', 'error');
        });
}

function applyOptimizationPreset(preset) {
    const presets = {
        'high_performance': {
            max_workers: 20,
            chunk_size_mb: 50,
            retry_times: 5,
            retry_delay: 1,
            enable_parallel_scan: true,
            enable_cache: true,
            cache_ttl_hours: 12,
            verify_integrity: true
        },
        'balanced': {
            max_workers: 8,
            chunk_size_mb: 20,
            retry_times: 3,
            retry_delay: 2,
            enable_parallel_scan: true,
            enable_cache: true,
            cache_ttl_hours: 24,
            verify_integrity: true
        },
        'compatible': {
            max_workers: 4,
            chunk_size_mb: 10,
            retry_times: 5,
            retry_delay: 3,
            enable_parallel_scan: false,
            enable_cache: false,
            cache_ttl_hours: 48,
            verify_integrity: true
        }
    };

    if (presets[preset]) {
        const config = presets[preset];

        // 更新表单字段
        document.getElementById('max_workers').value = config.max_workers;
        document.getElementById('chunk_size_mb').value = config.chunk_size_mb;
        document.getElementById('retry_times').value = config.retry_times;
        if (document.getElementById('retry_delay')) {
            document.getElementById('retry_delay').value = config.retry_delay;
        }
        document.getElementById('enable_parallel_scan').checked = config.enable_parallel_scan;
        document.getElementById('enable_cache').checked = config.enable_cache;
        if (document.getElementById('cache_ttl_hours')) {
            document.getElementById('cache_ttl_hours').value = config.cache_ttl_hours;
        }
        if (document.getElementById('verify_integrity')) {
            document.getElementById('verify_integrity').checked = config.verify_integrity;
        }

        const presetNames = {
            'high_performance': '高性能',
            'balanced': '平衡',
            'compatible': '兼容'
        };

        showNotification(`已应用${presetNames[preset]}配置预设`, 'success');
    } else {
        showNotification('未知的配置预设', 'error');
    }
}

function saveOptimizationConfig() {
    const config = {
        max_workers: parseInt(document.getElementById('max_workers').value),
        chunk_size_mb: parseInt(document.getElementById('chunk_size_mb').value),
        retry_times: parseInt(document.getElementById('retry_times').value),
        enable_parallel_scan: document.getElementById('enable_parallel_scan').checked,
        enable_cache: document.getElementById('enable_cache').checked
    };

    // 添加可选字段（如果存在的话）
    const retryDelayElement = document.getElementById('retry_delay');
    if (retryDelayElement) {
        config.retry_delay = parseFloat(retryDelayElement.value);
    }

    const cacheTtlElement = document.getElementById('cache_ttl_hours');
    if (cacheTtlElement) {
        config.cache_ttl_hours = parseInt(cacheTtlElement.value);
    }

    const verifyIntegrityElement = document.getElementById('verify_integrity');
    if (verifyIntegrityElement) {
        config.verify_integrity = verifyIntegrityElement.checked;
    }

    fetch('/api/optimization-config', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(config)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('优化配置保存成功', 'success');
        } else {
            showNotification(data.message || '保存配置失败', 'error');
        }
    })
    .catch(error => {
        console.error('保存配置错误:', error);
        showNotification('保存配置时发生错误', 'error');
    });
}

function resetOptimizationConfig() {
    if (confirm('确定要重置为默认配置吗？')) {
        applyOptimizationPreset('balanced');
        showNotification('已重置为默认配置', 'info');
    }
}

// 全局错误处理
window.addEventListener('error', function(e) {
    console.error('JavaScript错误:', e.error);
    showNotification('发生了一个错误，请查看控制台', 'error');
});

// 全局未处理的Promise拒绝
window.addEventListener('unhandledrejection', function(e) {
    console.error('未处理的Promise拒绝:', e.reason);
    showNotification('网络请求失败，请检查连接', 'error');
});

// 全局变量保存当前的目标输入字段
let currentBucketTarget = null;

// S3存储桶列表功能
function listBuckets(prefix) {
    const endpointInput = document.getElementById(`${prefix}-endpoint`);
    const accessKeyInput = document.getElementById(`${prefix}-access-key`);
    const secretKeyInput = document.getElementById(`${prefix}-secret-key`);
    const regionInput = document.getElementById(`${prefix}-region`);
    const bucketInput = document.getElementById(`${prefix}-bucket`);

    if (!endpointInput || !accessKeyInput || !secretKeyInput || !regionInput || !bucketInput) {
        showNotification('无法找到必要的输入字段', 'error');
        return;
    }

    // 保存目标输入字段的引用，这样即使DOM被模态框覆盖也能找到
    currentBucketTarget = {
        inputElement: bucketInput,
        inputId: `${prefix}-bucket`,
        prefix: prefix
    };

    console.log('保存目标输入字段引用:', currentBucketTarget);

    const data = {
        endpoint: endpointInput.value,
        access_key: accessKeyInput.value,
        secret_key: secretKeyInput.value,
        region: regionInput.value
    };

    if (!data.endpoint || !data.access_key || !data.secret_key || !data.region) {
        showNotification('请先填写端点URL、Access Key、Secret Key和区域信息', 'warning');
        return;
    }

    // 显示加载状态
    showBucketLoadingModal();

    fetch('/api/list-buckets', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        closeBucketModal();
        if (result.success && result.buckets.length > 0) {
            showBucketSelectionModal(result.buckets, `${prefix}-bucket`);
        } else {
            showNotification(result.message || '未找到可用的存储桶', 'warning');
        }
    })
    .catch(error => {
        closeBucketModal();
        console.error('获取存储桶列表失败:', error);
        showNotification('获取存储桶列表失败，请检查网络连接', 'error');
    });
}

function showBucketLoadingModal() {
    const content = `
        <div style="text-align: center; padding: 40px;">
            <div style="font-size: 2rem; margin-bottom: 20px;">⏳</div>
            <div style="font-size: 1.2rem; color: #666;">正在获取存储桶列表...</div>
            <div style="margin-top: 15px; color: #999;">请稍候，这可能需要几秒钟</div>
        </div>
    `;
    showModal('获取存储桶列表', content);
}

function showBucketSelectionModal(buckets, targetInputId) {
    const bucketListHtml = buckets.map(bucket => `
        <div style="
            padding: 12px 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            margin: 8px 0;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
        "
        onmouseover="this.style.background='#f0f8ff'; this.style.borderColor='#ff6b35';"
        onmouseout="this.style.background='white'; this.style.borderColor='#e0e0e0';"
        onclick="selectBucket('${bucket}', '${targetInputId}')">
            <div style="font-weight: 600; color: #333;">📦 ${bucket}</div>
        </div>
    `).join('');

    const content = `
        <div style="max-height: 400px; overflow-y: auto;">
            <div style="margin-bottom: 20px; color: #666;">
                找到 ${buckets.length} 个存储桶，请选择一个：
            </div>
            ${bucketListHtml}
        </div>
        <div style="margin-top: 20px; text-align: center;">
            <button type="button" class="btn btn-secondary" onclick="closeModal()">取消</button>
        </div>
    `;

    showModal('选择存储桶', content);
}

function selectBucket(bucketName, targetInputId) {
    console.log('选择存储桶:', bucketName, '目标字段ID:', targetInputId);
    console.log('当前保存的目标字段:', currentBucketTarget);

    // 先关闭模态框，避免DOM查找问题
    closeModal();

    // 等待一小段时间让模态框完全关闭
    setTimeout(() => {
        let success = false;

        // 方案1：使用保存的输入字段引用
        if (currentBucketTarget && currentBucketTarget.inputElement) {
            try {
                // 重新验证元素是否还在DOM中
                if (document.contains(currentBucketTarget.inputElement)) {
                    currentBucketTarget.inputElement.value = bucketName;
                    success = true;
                    console.log('使用保存的引用成功设置存储桶名称');
                } else {
                    console.warn('保存的输入字段引用已失效');
                }
            } catch (error) {
                console.warn('使用保存的输入字段引用失败:', error);
            }
        }

        // 方案2：通过ID查找
        if (!success) {
            const targetInput = document.getElementById(targetInputId);
            if (targetInput) {
                targetInput.value = bucketName;
                success = true;
                console.log('通过ID查找成功设置存储桶名称');
            } else {
                console.warn('无法通过ID找到目标输入字段:', targetInputId);
            }
        }

        // 方案3：通过保存的prefix重新查找
        if (!success && currentBucketTarget && currentBucketTarget.prefix) {
            const bucketInput = document.getElementById(`${currentBucketTarget.prefix}-bucket`);
            if (bucketInput) {
                bucketInput.value = bucketName;
                success = true;
                console.log('通过prefix重新查找成功设置存储桶名称');
            }
        }

        // 方案4：查找所有可能的bucket输入字段
        if (!success) {
            console.error('前面的方案都失败了，尝试查找所有bucket字段');
            const allInputs = document.querySelectorAll('input[type="text"]');
            const bucketInputs = Array.from(allInputs).filter(input =>
                input.id.includes('bucket') || input.name === 'bucket'
            );

            if (bucketInputs.length > 0) {
                const visibleInput = bucketInputs.find(input =>
                    input.offsetParent !== null && !input.disabled
                );
                if (visibleInput) {
                    visibleInput.value = bucketName;
                    success = true;
                    console.log('通过自动匹配成功设置存储桶名称');
                }
            }
        }

        // 显示结果通知
        if (success) {
            showNotification(`已选择存储桶: ${bucketName}`, 'success');
        } else {
            showNotification('无法找到目标输入字段，请手动输入存储桶名称', 'error');
            console.error('所有方案都失败了，无法设置存储桶名称');
        }
    }, 100); // 等待100ms让模态框完全关闭
}

function closeBucketModal() {
    closeModal();
}
