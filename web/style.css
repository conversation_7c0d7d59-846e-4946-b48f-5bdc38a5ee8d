/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    min-height: 100vh;
    display: flex;
    color: #333333;
}

/* 侧边栏样式 */
.sidebar {
    width: 280px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: 2px 0 20px rgba(0, 0, 0, 0.1);
    padding: 20px;
    overflow-y: auto;
    border-right: 1px solid #e0e0e0;
}

.logo {
    text-align: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #e0e0e0;
}

.logo-img {
    font-size: 3rem;
    margin-bottom: 10px;
}

.logo h1 {
    color: #ff6b35;
    font-size: 1.8rem;
    margin-bottom: 5px;
    font-weight: 700;
}

.logo p {
    color: #666;
    font-size: 0.9rem;
}

/* 导航菜单样式 */
.nav-menu {
    list-style: none;
}

.nav-item {
    margin-bottom: 8px;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    color: #555;
    text-decoration: none;
    border-radius: 8px;
    transition: all 0.3s ease;
    font-weight: 500;
    cursor: pointer;
    position: relative;
}

.nav-link:hover {
    background: rgba(255, 107, 53, 0.1);
    color: #ff6b35;
    transform: translateX(5px);
}

.nav-link.active {
    background: linear-gradient(135deg, #ff6b35, #ff8c42);
    color: white;
    box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
}

.nav-icon {
    margin-right: 12px;
    font-size: 1.2rem;
}

.nav-group-header {
    position: relative;
}

.nav-arrow {
    position: absolute;
    right: 16px;
    transition: transform 0.3s ease;
    font-size: 0.8rem;
}

.nav-arrow.expanded {
    transform: rotate(180deg);
}

.nav-submenu {
    list-style: none;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
    background: rgba(0, 0, 0, 0.05);
    border-radius: 0 0 8px 8px;
    margin-top: 4px;
}

.nav-submenu.expanded {
    max-height: 200px;
}

.nav-subitem {
    margin-bottom: 4px;
}

.nav-sublink {
    display: flex;
    align-items: center;
    padding: 8px 16px 8px 32px;
    color: #666;
    text-decoration: none;
    border-radius: 6px;
    transition: all 0.3s ease;
    font-weight: 400;
    font-size: 0.9rem;
    cursor: pointer;
}

.nav-sublink:hover {
    background: rgba(255, 107, 53, 0.15);
    color: #ff6b35;
    transform: translateX(5px);
}

.nav-sublink.active {
    background: linear-gradient(135deg, #ff6b35, #ff8c42);
    color: white;
    box-shadow: 0 2px 8px rgba(255, 107, 53, 0.3);
}

.nav-sublink .nav-icon {
    font-size: 1rem;
    margin-right: 8px;
}

/* 主内容区域样式 */
.main-content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
}

.page-header {
    text-align: center;
    color: #333;
    margin-bottom: 30px;
}

.page-header h2 {
    font-size: 2rem;
    margin-bottom: 10px;
    color: #ff6b35;
}

.page-header p {
    font-size: 1rem;
    opacity: 0.8;
    color: #666;
}

.page-actions {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    align-items: center;
}

/* 页面内容样式 */
.page-content {
    display: none;
}

.page-content.active {
    display: block;
}

/* 统计卡片样式 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid #e0e0e0;
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    border-color: #ff6b35;
}

.stat-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 15px;
}

.stat-icon {
    font-size: 2rem;
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.stat-icon.sources { background: linear-gradient(45deg, #4CAF50, #45a049); }
.stat-icon.targets { background: linear-gradient(45deg, #2196F3, #1976D2); }
.stat-icon.tasks { background: linear-gradient(45deg, #FF9800, #F57C00); }
.stat-icon.executions { background: linear-gradient(45deg, #9C27B0, #7B1FA2); }

.stat-title {
    font-size: 1.1rem;
    color: #333;
    font-weight: 600;
}

.stat-value {
    font-size: 2.5rem;
    font-weight: bold;
    color: #ff6b35;
    margin-bottom: 8px;
}

.stat-description {
    color: #666;
    font-size: 0.9rem;
}

/* 图表区域样式 */
.chart-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 30px;
}

.chart-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid #e0e0e0;
}

.chart-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    text-align: center;
}

.chart-placeholder {
    text-align: center;
    color: #999;
    padding: 40px;
    font-style: italic;
}

/* 最近记录区域样式 */
.recent-section {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid #e0e0e0;
}

.section-title {
    font-size: 1.4rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.recent-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 10px;
    background: rgba(0, 0, 0, 0.02);
    border-left: 4px solid #ff6b35;
    border: 1px solid #e0e0e0;
    transition: all 0.3s ease;
}

.recent-item:hover {
    background: #f8f9fa;
    border-color: #ff6b35;
    transform: translateX(5px);
    box-shadow: 0 4px 15px rgba(255, 107, 53, 0.2);
}

.recent-info {
    flex: 1;
}

.recent-name {
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
}

.recent-details {
    color: #666;
    font-size: 0.9rem;
}

.recent-status {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-completed { background: #d4edda; color: #155724; }
.status-running { background: #d1ecf1; color: #0c5460; }
.status-failed { background: #f8d7da; color: #721c24; }
.status-idle { background: #e2e3e5; color: #383d41; }

/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.empty-state h3 {
    margin-bottom: 10px;
    color: #999;
}

/* 按钮样式 */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    font-size: 0.9rem;
}

.btn-primary {
    background: linear-gradient(135deg, #ff6b35, #ff8c42);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(255, 107, 53, 0.4);
}

.btn-secondary {
    background: #f8f9fa;
    color: #333;
    border: 1px solid #e0e0e0;
}

.btn-secondary:hover {
    background: #e9ecef;
    border-color: #adb5bd;
}

.btn-edit {
    background: #17a2b8;
    color: white;
    padding: 6px 12px;
    font-size: 0.8rem;
}

.btn-edit:hover {
    background: #138496;
}

.btn-delete {
    background: #dc3545;
    color: white;
    padding: 6px 12px;
    font-size: 0.8rem;
}

.btn-delete:hover {
    background: #c82333;
}

.btn-run {
    background: #28a745;
    color: white;
    padding: 6px 12px;
    font-size: 0.8rem;
}

.btn-run:hover {
    background: #218838;
}

.btn-view {
    background: #6c757d;
    color: white;
    padding: 6px 12px;
    font-size: 0.8rem;
}

.btn-view:hover {
    background: #5a6268;
}

/* 配置容器样式 */
.config-container {
    display: grid;
    gap: 20px;
}

.config-item {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    border: 1px solid #e0e0e0;
    transition: all 0.3s ease;
}

.config-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.config-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.config-header h4 {
    color: #333;
    font-size: 1.2rem;
}

.config-actions {
    display: flex;
    gap: 8px;
}

.config-desc {
    color: #666;
    margin-bottom: 15px;
    font-size: 0.9rem;
}

.config-details {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    font-size: 0.85rem;
}

.config-details span {
    background: #f8f9fa;
    padding: 4px 8px;
    border-radius: 4px;
    color: #495057;
}

/* 通知样式 */
#notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
}

.notification {
    background: #17a2b8;
    color: white;
    padding: 12px 20px;
    border-radius: 6px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-width: 300px;
    animation: slideIn 0.3s ease;
}

.notification-success { background: #28a745; }
.notification-error { background: #dc3545; }
.notification-info { background: #17a2b8; }

.notification button {
    background: none;
    border: none;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    margin-left: 10px;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 优化配置样式 */
.optimization-container {
    max-width: 800px;
    margin: 0 auto;
}

.optimization-presets {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.preset-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.optimization-form {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.form-section {
    margin-bottom: 25px;
}

.form-section h3 {
    color: #333;
    margin-bottom: 15px;
    font-size: 1.1rem;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    color: #555;
    font-weight: 500;
}

.form-group input[type="number"],
.form-group input[type="text"],
.form-group select {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 0.9rem;
}

.form-group input[type="checkbox"] {
    margin-right: 8px;
}

.form-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    margin-top: 20px;
}

/* 日志容器样式 */
.logs-container {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    max-height: 600px;
    overflow-y: auto;
}

.log-entry {
    padding: 8px 12px;
    margin-bottom: 5px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
    border-left: 3px solid #ddd;
}

.log-entry.log-info {
    background: #e7f3ff;
    border-left-color: #007bff;
}

.log-entry.log-warning {
    background: #fff3cd;
    border-left-color: #ffc107;
}

.log-entry.log-error {
    background: #f8d7da;
    border-left-color: #dc3545;
}

.log-entry.log-success {
    background: #d4edda;
    border-left-color: #28a745;
}

/* 模态框样式 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none; /* 默认隐藏 */
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.modal.show {
    display: flex !important; /* 显示时使用flex布局 */
}

.modal-content {
    background: white;
    border-radius: 15px;
    padding: 25px;
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.modal-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: #333;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #999;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-btn:hover {
    color: #333;
}

/* 配置表单样式 */
.config-form {
    max-width: 500px;
}

.config-form .form-group {
    margin-bottom: 20px;
}

.config-form label {
    display: block;
    margin-bottom: 8px;
    color: #333;
    font-weight: 500;
}

.config-form input[type="text"],
.config-form input[type="password"],
.config-form input[type="number"],
.config-form select,
.config-form textarea {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 0.9rem;
    transition: border-color 0.3s ease;
}

.config-form input:focus,
.config-form select:focus,
.config-form textarea:focus {
    outline: none;
    border-color: #ff6b35;
    box-shadow: 0 0 0 2px rgba(255, 107, 53, 0.2);
}

.config-form input[type="checkbox"] {
    margin-right: 8px;
    transform: scale(1.2);
}

.config-form .form-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #eee;
}

.config-form .form-actions .btn {
    min-width: 80px;
}

.config-form small {
    display: block;
    margin-top: 5px;
    color: #666;
    font-size: 0.8rem;
    font-style: italic;
}

/* 日志执行项样式 */
.log-execution-item {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 15px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #ff6b35;
    transition: all 0.3s ease;
}

.log-execution-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.log-execution-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.log-execution-header h4 {
    color: #333;
    margin: 0;
    font-size: 1.1rem;
}

.log-execution-status {
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.log-execution-details {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 15px;
    font-size: 0.9rem;
    color: #666;
}

.log-execution-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

/* 日志头部操作样式 */
.log-header-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px 10px 0 0;
    margin-bottom: 0;
    font-weight: 600;
}

.log-count {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
}

/* 更新日志执行项以支持复选框 */
.log-execution-item {
    display: flex;
    align-items: flex-start;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 15px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #ff6b35;
    transition: all 0.3s ease;
}

.log-execution-checkbox {
    margin-right: 15px;
    margin-top: 5px;
}

.log-execution-content {
    flex: 1;
}

/* 复选框样式 */
.checkbox-container {
    display: flex;
    align-items: center;
    position: relative;
    padding-left: 25px;
    cursor: pointer;
    font-size: 14px;
    user-select: none;
    color: white;
}

.checkbox-container input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
}

.checkmark {
    position: absolute;
    top: 0;
    left: 0;
    height: 18px;
    width: 18px;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    transition: all 0.2s;
}

.checkbox-container:hover input ~ .checkmark {
    background-color: rgba(255, 255, 255, 0.3);
}

.checkbox-container input:checked ~ .checkmark {
    background-color: #ff6b35;
    border-color: #ff6b35;
}

.checkmark:after {
    content: "";
    position: absolute;
    display: none;
}

.checkbox-container input:checked ~ .checkmark:after {
    display: block;
}

.checkbox-container .checkmark:after {
    left: 6px;
    top: 2px;
    width: 5px;
    height: 10px;
    border: solid white;
    border-width: 0 3px 3px 0;
    transform: rotate(45deg);
}

/* 日志执行项内的复选框样式 */
.log-execution-checkbox .checkbox-container {
    color: #333;
}

.log-execution-checkbox .checkmark {
    background-color: #eee;
    border-color: #ddd;
}

.log-execution-checkbox .checkbox-container:hover input ~ .checkmark {
    background-color: #ccc;
}

.log-execution-checkbox .checkbox-container input:checked ~ .checkmark {
    background-color: #007bff;
    border-color: #007bff;
}

/* 文件浏览器样式 */
.file-browser-modal {
    max-width: 90vw;
    max-height: 90vh;
    width: 1200px;
    height: 800px;
}

.file-browser-toolbar {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    align-items: center;
    padding: 15px 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px;
    margin-bottom: 20px;
}

.storage-selector, .path-navigation, .file-actions {
    display: flex;
    align-items: center;
    gap: 10px;
}

.storage-selector label, .path-navigation label {
    font-weight: 600;
    white-space: nowrap;
}

.storage-selector select {
    padding: 8px 12px;
    border: none;
    border-radius: 5px;
    background: white;
    color: #333;
    min-width: 200px;
}

.breadcrumb {
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.1);
    padding: 8px 12px;
    border-radius: 5px;
    max-width: 400px;
    overflow-x: auto;
}

.breadcrumb-item {
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 3px;
    transition: background-color 0.2s;
    white-space: nowrap;
}

.breadcrumb-item:hover {
    background: rgba(255, 255, 255, 0.2);
}

.breadcrumb-separator {
    margin: 0 5px;
    opacity: 0.7;
}

.file-browser-content {
    max-height: 600px;
    overflow-y: auto;
    border: 1px solid #ddd;
    border-radius: 8px;
    background: white;
}

.file-list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #eee;
    font-weight: 600;
}

.file-list {
    padding: 10px;
}

.file-item {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    border-radius: 8px;
    margin-bottom: 8px;
    transition: all 0.2s ease;
    border: 1px solid transparent;
}

.file-item:hover {
    background: #f8f9fa;
    border-color: #e9ecef;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.directory-item {
    cursor: pointer;
    background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
}

.directory-item:hover {
    background: linear-gradient(135deg, #bbdefb 0%, #e1bee7 100%);
}

.file-checkbox {
    margin-right: 15px;
    flex-shrink: 0;
}

.file-content {
    display: flex;
    align-items: center;
    flex: 1;
    cursor: pointer;
}

.directory-item .file-content {
    cursor: pointer;
}

.file-icon {
    font-size: 24px;
    margin-right: 15px;
    min-width: 30px;
    text-align: center;
}

.file-info {
    flex: 1;
    min-width: 0;
}

.file-name {
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.file-details {
    font-size: 0.9rem;
    color: #666;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.loading-placeholder, .error-placeholder, .empty-placeholder {
    text-align: center;
    padding: 40px 20px;
    color: #666;
    font-style: italic;
}

.error-placeholder {
    color: #dc3545;
}

.empty-placeholder {
    color: #6c757d;
}

/* 文件类型特定样式 */
.file-item[data-type="image"] .file-icon {
    color: #28a745;
}

.file-item[data-type="video"] .file-icon {
    color: #dc3545;
}

.file-item[data-type="audio"] .file-icon {
    color: #ffc107;
}

.file-item[data-type="archive"] .file-icon {
    color: #6f42c1;
}

.file-item[data-type="document"] .file-icon {
    color: #007bff;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .file-browser-modal {
        width: 95vw;
        height: 95vh;
        max-width: none;
        max-height: none;
    }

    .file-browser-toolbar {
        flex-direction: column;
        align-items: stretch;
        gap: 15px;
    }

    .storage-selector, .path-navigation, .file-actions {
        flex-direction: column;
        align-items: stretch;
    }

    .breadcrumb {
        max-width: none;
    }

    .file-item {
        padding: 10px;
    }

    .file-icon {
        font-size: 20px;
        margin-right: 10px;
    }
}

/* 高级任务表单样式 */
.advanced-task-form {
    max-width: 800px;
}

.advanced-task-form .form-section {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    border-left: 4px solid #ff6b35;
}

.advanced-task-form .form-section h3 {
    color: #ff6b35;
    margin-bottom: 15px;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

.advanced-task-form .form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.advanced-task-form textarea {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 0.9rem;
    font-family: inherit;
    resize: vertical;
    transition: border-color 0.3s ease;
}

.advanced-task-form textarea:focus {
    outline: none;
    border-color: #ff6b35;
    box-shadow: 0 0 0 2px rgba(255, 107, 53, 0.2);
}

/* 任务预览样式 */
.task-preview {
    max-width: 600px;
}

.preview-section {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    border-left: 3px solid #ff6b35;
}

.preview-section h5 {
    color: #ff6b35;
    margin-bottom: 10px;
    font-size: 1rem;
}

.preview-section p {
    margin-bottom: 5px;
    font-size: 0.9rem;
    color: #333;
}

.preview-section p strong {
    color: #555;
    min-width: 100px;
    display: inline-block;
}

/* 模态框尺寸调整 */
.modal-content {
    max-width: 900px;
    width: 95%;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .advanced-task-form .form-row {
        grid-template-columns: 1fr;
    }

    .modal-content {
        width: 98%;
        margin: 10px;
    }

    .advanced-task-form {
        max-width: 100%;
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    body {
        flex-direction: column;
    }

    .sidebar {
        width: 100%;
        height: auto;
    }

    .chart-section {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .page-actions {
        flex-direction: column;
        align-items: stretch;
    }

    .preset-buttons {
        flex-direction: column;
    }

    .form-actions {
        flex-direction: column;
    }
}

/* 显示模式工具栏样式 */
.view-mode-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 12px;
    margin-bottom: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.view-mode-group {
    display: flex;
    align-items: center;
    gap: 10px;
}

.view-mode-label {
    font-weight: 600;
    color: #666;
    margin-right: 10px;
}

.view-mode-btn {
    padding: 8px 12px;
    border: 2px solid #e0e0e0;
    background: white;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1.2rem;
    min-width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.view-mode-btn:hover {
    border-color: #ff6b35;
    background: #fff5f2;
    transform: translateY(-2px);
}

.view-mode-btn.active {
    border-color: #ff6b35;
    background: #ff6b35;
    color: white;
    box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
}

.view-actions {
    display: flex;
    gap: 10px;
}

.action-btn {
    padding: 8px 12px;
    border: 2px solid #e0e0e0;
    background: white;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1.1rem;
    min-width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.action-btn:hover {
    border-color: #007bff;
    background: #f0f8ff;
    transform: translateY(-2px);
}

/* 可点击卡片样式 */
.stat-card.clickable {
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-card.clickable:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.stat-card.clickable:hover .stat-arrow {
    opacity: 1;
    transform: translateX(0);
}

.stat-arrow {
    position: absolute;
    top: 15px;
    right: 15px;
    font-size: 1.2rem;
    color: #ff6b35;
    opacity: 0;
    transform: translateX(-10px);
    transition: all 0.3s ease;
}

/* 网格视图样式 */
.grid-view {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    padding: 10px;
}

.grid-item {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.grid-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-color: #ff6b35;
}

.grid-item-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
}

.grid-item-icon {
    font-size: 2rem;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #ff6b35, #ff8c42);
    border-radius: 12px;
    color: white;
}

.grid-item h4 {
    color: #333;
    font-size: 1.2rem;
    font-weight: 600;
}

.grid-item-desc {
    color: #666;
    margin-bottom: 15px;
    line-height: 1.5;
}

.grid-item-details {
    margin-bottom: 20px;
}

.grid-item-details .storage-type {
    display: inline-block;
    background: #f8f9fa;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 0.85rem;
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
}

.grid-item-actions {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

/* 列表视图样式 */
.list-view {
    display: flex;
    flex-direction: column;
    gap: 15px;
    padding: 10px;
}

.list-item {
    display: flex;
    align-items: center;
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.list-item:hover {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    border-color: #ff6b35;
}

.list-item-icon {
    font-size: 2rem;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #ff6b35, #ff8c42);
    border-radius: 12px;
    color: white;
    margin-right: 20px;
    flex-shrink: 0;
}

.list-item-content {
    flex: 1;
    margin-right: 20px;
}

.list-item-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 8px;
}

.list-item-header h4 {
    color: #333;
    font-size: 1.2rem;
    font-weight: 600;
    margin: 0;
}

.list-item-header .storage-type {
    background: #f8f9fa;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 0.85rem;
    font-weight: 600;
    color: #495057;
}

.list-item-desc {
    color: #666;
    margin-bottom: 8px;
    line-height: 1.5;
}

.list-item-details {
    color: #888;
    font-size: 0.9rem;
}

.list-item-actions {
    display: flex;
    gap: 8px;
    flex-shrink: 0;
}

/* 卡片视图样式 */
.card-view {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 25px;
    padding: 10px;
}

.card-item {
    background: white;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.card-item:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
    border-color: #ff6b35;
}

.card-item-header {
    background: linear-gradient(135deg, #ff6b35, #ff8c42);
    color: white;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
}

.card-item-icon {
    font-size: 2.5rem;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 12px;
}

.card-item-title h4 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 5px;
}

.card-item-title .storage-type {
    background: rgba(255, 255, 255, 0.2);
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 0.85rem;
    font-weight: 500;
}

.card-item-body {
    padding: 20px;
}

.card-item-desc {
    color: #666;
    margin-bottom: 15px;
    line-height: 1.6;
}

.card-item-details {
    color: #888;
    font-size: 0.9rem;
    margin-bottom: 20px;
}

.card-item-footer {
    padding: 15px 20px;
    background: #f8f9fa;
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

/* 表格视图样式 */
.table-view {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    margin: 10px;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th {
    background: linear-gradient(135deg, #ff6b35, #ff8c42);
    color: white;
    padding: 15px 12px;
    text-align: left;
    font-weight: 600;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.data-table td {
    padding: 15px 12px;
    border-bottom: 1px solid #e9ecef;
    vertical-align: middle;
}

.data-table tr:hover {
    background: #f8f9fa;
}

.data-table tr:last-child td {
    border-bottom: none;
}

.table-icon {
    font-size: 1.5rem;
    text-align: center;
    width: 60px;
}

.table-name {
    font-weight: 600;
    color: #333;
}

.table-type {
    background: #f8f9fa;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 0.85rem;
    font-weight: 600;
    color: #495057;
    display: inline-block;
}

.table-desc {
    color: #666;
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.table-details {
    color: #888;
    font-size: 0.9rem;
    max-width: 250px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.table-actions {
    white-space: nowrap;
}

.btn-sm {
    padding: 6px 10px;
    font-size: 0.8rem;
    margin-right: 5px;
}

/* 浏览按钮样式 */
.btn-browse {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    border: none;
}

.btn-browse:hover {
    background: linear-gradient(135deg, #218838, #1ea080);
    transform: translateY(-2px);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .view-mode-toolbar {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }

    .view-mode-group {
        justify-content: center;
    }

    .view-actions {
        justify-content: center;
    }

    .grid-view {
        grid-template-columns: 1fr;
    }

    .card-view {
        grid-template-columns: 1fr;
    }

    .list-item {
        flex-direction: column;
        text-align: center;
    }

    .list-item-icon {
        margin-right: 0;
        margin-bottom: 15px;
    }

    .list-item-content {
        margin-right: 0;
        margin-bottom: 15px;
    }

    .table-view {
        overflow-x: auto;
    }

    .data-table {
        min-width: 800px;
    }
}

/* 任务状态样式 */
.task-status {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 0.85rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.task-status.idle {
    background: #f8f9fa;
    color: #6c757d;
}

.task-status.running {
    background: #d4edda;
    color: #155724;
}

.task-status.completed {
    background: #d1ecf1;
    color: #0c5460;
}

.task-status.failed {
    background: #f8d7da;
    color: #721c24;
}

.task-status.paused {
    background: #fff3cd;
    color: #856404;
}

/* 任务详情行样式 */
.task-detail-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    padding: 4px 0;
    border-bottom: 1px solid #f0f0f0;
}

.task-detail-row:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.detail-label {
    font-weight: 600;
    color: #666;
    flex-shrink: 0;
}

.detail-value {
    color: #333;
    text-align: right;
}

/* 任务信息样式 */
.task-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
    margin-top: 8px;
}

.task-info span {
    font-size: 0.85rem;
    color: #666;
    padding: 2px 6px;
    background: #f8f9fa;
    border-radius: 4px;
}

/* 运行按钮特殊样式 */
.btn-run {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    border: none;
}

.btn-run:hover {
    background: linear-gradient(135deg, #0056b3, #004085);
    transform: translateY(-2px);
}

/* 查看按钮样式 */
.btn-view {
    background: linear-gradient(135deg, #6f42c1, #5a2d91);
    color: white;
    border: none;
}

.btn-view:hover {
    background: linear-gradient(135deg, #5a2d91, #4c1d7a);
    transform: translateY(-2px);
}
