#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试文件浏览器高级修复
"""

import logging
import inspect
from datetime import datetime, timezone

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def test_async_detection():
    """测试异步方法检测"""
    logger = setup_logging()
    
    try:
        # 模拟不同类型的方法
        def sync_method():
            return "sync"
        
        async def async_method():
            return "async"
        
        class MockAdapter:
            def sync_list_files(self):
                return []
            
            async def async_list_files(self):
                return []
        
        adapter = MockAdapter()
        
        # 测试检测逻辑
        test_cases = [
            (sync_method, False, "同步函数"),
            (async_method, True, "异步函数"),
            (adapter.sync_list_files, False, "同步方法"),
            (adapter.async_list_files, True, "异步方法"),
        ]
        
        for method, expected, description in test_cases:
            is_async = inspect.iscoroutinefunction(method)
            if is_async == expected:
                logger.info(f"✅ {description}: {is_async}")
            else:
                logger.error(f"❌ {description}: 期望 {expected}, 实际 {is_async}")
                return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试异步检测失败: {e}")
        return False

def test_enhanced_datetime_handling():
    """测试增强的日期时间处理"""
    logger = logging.getLogger(__name__)
    
    try:
        from file_browser_manager import FileBrowserManager
        from unified_config_manager import UnifiedConfigManager
        
        config_manager = UnifiedConfigManager()
        browser = FileBrowserManager(config_manager)
        
        # 测试更多类型的日期时间
        test_cases = [
            (None, "空值"),
            ("2023-12-01T10:30:00Z", "ISO格式带Z"),
            ("2023-12-01T10:30:00+08:00", "ISO格式带时区"),
            ("2023-12-01 10:30:00", "标准格式"),
            (datetime.now(), "当前时间"),
            (datetime.now(timezone.utc), "UTC时间"),
            (1701422200, "时间戳"),
            (1701422200.123, "浮点时间戳"),
            ("invalid-date", "无效日期"),
            ("", "空字符串"),
            (123, "小数字"),
        ]

        for i, (test_dt, description) in enumerate(test_cases):
            try:
                result = browser._safe_format_datetime(test_dt)
                logger.info(f"✅ 测试 {i+1} ({description}): {test_dt} -> {result}")
            except Exception as e:
                logger.error(f"❌ 测试 {i+1} ({description}) 失败: {test_dt} -> {e}")
                return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试增强日期时间处理失败: {e}")
        return False

def test_error_handling():
    """测试错误处理"""
    logger = logging.getLogger(__name__)
    
    try:
        from file_browser_manager import FileBrowserManager
        from unified_config_manager import UnifiedConfigManager
        
        config_manager = UnifiedConfigManager()
        browser = FileBrowserManager(config_manager)
        
        # 测试不存在的存储
        result = browser.browse_storage("non_existent_storage", "source", "")
        
        if not result['success'] and '存储配置不存在' in result['message']:
            logger.info("✅ 不存在存储的错误处理正确")
        else:
            logger.error(f"❌ 不存在存储的错误处理不正确: {result}")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试错误处理失败: {e}")
        return False

def test_mock_adapter_compatibility():
    """测试模拟适配器兼容性"""
    logger = logging.getLogger(__name__)
    
    try:
        # 模拟不同类型的存储适配器
        class MockSyncAdapter:
            def connect(self):
                return True
            
            def list_files(self, path=""):
                return [
                    type('FileMetadata', (), {
                        'key': 'file1.txt',
                        'size': 1024,
                        'last_modified': datetime.now()
                    })(),
                    type('FileMetadata', (), {
                        'key': 'file2.txt',
                        'size': 2048,
                        'last_modified': "2023-12-01T10:30:00Z"
                    })()
                ]
        
        class MockAsyncAdapter:
            async def connect(self):
                return True
            
            async def list_files(self, path=""):
                return [
                    type('FileMetadata', (), {
                        'key': 'file3.txt',
                        'size': 3072,
                        'last_modified': 1701422200
                    })()
                ]
        
        class MockFailingAdapter:
            def connect(self):
                raise Exception("连接失败测试")
            
            def list_files(self, path=""):
                raise Exception("列表失败测试")
        
        adapters = [
            (MockSyncAdapter(), "同步适配器"),
            (MockAsyncAdapter(), "异步适配器"),
            (MockFailingAdapter(), "失败适配器"),
        ]
        
        for adapter, description in adapters:
            try:
                # 测试连接检测
                has_connect = hasattr(adapter, 'connect')
                is_async_connect = inspect.iscoroutinefunction(adapter.connect) if has_connect else False
                
                # 测试列表方法检测
                has_list = hasattr(adapter, 'list_files')
                is_async_list = inspect.iscoroutinefunction(adapter.list_files) if has_list else False
                
                logger.info(f"✅ {description}: connect={has_connect}(async={is_async_connect}), list={has_list}(async={is_async_list})")
                
            except Exception as e:
                logger.warning(f"⚠️ {description} 检测异常: {e}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试模拟适配器兼容性失败: {e}")
        return False

def test_connection_error_messages():
    """测试连接错误消息"""
    logger = logging.getLogger(__name__)
    
    try:
        # 模拟常见的连接错误
        error_cases = [
            ("getaddrinfo failed", "DNS解析失败"),
            ("STATUS_LOGON_FAILURE", "登录失败"),
            ("Connection refused", "连接被拒绝"),
            ("Timeout", "连接超时"),
        ]
        
        for error_text, description in error_cases:
            try:
                raise Exception(error_text)
            except Exception as e:
                error_msg = f"连接存储失败: {str(e)}"
                logger.info(f"✅ {description}: {error_msg}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试连接错误消息失败: {e}")
        return False

def main():
    """主函数"""
    logger = setup_logging()
    
    logger.info("🧪 文件浏览器高级修复测试")
    logger.info("=" * 60)
    
    tests = [
        ("异步方法检测测试", test_async_detection),
        ("增强日期时间处理测试", test_enhanced_datetime_handling),
        ("错误处理测试", test_error_handling),
        ("模拟适配器兼容性测试", test_mock_adapter_compatibility),
        ("连接错误消息测试", test_connection_error_messages),
    ]
    
    passed_tests = 0
    
    for test_name, test_func in tests:
        logger.info(f"\n📋 执行测试: {test_name}")
        
        try:
            if test_func():
                logger.info(f"✅ {test_name} 通过")
                passed_tests += 1
            else:
                logger.error(f"❌ {test_name} 失败")
        except Exception as e:
            logger.error(f"❌ {test_name} 异常: {e}")
    
    logger.info(f"\n📊 测试结果: {passed_tests}/{len(tests)} 通过")
    
    if passed_tests == len(tests):
        logger.info("🎉 所有测试通过！高级修复成功")
        logger.info("")
        logger.info("📋 修复内容:")
        logger.info("  ✅ 智能异步/同步方法检测")
        logger.info("  ✅ 增强的日期时间处理（支持时间戳、多种格式）")
        logger.info("  ✅ 完善的连接错误处理")
        logger.info("  ✅ 详细的错误消息和日志")
        logger.info("  ✅ 多种适配器类型兼容")
        logger.info("")
        logger.info("🚀 现在可以:")
        logger.info("  1. 自动检测并正确调用同步/异步方法")
        logger.info("  2. 处理各种格式的日期时间数据")
        logger.info("  3. 优雅处理连接失败和权限错误")
        logger.info("  4. 提供详细的错误诊断信息")
        logger.info("  5. 兼容更多类型的存储适配器")
        
        return 0
    else:
        logger.error("❌ 部分测试失败，需要进一步修复")
        return 1

if __name__ == "__main__":
    exit(main())
