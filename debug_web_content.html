<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lightrek ç»ä¸å­å¨åæ­¥å·¥å·</title>
    <link rel="stylesheet" href="style.css">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>ð</text></svg>">
</head>
<body>
    <div class="sidebar">
        <div class="logo">
            <div class="logo-img">ð</div>
            <h1>Lightrek</h1>
            <p>ç»ä¸å­å¨åæ­¥å·¥å·</p>
        </div>
        
        <nav>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a class="nav-link active" onclick="showPage('dashboard')">
                        <span class="nav-icon">ð</span>
                        ä»ªè¡¨ç
                    </a>
                </li>
                
                <li class="nav-item">
                    <div class="nav-link nav-group-header" onclick="toggleSubmenu('config-menu')">
                        <span class="nav-icon">âï¸</span>
                        éç½®ç®¡ç
                        <span class="nav-arrow">â¼</span>
                    </div>
                    <ul class="nav-submenu expanded" id="config-menu">
                        <li class="nav-subitem">
                            <a class="nav-sublink" onclick="showPage('sources')">
                                <span class="nav-icon">ð</span>
                                æ°æ®æº
                            </a>
                        </li>
                        <li class="nav-subitem">
                            <a class="nav-sublink" onclick="showPage('targets')">
                                <span class="nav-icon">ð¯</span>
                                ç®æ å­å¨
                            </a>
                        </li>
                        <li class="nav-subitem">
                            <a class="nav-sublink" onclick="showPage('tasks')">
                                <span class="nav-icon">ð</span>
                                åæ­¥ä»»å¡
                            </a>
                        </li>
                    </ul>
                </li>
                
                <li class="nav-item">
                    <a class="nav-link" onclick="showPage('logs')">
                        <span class="nav-icon">ð</span>
                        ä»»å¡æ¥å¿
                    </a>
                </li>
                
                <li class="nav-item">
                    <a class="nav-link" onclick="showPage('optimization')">
                        <span class="nav-icon">ð</span>
                        æ§è½ä¼å
                    </a>
                </li>
                
                <li class="nav-item">
                    <a class="nav-link" href="manual.html" target="_blank">
                        <span class="nav-icon">ð</span>
                        ç¨æ·æå
                    </a>
                </li>
            </ul>
        </nav>
    </div>
    
    <div class="main-content">
        <!-- ä»ªè¡¨çé¡µé¢ -->
        <div id="dashboard-page" class="page-content active">
            <div class="page-header">
                <h2>ð ç³»ç»ä»ªè¡¨ç</h2>
                <p>å®æ¶çæ§åæ­¥ä»»å¡ç¶æåç³»ç»æ§è½</p>
            </div>
            
            <div class="stats-grid" id="stats-grid">
                <!-- ç»è®¡å¡çå°éè¿JavaScriptå¨æçæ -->
            </div>
            
            <div class="chart-section">
                <div class="chart-card">
                    <div class="chart-title">ð ä»»å¡æ§è¡è¶å¿</div>
                    <div id="execution-chart">
                        <div class="chart-placeholder">ææ æ°æ®</div>
                    </div>
                </div>
                <div class="chart-card">
                    <div class="chart-title">ð¾ å­å¨ä½¿ç¨æåµ</div>
                    <div id="storage-chart">
                        <div class="chart-placeholder">ææ æ°æ®</div>
                    </div>
                </div>
            </div>
            
            <div class="recent-section">
                <div class="section-title">
                    <span>ð æè¿æ§è¡è®°å½</span>
                    <button class="btn btn-secondary" onclick="refreshExecutions()">ð å·æ°</button>
                </div>
                <div id="recent-executions">
                    <!-- æè¿æ§è¡è®°å½å°éè¿JavaScriptå¨æå è½½ -->
                </div>
            </div>
        </div>
        
        <!-- æ°æ®æºé¡µé¢ -->
        <div id="sources-page" class="page-content">
            <div class="page-header">
                <h2>ð æ°æ®æºç®¡ç</h2>
                <p>éç½®åç®¡çåç§ç±»åçæ°æ®æº</p>
            </div>
            
            <div class="page-actions">
                <button class="btn btn-primary" onclick="showAddSourceModal()">
                    â æ·»å æ°æ®æº
                </button>
                <button class="btn btn-secondary" onclick="loadSources()">
                    ð å·æ°åè¡¨
                </button>
            </div>
            
            <div id="sources-container" class="config-container">
                <!-- æ°æ®æºåè¡¨å°éè¿JavaScriptå¨æå è½½ -->
            </div>
        </div>
        
        <!-- ç®æ å­å¨é¡µé¢ -->
        <div id="targets-page" class="page-content">
            <div class="page-header">
                <h2>ð¯ ç®æ å­å¨ç®¡ç</h2>
                <p>éç½®åç®¡çç®æ å­å¨ä½ç½®</p>
            </div>
            
            <div class="page-actions">
                <button class="btn btn-primary" onclick="showAddTargetModal()">
                    â æ·»å ç®æ å­å¨
                </button>
                <button class="btn btn-secondary" onclick="loadTargets()">
                    ð å·æ°åè¡¨
                </button>
            </div>
            
            <div id="targets-container" class="config-container">
                <!-- ç®æ å­å¨åè¡¨å°éè¿JavaScriptå¨æå è½½ -->
            </div>
        </div>
        
        <!-- åæ­¥ä»»å¡é¡µé¢ -->
        <div id="tasks-page" class="page-content">
            <div class="page-header">
                <h2>ð åæ­¥ä»»å¡ç®¡ç</h2>
                <p>åå»ºåç®¡çåæ­¥ä»»å¡</p>
            </div>
            
            <div class="page-actions">
                <button class="btn btn-primary" onclick="showAddTaskModal()">
                    â åå»ºåæ­¥ä»»å¡
                </button>
                <button class="btn btn-secondary" onclick="loadTasks()">
                    ð å·æ°åè¡¨
                </button>
            </div>
            
            <div id="tasks-container" class="config-container">
                <!-- åæ­¥ä»»å¡åè¡¨å°éè¿JavaScriptå¨æå è½½ -->
            </div>
        </div>
        
        <!-- ä»»å¡æ¥å¿é¡µé¢ -->
        <div id="logs-page" class="page-content">
            <div class="page-header">
                <h2>ð ä»»å¡æ¥å¿</h2>
                <p>æ¥çä»»å¡æ§è¡æ¥å¿åç¶æ</p>
            </div>
            
            <div class="page-actions">
                <select id="taskLogFilter" onchange="filterTaskLogs()">
                    <option value="">ææä»»å¡</option>
                </select>
                <button class="btn btn-secondary" onclick="loadTaskLogs()">
                    ð å·æ°æ¥å¿
                </button>
            </div>
            
            <div id="logs-container" class="logs-container">
                <!-- ä»»å¡æ¥å¿å°éè¿JavaScriptå¨æå è½½ -->
            </div>
        </div>
        
        <!-- æ§è½ä¼åé¡µé¢ -->
        <div id="optimization-page" class="page-content">
            <div class="page-header">
                <h2>ð æ§è½ä¼å</h2>
                <p>è°æ´ç³»ç»æ§è½åæ°</p>
            </div>
            
            <div class="optimization-container">
                <div class="optimization-presets">
                    <h3>å¿«ééç½®</h3>
                    <div class="preset-buttons">
                        <button class="btn btn-secondary" onclick="applyOptimizationPreset('high_performance')">
                            ð é«æ§è½
                        </button>
                        <button class="btn btn-secondary" onclick="applyOptimizationPreset('balanced')">
                            âï¸ å¹³è¡¡
                        </button>
                        <button class="btn btn-secondary" onclick="applyOptimizationPreset('compatible')">
                            ð¡ï¸ å¼å®¹
                        </button>
                    </div>
                </div>
                
                <form id="optimization-form" class="optimization-form">
                    <div class="form-section">
                        <h3>å¹¶åè®¾ç½®</h3>
                        <div class="form-group">
                            <label for="max_workers">æå¤§å¹¶åçº¿ç¨æ°</label>
                            <input type="number" id="max_workers" name="max_workers" min="1" max="100" value="20">
                        </div>
                    </div>
                    
                    <div class="form-section">
                        <h3>ä¼ è¾è®¾ç½®</h3>
                        <div class="form-group">
                            <label for="chunk_size_mb">ååå¤§å° (MB)</label>
                            <input type="number" id="chunk_size_mb" name="chunk_size_mb" min="1" max="1000" value="10">
                        </div>
                        <div class="form-group">
                            <label for="retry_times">éè¯æ¬¡æ°</label>
                            <input type="number" id="retry_times" name="retry_times" min="0" max="10" value="3">
                        </div>
                    </div>
                    
                    <div class="form-section">
                        <h3>åè½å¼å³</h3>
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="enable_compression" name="enable_compression" checked>
                                å¯ç¨åç¼©ä¼ è¾
                            </label>
                        </div>
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="enable_parallel_scan" name="enable_parallel_scan" checked>
                                å¯ç¨å¹¶è¡æ«æ
                            </label>
                        </div>
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="enable_cache" name="enable_cache" checked>
                                å¯ç¨ç¼å­
                            </label>
                        </div>
                    </div>
                    
                    <div class="form-actions">
                        <button type="button" class="btn btn-primary" onclick="saveOptimizationConfig()">
                            ð¾ ä¿å­éç½®
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="resetOptimizationConfig()">
                            ð éç½®é»è®¤
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- éç¥å®¹å¨ -->
    <div id="notification-container"></div>
    
    <!-- æ¨¡ææ¡å®¹å¨ -->
    <div id="modal-container"></div>
    
    <script src="app.js"></script>
</body>
</html>
