#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
严格的配置项验证测试
验证所有配置项是否真正有效
"""

import requests
import json
import time

class StrictConfigTester:
    """严格配置测试器"""
    
    def __init__(self, base_url="http://localhost:8001"):
        self.base_url = base_url
        self.test_results = {}
        
    def run_strict_test(self):
        """运行严格测试"""
        print("🔍 LightRek 严格配置项验证测试")
        print("=" * 80)
        
        # 1. 边界条件严格测试
        self.test_boundary_conditions_strict()
        
        # 2. 参数验证严格测试
        self.test_parameter_validation_strict()
        
        # 3. 配置项有效性测试
        self.test_config_effectiveness()
        
        # 4. 错误处理严格测试
        self.test_error_handling_strict()
        
        # 5. 生成严格测试报告
        self.generate_strict_report()
        
    def test_boundary_conditions_strict(self):
        """严格测试边界条件"""
        print("\n🎯 1. 边界条件严格测试")
        print("-" * 50)
        
        boundary_tests = []
        
        # 测试端口边界 - 更严格
        print("🔧 严格测试端口边界...")
        port_tests = [
            {"port": -1, "should_fail": True, "reason": "负数端口"},
            {"port": 0, "should_fail": True, "reason": "零端口"},
            {"port": 65536, "should_fail": True, "reason": "超出范围"},
            {"port": 99999, "should_fail": True, "reason": "远超范围"},
            {"port": "abc", "should_fail": True, "reason": "非数字端口"},
            {"port": 22, "should_fail": False, "reason": "有效端口"},
            {"port": 443, "should_fail": False, "reason": "有效端口"}
        ]
        
        for test in port_tests:
            config = {
                "name": f"端口测试_{test['port']}",
                "storage_type": "sftp",
                "host": "example.com",
                "port": test["port"],
                "username": "test"
            }
            
            result = self._test_config(config, test["should_fail"], test["reason"])
            boundary_tests.append(result)
            
        # 测试字符串长度边界 - 更严格
        print("\n🔧 严格测试字符串长度...")
        string_tests = [
            {"name": "", "should_fail": True, "reason": "空字符串"},
            {"name": "a" * 1000, "should_fail": True, "reason": "超长字符串"},
            {"name": "a" * 500, "should_fail": True, "reason": "很长字符串"},
            {"name": "a", "should_fail": False, "reason": "最短有效"},
            {"name": "正常名称", "should_fail": False, "reason": "正常长度"}
        ]
        
        for test in string_tests:
            config = {
                "name": test["name"],
                "storage_type": "s3",
                "endpoint": "https://s3.amazonaws.com",
                "access_key": "test",
                "secret_key": "test",
                "bucket": "test"
            }
            
            result = self._test_config(config, test["should_fail"], test["reason"])
            boundary_tests.append(result)
            
        # 测试URL格式 - 更严格
        print("\n🔧 严格测试URL格式...")
        url_tests = [
            {"endpoint": "invalid-url", "should_fail": True, "reason": "无效URL"},
            {"endpoint": "ftp://example.com", "should_fail": True, "reason": "错误协议"},
            {"endpoint": "http://", "should_fail": True, "reason": "不完整URL"},
            {"endpoint": "https://", "should_fail": True, "reason": "不完整URL"},
            {"endpoint": "http://example.com", "should_fail": False, "reason": "有效HTTP"},
            {"endpoint": "https://s3.amazonaws.com", "should_fail": False, "reason": "有效HTTPS"}
        ]
        
        for test in url_tests:
            config = {
                "name": "URL测试",
                "storage_type": "s3",
                "endpoint": test["endpoint"],
                "access_key": "test",
                "secret_key": "test",
                "bucket": "test"
            }
            
            result = self._test_config(config, test["should_fail"], test["reason"])
            boundary_tests.append(result)
            
        self.test_results['boundary_conditions_strict'] = boundary_tests
        
    def test_parameter_validation_strict(self):
        """严格测试参数验证"""
        print("\n🔍 2. 参数验证严格测试")
        print("-" * 50)
        
        validation_tests = []
        
        # 测试必需字段验证
        print("🔧 测试必需字段验证...")
        required_field_tests = [
            {
                "config": {"storage_type": "s3"},
                "should_fail": True,
                "reason": "缺少所有S3必需字段"
            },
            {
                "config": {"name": "测试", "storage_type": "s3", "endpoint": "https://s3.amazonaws.com"},
                "should_fail": True,
                "reason": "缺少access_key等字段"
            },
            {
                "config": {"storage_type": "sftp"},
                "should_fail": True,
                "reason": "缺少SFTP必需字段"
            },
            {
                "config": {"name": "测试", "storage_type": "sftp", "host": "example.com"},
                "should_fail": True,
                "reason": "缺少username字段"
            }
        ]
        
        for test in required_field_tests:
            result = self._test_config(test["config"], test["should_fail"], test["reason"])
            validation_tests.append(result)
            
        # 测试数据类型验证
        print("\n🔧 测试数据类型验证...")
        type_tests = [
            {
                "config": {
                    "name": "类型测试",
                    "storage_type": "sftp",
                    "host": "example.com",
                    "port": "not_a_number",
                    "username": "test"
                },
                "should_fail": True,
                "reason": "端口不是数字"
            },
            {
                "config": {
                    "name": 123,  # 应该是字符串
                    "storage_type": "s3",
                    "endpoint": "https://s3.amazonaws.com",
                    "access_key": "test",
                    "secret_key": "test",
                    "bucket": "test"
                },
                "should_fail": True,
                "reason": "名称不是字符串"
            }
        ]
        
        for test in type_tests:
            result = self._test_config(test["config"], test["should_fail"], test["reason"])
            validation_tests.append(result)
            
        self.test_results['parameter_validation_strict'] = validation_tests
        
    def test_config_effectiveness(self):
        """测试配置项有效性"""
        print("\n⚙️ 3. 配置项有效性测试")
        print("-" * 50)
        
        effectiveness_tests = []
        
        # 测试配置是否真正保存和使用
        print("🔧 测试配置保存和使用...")
        
        # 添加一个配置
        test_config = {
            "name": "有效性测试存储",
            "storage_type": "s3",
            "endpoint": "https://s3.amazonaws.com",
            "access_key": "test_key_12345",
            "secret_key": "test_secret_67890",
            "bucket": "test-bucket-unique"
        }
        
        try:
            # 添加配置
            add_response = requests.post(f"{self.base_url}/api/sources", json=test_config, timeout=10)
            
            if add_response.status_code == 200:
                add_result = add_response.json()
                if add_result.get("success"):
                    storage_id = add_result.get("id")
                    print(f"✅ 配置添加成功，ID: {storage_id}")
                    
                    # 验证配置是否真正保存
                    get_response = requests.get(f"{self.base_url}/api/sources", timeout=5)
                    if get_response.status_code == 200:
                        sources = get_response.json()
                        
                        # 查找我们添加的配置
                        found_config = None
                        for source in sources:
                            if source.get("name") == test_config["name"]:
                                found_config = source
                                break
                        
                        if found_config:
                            print("✅ 配置成功保存并可检索")
                            
                            # 验证配置字段是否正确保存
                            fields_correct = True
                            for key, value in test_config.items():
                                if key in found_config and found_config[key] != value:
                                    fields_correct = False
                                    print(f"❌ 字段 {key} 保存不正确: 期望 {value}, 实际 {found_config[key]}")
                            
                            if fields_correct:
                                print("✅ 所有配置字段正确保存")
                                effectiveness_tests.append({"test": "config_save_retrieve", "passed": True})
                            else:
                                print("❌ 部分配置字段保存不正确")
                                effectiveness_tests.append({"test": "config_save_retrieve", "passed": False})
                        else:
                            print("❌ 配置未找到")
                            effectiveness_tests.append({"test": "config_save_retrieve", "passed": False})
                    else:
                        print("❌ 无法检索配置列表")
                        effectiveness_tests.append({"test": "config_save_retrieve", "passed": False})
                else:
                    print("❌ 配置添加失败")
                    effectiveness_tests.append({"test": "config_save_retrieve", "passed": False})
            else:
                print("❌ 配置添加请求失败")
                effectiveness_tests.append({"test": "config_save_retrieve", "passed": False})
                
        except Exception as e:
            print(f"❌ 配置有效性测试异常: {e}")
            effectiveness_tests.append({"test": "config_save_retrieve", "passed": False, "error": str(e)})
            
        self.test_results['config_effectiveness'] = effectiveness_tests
        
    def test_error_handling_strict(self):
        """严格测试错误处理"""
        print("\n🛡️ 4. 错误处理严格测试")
        print("-" * 50)
        
        error_tests = []
        
        # 测试各种错误情况
        error_scenarios = [
            {
                "name": "完全无效JSON",
                "data": "这不是JSON",
                "content_type": "application/json",
                "expected_status": [400, 500]
            },
            {
                "name": "空JSON对象",
                "data": {},
                "content_type": "application/json",
                "expected_status": [400, 200]  # 可能返回验证错误
            },
            {
                "name": "超大JSON",
                "data": {"name": "x" * 100000},
                "content_type": "application/json",
                "expected_status": [400, 413, 200]
            }
        ]
        
        for scenario in error_scenarios:
            print(f"\n🧪 测试: {scenario['name']}")
            
            try:
                if isinstance(scenario["data"], str):
                    response = requests.post(
                        f"{self.base_url}/api/sources",
                        data=scenario["data"],
                        headers={"Content-Type": scenario["content_type"]},
                        timeout=10
                    )
                else:
                    response = requests.post(
                        f"{self.base_url}/api/sources",
                        json=scenario["data"],
                        timeout=10
                    )
                
                print(f"   状态码: {response.status_code}")
                
                if response.status_code in scenario["expected_status"]:
                    print("   ✅ 错误处理正确")
                    error_tests.append({"test": scenario["name"], "passed": True})
                else:
                    print(f"   ❌ 意外状态码，期望: {scenario['expected_status']}")
                    error_tests.append({"test": scenario["name"], "passed": False})
                    
            except Exception as e:
                print(f"   ❌ 测试异常: {e}")
                error_tests.append({"test": scenario["name"], "passed": False, "error": str(e)})
                
        self.test_results['error_handling_strict'] = error_tests
        
    def _test_config(self, config, should_fail, reason):
        """测试单个配置"""
        try:
            response = requests.post(f"{self.base_url}/api/sources", json=config, timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                success = result.get("success", False)
                
                if should_fail:
                    if not success:
                        print(f"   ✅ 正确拒绝: {reason}")
                        return {"test": reason, "passed": True, "config": config}
                    else:
                        print(f"   ❌ 错误接受: {reason}")
                        return {"test": reason, "passed": False, "config": config}
                else:
                    if success:
                        print(f"   ✅ 正确接受: {reason}")
                        return {"test": reason, "passed": True, "config": config}
                    else:
                        print(f"   ❌ 错误拒绝: {reason}")
                        return {"test": reason, "passed": False, "config": config}
            else:
                print(f"   ❌ HTTP错误 {response.status_code}: {reason}")
                return {"test": reason, "passed": False, "config": config, "http_error": response.status_code}
                
        except Exception as e:
            print(f"   ❌ 异常: {reason} - {e}")
            return {"test": reason, "passed": False, "config": config, "error": str(e)}
    
    def generate_strict_report(self):
        """生成严格测试报告"""
        print("\n📊 5. 严格测试报告")
        print("-" * 50)
        
        total_tests = 0
        passed_tests = 0
        
        for category, tests in self.test_results.items():
            category_passed = sum(1 for test in tests if test.get("passed", False))
            total_tests += len(tests)
            passed_tests += category_passed
            
            print(f"\n📋 {category}:")
            print(f"   通过: {category_passed}/{len(tests)}")
            
            for test in tests:
                status = "✅" if test.get("passed", False) else "❌"
                test_name = test.get("test", "未知测试")
                print(f"   {status} {test_name}")
        
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        
        print(f"\n总体结果:")
        print(f"总测试数: {total_tests}")
        print(f"通过测试: {passed_tests}")
        print(f"成功率: {success_rate:.1f}%")
        
        # 保存详细报告
        report = {
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'summary': {
                'total_tests': total_tests,
                'passed_tests': passed_tests,
                'success_rate': f"{success_rate:.1f}%"
            },
            'detailed_results': self.test_results
        }
        
        with open('strict_config_test_report.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 详细报告已保存: strict_config_test_report.json")
        
        if success_rate >= 90:
            print("\n🎉 严格测试通过！配置项验证可靠。")
        elif success_rate >= 70:
            print("\n⚠️ 部分测试失败，需要改进。")
        else:
            print("\n❌ 严重问题！需要大幅改进配置验证。")


if __name__ == "__main__":
    tester = StrictConfigTester()
    tester.run_strict_test()
