#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库并发访问测试
"""

import threading
import time
import logging
from datetime import datetime
import uuid

def setup_logging():
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(threadName)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def concurrent_hash_update_test(thread_id: int, num_operations: int = 100):
    """并发哈希更新测试"""
    logger = logging.getLogger(__name__)
    
    try:
        from database_manager import db_manager
        
        success_count = 0
        
        for i in range(num_operations):
            storage_id = f"test_storage_{thread_id}"
            file_key = f"test_file_{i}.txt"
            file_hash = f"hash_{thread_id}_{i}_{uuid.uuid4().hex[:8]}"
            
            success = db_manager.update_file_hash(
                storage_id=storage_id,
                file_key=file_key,
                file_size=1024 + i,
                file_hash=file_hash,
                last_modified=datetime.now().isoformat()
            )
            
            if success:
                success_count += 1
            else:
                logger.error(f"线程 {thread_id} 操作 {i} 失败")
            
            # 小延迟模拟真实场景
            time.sleep(0.01)
        
        logger.info(f"线程 {thread_id} 完成: {success_count}/{num_operations} 成功")
        return success_count
        
    except Exception as e:
        logger.error(f"线程 {thread_id} 异常: {e}")
        return 0

def main():
    """主函数"""
    logger = setup_logging()
    
    logger.info("🧪 数据库并发访问测试")
    logger.info("=" * 50)
    
    # 测试参数
    num_threads = 10
    operations_per_thread = 50
    
    logger.info(f"启动 {num_threads} 个线程，每个线程执行 {operations_per_thread} 次操作")
    
    # 创建线程
    threads = []
    results = {}
    
    start_time = time.time()
    
    for i in range(num_threads):
        thread = threading.Thread(
            target=lambda tid=i: results.update({tid: concurrent_hash_update_test(tid, operations_per_thread)}),
            name=f"TestThread-{i}"
        )
        threads.append(thread)
        thread.start()
    
    # 等待所有线程完成
    for thread in threads:
        thread.join()
    
    end_time = time.time()
    
    # 统计结果
    total_operations = num_threads * operations_per_thread
    total_success = sum(results.values())
    success_rate = (total_success / total_operations) * 100
    
    logger.info("=" * 50)
    logger.info(f"📊 测试结果:")
    logger.info(f"  总操作数: {total_operations}")
    logger.info(f"  成功操作: {total_success}")
    logger.info(f"  成功率: {success_rate:.1f}%")
    logger.info(f"  总耗时: {end_time - start_time:.2f}秒")
    logger.info(f"  平均速度: {total_operations / (end_time - start_time):.1f} 操作/秒")
    
    if success_rate >= 99.0:
        logger.info("🎉 并发测试通过！数据库锁定问题已解决")
        return 0
    else:
        logger.error("❌ 并发测试失败，仍存在数据库锁定问题")
        return 1

if __name__ == "__main__":
    exit(main())
