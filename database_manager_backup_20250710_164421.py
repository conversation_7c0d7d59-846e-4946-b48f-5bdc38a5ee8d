"""
SQLite数据库管理器 - 用于存储文件哈希、任务日志等数据
"""

import sqlite3
import json
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
import threading


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, db_path: str = "lightrek_data.db"):
        self.db_path = db_path
        self.logger = logging.getLogger(__name__)
        self._lock = threading.Lock()
        self._init_database()
    
    def _init_database(self):
        """初始化数据库表结构"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 文件哈希表 - 用于增量同步
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS file_hashes (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        storage_id TEXT NOT NULL,
                        file_key TEXT NOT NULL,
                        file_size INTEGER NOT NULL,
                        file_hash TEXT NOT NULL,
                        last_modified TEXT NOT NULL,
                        created_at TEXT NOT NULL,
                        updated_at TEXT NOT NULL,
                        UNIQUE(storage_id, file_key)
                    )
                ''')
                
                # 任务执行历史表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS task_executions (
                        id TEXT PRIMARY KEY,
                        task_id TEXT NOT NULL,
                        task_name TEXT NOT NULL,
                        source_id TEXT NOT NULL,
                        target_id TEXT NOT NULL,
                        status TEXT NOT NULL,
                        start_time TEXT NOT NULL,
                        end_time TEXT,
                        files_total INTEGER DEFAULT 0,
                        files_processed INTEGER DEFAULT 0,
                        files_skipped INTEGER DEFAULT 0,
                        files_failed INTEGER DEFAULT 0,
                        bytes_total INTEGER DEFAULT 0,
                        bytes_transferred INTEGER DEFAULT 0,
                        error_message TEXT,
                        created_at TEXT NOT NULL
                    )
                ''')
                
                # 任务日志表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS task_logs (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        execution_id TEXT NOT NULL,
                        timestamp TEXT NOT NULL,
                        level TEXT NOT NULL,
                        message TEXT NOT NULL,
                        details TEXT,
                        created_at TEXT NOT NULL,
                        FOREIGN KEY (execution_id) REFERENCES task_executions (id)
                    )
                ''')
                
                # 创建索引
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_file_hashes_storage_key ON file_hashes(storage_id, file_key)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_task_executions_task_id ON task_executions(task_id)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_task_logs_execution_id ON task_logs(execution_id)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_task_logs_timestamp ON task_logs(timestamp)')
                
                conn.commit()
                self.logger.info("数据库初始化成功")
                
        except Exception as e:
            self.logger.error(f"数据库初始化失败: {e}")
            raise
    
    def get_file_hash(self, storage_id: str, file_key: str) -> Optional[Dict[str, Any]]:
        """获取文件哈希记录"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT * FROM file_hashes 
                    WHERE storage_id = ? AND file_key = ?
                ''', (storage_id, file_key))
                
                row = cursor.fetchone()
                return dict(row) if row else None
                
        except Exception as e:
            self.logger.error(f"获取文件哈希失败: {e}")
            return None
    
    def update_file_hash(self, storage_id: str, file_key: str, file_size: int, 
                        file_hash: str, last_modified: str) -> bool:
        """更新文件哈希记录"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                now = datetime.now().isoformat()
                
                cursor.execute('''
                    INSERT OR REPLACE INTO file_hashes 
                    (storage_id, file_key, file_size, file_hash, last_modified, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (storage_id, file_key, file_size, file_hash, last_modified, now, now))
                
                conn.commit()
                return True
                
        except Exception as e:
            self.logger.error(f"更新文件哈希失败: {e}")
            return False
    
    def create_task_execution(self, execution_id: str, task_id: str, task_name: str,
                            source_id: str, target_id: str) -> bool:
        """创建任务执行记录"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                now = datetime.now().isoformat()
                
                cursor.execute('''
                    INSERT INTO task_executions 
                    (id, task_id, task_name, source_id, target_id, status, start_time, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (execution_id, task_id, task_name, source_id, target_id, 'running', now, now))
                
                conn.commit()
                return True
                
        except Exception as e:
            self.logger.error(f"创建任务执行记录失败: {e}")
            return False
    
    def update_task_execution(self, execution_id: str, **kwargs) -> bool:
        """更新任务执行记录"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 构建更新语句
                set_clauses = []
                values = []
                
                for key, value in kwargs.items():
                    if key in ['status', 'end_time', 'files_total', 'files_processed', 
                              'files_skipped', 'files_failed', 'bytes_total', 'bytes_transferred', 'error_message']:
                        set_clauses.append(f"{key} = ?")
                        values.append(value)
                
                if not set_clauses:
                    return True
                
                values.append(execution_id)
                sql = f"UPDATE task_executions SET {', '.join(set_clauses)} WHERE id = ?"
                
                cursor.execute(sql, values)
                conn.commit()
                return True
                
        except Exception as e:
            self.logger.error(f"更新任务执行记录失败: {e}")
            return False
    
    def add_task_log(self, execution_id: str, level: str, message: str, details: str = None) -> bool:
        """添加任务日志"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                now = datetime.now().isoformat()
                
                cursor.execute('''
                    INSERT INTO task_logs (execution_id, timestamp, level, message, details, created_at)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (execution_id, now, level, message, details, now))
                
                conn.commit()
                return True
                
        except Exception as e:
            self.logger.error(f"添加任务日志失败: {e}")
            return False
    
    def get_task_executions(self, task_id: str = None, limit: int = 100) -> List[Dict[str, Any]]:
        """获取任务执行历史"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()

                if task_id:
                    cursor.execute('''
                        SELECT * FROM task_executions
                        WHERE task_id = ?
                        ORDER BY start_time DESC
                        LIMIT ?
                    ''', (task_id, limit))
                else:
                    cursor.execute('''
                        SELECT * FROM task_executions
                        ORDER BY start_time DESC
                        LIMIT ?
                    ''', (limit,))

                return [dict(row) for row in cursor.fetchall()]

        except Exception as e:
            self.logger.error(f"获取任务执行历史失败: {e}")
            return []

    def get_recent_executions(self, limit: int = 20) -> List[Dict[str, Any]]:
        """获取最近的任务执行记录"""
        return self.get_task_executions(limit=limit)
    
    def get_execution_logs(self, execution_id: str, limit: int = 1000) -> List[Dict[str, Any]]:
        """获取任务执行日志"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT * FROM task_logs 
                    WHERE execution_id = ? 
                    ORDER BY timestamp DESC 
                    LIMIT ?
                ''', (execution_id, limit))
                
                return [dict(row) for row in cursor.fetchall()]
                
        except Exception as e:
            self.logger.error(f"获取任务执行日志失败: {e}")
            return []
    
    def cleanup_old_data(self, days: int = 30) -> bool:
        """清理旧数据"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cutoff_date = datetime.now().replace(day=datetime.now().day - days).isoformat()
                
                # 清理旧的任务日志
                cursor.execute('DELETE FROM task_logs WHERE created_at < ?', (cutoff_date,))
                
                # 清理旧的任务执行记录
                cursor.execute('DELETE FROM task_executions WHERE created_at < ?', (cutoff_date,))
                
                conn.commit()
                return True
                
        except Exception as e:
            self.logger.error(f"清理旧数据失败: {e}")
            return False

    def clean_invalid_logs(self) -> int:
        """清理无效的任务日志"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 删除没有对应任务执行记录的日志
                cursor.execute('''
                    DELETE FROM task_logs
                    WHERE execution_id NOT IN (
                        SELECT execution_id FROM task_executions
                    )
                ''')

                deleted_logs = cursor.rowcount

                # 删除超过30天的日志
                cursor.execute('''
                    DELETE FROM task_logs
                    WHERE timestamp < datetime('now', '-30 days')
                ''')

                deleted_logs += cursor.rowcount

                # 删除状态为"运行中"但超过24小时的执行记录
                cursor.execute('''
                    DELETE FROM task_executions
                    WHERE status = 'running'
                    AND start_time < datetime('now', '-1 day')
                ''')

                deleted_executions = cursor.rowcount

                conn.commit()

                self.logger.info(f"清理完成: 删除了 {deleted_logs} 条日志, {deleted_executions} 条执行记录")
                return deleted_logs + deleted_executions

        except Exception as e:
            self.logger.error(f"清理无效日志失败: {e}")
            return 0

    def delete_execution(self, execution_id: str) -> bool:
        """删除指定的执行记录及其相关日志"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 删除相关日志
                cursor.execute('DELETE FROM task_logs WHERE execution_id = ?', (execution_id,))

                # 删除执行记录
                cursor.execute('DELETE FROM task_executions WHERE execution_id = ?', (execution_id,))

                # 删除文件同步记录
                cursor.execute('DELETE FROM file_sync_records WHERE execution_id = ?', (execution_id,))

                conn.commit()

                if cursor.rowcount > 0:
                    self.logger.info(f"已删除执行记录: {execution_id}")
                    return True
                else:
                    self.logger.warning(f"执行记录不存在: {execution_id}")
                    return False

        except Exception as e:
            self.logger.error(f"删除执行记录失败: {e}")
            return False


# 全局数据库管理器实例
db_manager = DatabaseManager()
