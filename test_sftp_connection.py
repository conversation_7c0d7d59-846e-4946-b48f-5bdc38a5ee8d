#!/usr/bin/env python3
"""
测试SFTP连接功能
"""

import requests
import json

def test_sftp_connection():
    """测试SFTP连接"""
    print("🧪 测试SFTP连接功能")
    print("=" * 50)
    
    # 测试连接数据
    connection_data = {
        "name": "SFTP连接测试",
        "description": "测试SFTP连接",
        "storage_type": "sftp",
        "hostname": "**************",
        "port": 22,
        "username": "root",
        "password": "rootreadbook2",
        "root_path": "/"
    }
    
    try:
        # 发送连接测试请求
        response = requests.post(
            'http://localhost:8001/api/test-connection',
            json=connection_data,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        print(f"HTTP状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ SFTP连接测试成功")
                print(f"消息: {result.get('message', '')}")
            else:
                print(f"❌ SFTP连接测试失败: {result.get('message', '未知错误')}")
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    test_sftp_connection()
