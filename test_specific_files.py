#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试特定文件的编码问题
"""

import smb_storage_adapter
from storage_abstraction import StorageFactory, SMBStorageConfig, StorageType

def test_specific_files():
    """测试特定文件的编码问题"""
    print("🧪 测试特定文件的编码问题")
    
    # 创建SMB配置
    config = SMBStorageConfig(
        storage_type=StorageType.SMB,
        hostname='Jay<PERSON>',
        port=445,
        username='smb',
        password='smbsmb',
        domain='WORKGROUP',
        share_name='hlmj',
        root_path=''
    )
    
    # 创建适配器
    adapter = StorageFactory.create_adapter(config)
    
    # 首先列出所有文件，看看实际的文件名
    print("📋 列出所有文件:")
    try:
        result = adapter.list_files("", max_keys=50)
        for i, file_meta in enumerate(result.files):
            print(f"  {i+1:2d}. {repr(file_meta.key)} - {file_meta.size} bytes")
            
            # 检查特定的问题文件
            if file_meta.key in ['TerSafe.dll', 'TP3Helper.exe', 'UnityPlayer.dll']:
                print(f"      ⚠️ 问题文件: {file_meta.key}")
                print(f"      文件名类型: {type(file_meta.key)}")
                print(f"      文件名编码: {repr(file_meta.key)}")
                print(f"      UTF-8编码: {file_meta.key.encode('utf-8')}")
                
                # 尝试下载
                try:
                    data = adapter.get_file(file_meta.key)
                    if data:
                        print(f"      ✅ 下载成功: {len(data)} bytes")
                    else:
                        print(f"      ❌ 下载失败: 返回None")
                except Exception as e:
                    print(f"      ❌ 下载异常: {e}")
                print()
                
    except Exception as e:
        print(f"❌ 列出文件失败: {e}")

if __name__ == "__main__":
    test_specific_files()
