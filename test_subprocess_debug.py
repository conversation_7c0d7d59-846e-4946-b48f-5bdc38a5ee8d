#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试子进程下载问题
"""

import subprocess
import json
import tempfile
import os

def test_subprocess_debug():
    """调试子进程下载问题"""
    print("🧪 调试子进程下载问题")
    
    # SMB配置
    config_data = {
        'hostname': '<PERSON><PERSON>',
        'port': 445,
        'username': 'smb',
        'password': 'smbsmb',
        'domain': 'WORKGROUP',
        'share_name': 'hlmj',
        'root_path': ''
    }
    
    # 测试文件
    test_file = '398.xml'
    
    print(f"测试下载: {test_file}")
    
    temp_file = None
    try:
        # 创建临时文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False, encoding='utf-8') as f:
            json.dump({
                'config': config_data,
                'file_key': test_file
            }, f)
            temp_file = f.name
        
        print(f"临时文件: {temp_file}")
        
        # 构建命令
        script_path = 'smb_downloader.py'
        cmd = ['python', script_path, temp_file]
        
        print(f"执行命令: {cmd}")
        
        # 执行子进程
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=30,
            cwd=os.getcwd(),
            encoding='utf-8',
            errors='ignore'
        )
        
        print(f"返回码: {result.returncode}")
        print(f"标准输出: {repr(result.stdout)}")
        print(f"标准错误: {repr(result.stderr)}")
        
        if result.returncode == 0:
            output = result.stdout.strip()
            print(f"输出内容: {output}")
            
            # 查找SUCCESS行
            for line in output.split('\n'):
                if line.startswith("SUCCESS:"):
                    print(f"SUCCESS行: {line}")
                    parts = line.split(':', 2)
                    if len(parts) == 3:
                        size = int(parts[1])
                        output_file = parts[2]
                        print(f"文件大小: {size}")
                        print(f"输出文件: {output_file}")
                        
                        if os.path.exists(output_file):
                            print(f"输出文件存在: {os.path.getsize(output_file)} bytes")
                            os.unlink(output_file)
                            print("✅ 子进程下载成功")
                        else:
                            print("❌ 输出文件不存在")
                    break
                elif line.startswith("ERROR:"):
                    print(f"ERROR行: {line}")
                elif line.startswith("FAILED:"):
                    print(f"FAILED行: {line}")
        else:
            print("❌ 子进程执行失败")
            
    except Exception as e:
        print(f"❌ 异常: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 清理临时文件
        if temp_file and os.path.exists(temp_file):
            try:
                os.unlink(temp_file)
            except:
                pass

if __name__ == "__main__":
    test_subprocess_debug()
