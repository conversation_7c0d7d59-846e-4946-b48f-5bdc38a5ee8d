#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LightRek 统一存储同步工具 - 完整Web界面（单文件版本）
"""

import json
import uuid
import base64
import os
from http.server import BaseHTTPRequestHandler


class CompleteWebUI:
    """完整Web界面类"""
    
    def __init__(self, config_manager, task_manager, db_manager=None):
        self.config_manager = config_manager
        self.task_manager = task_manager
        self.db_manager = db_manager
        self.logo_base64 = self._load_logo()
    
    def _load_logo(self):
        """加载logo文件并转换为base64"""
        logo_base64 = ""
        try:
            logo_paths = [
                "lightrek logo 64px.png",
                "./lightrek logo 64px.png",
                os.path.join(os.path.dirname(__file__), "lightrek logo 64px.png"),
                os.path.join(os.getcwd(), "lightrek logo 64px.png")
            ]
            
            for logo_path in logo_paths:
                if os.path.exists(logo_path):
                    with open(logo_path, "rb") as f:
                        logo_data = f.read()
                        logo_base64 = base64.b64encode(logo_data).decode('utf-8')
                    print(f"成功加载logo: {logo_path}")
                    break
            else:
                print("警告: 找不到logo文件，将使用默认图标")
        except Exception as e:
            print(f"无法加载logo: {e}")
        
        return logo_base64
    
    def create_handler_class(self):
        """创建请求处理器类"""
        config_manager = self.config_manager
        task_manager = self.task_manager
        db_manager = self.db_manager
        logo_base64 = self.logo_base64
        
        class RequestHandler(BaseHTTPRequestHandler):
            def do_GET(self):
                if self.path == '/' or self.path == '/dashboard':
                    self._serve_main_page()
                elif self.path == '/manual':
                    self._serve_manual_page()
                elif self.path == '/api/sources':
                    self._serve_sources()
                elif self.path == '/api/targets':
                    self._serve_targets()
                elif self.path == '/api/tasks':
                    self._serve_tasks()
                elif self.path == '/api/task-status':
                    self._serve_task_status()
                elif self.path == '/api/task-executions':
                    self._serve_task_executions()
                elif self.path.startswith('/api/task-logs/'):
                    execution_id = self.path.split('/')[-1]
                    self._serve_task_logs(execution_id)
                elif self.path == '/api/statistics':
                    self._serve_statistics()
                elif self.path == '/api/optimization-config':
                    self._serve_optimization_config()
                else:
                    self._serve_404()
            
            def do_POST(self):
                content_length = int(self.headers.get('Content-Length', 0))
                post_data = self.rfile.read(content_length)
                
                try:
                    data = json.loads(post_data.decode('utf-8')) if post_data else {}
                except:
                    data = {}
                
                if self.path == '/api/sources':
                    self._add_source(data)
                elif self.path == '/api/targets':
                    self._add_target(data)
                elif self.path == '/api/tasks':
                    self._add_task(data)
                elif self.path == '/api/test-connection':
                    self._test_connection(data)
                elif self.path.startswith('/api/tasks/') and self.path.endswith('/run'):
                    task_id = self.path.split('/')[-2]
                    self._run_task(task_id)
                elif self.path.startswith('/api/tasks/') and self.path.endswith('/stop'):
                    task_id = self.path.split('/')[-2]
                    self._stop_task(task_id)
                elif self.path == '/api/optimization-config':
                    self._save_optimization_config(data)
                else:
                    self._serve_404()
            
            def do_PUT(self):
                content_length = int(self.headers.get('Content-Length', 0))
                post_data = self.rfile.read(content_length)
                
                try:
                    data = json.loads(post_data.decode('utf-8')) if post_data else {}
                except:
                    data = {}
                
                if self.path.startswith('/api/sources/'):
                    source_id = self.path.split('/')[-1]
                    self._update_source(source_id, data)
                elif self.path.startswith('/api/targets/'):
                    target_id = self.path.split('/')[-1]
                    self._update_target(target_id, data)
                elif self.path.startswith('/api/tasks/'):
                    task_id = self.path.split('/')[-1]
                    self._update_task(task_id, data)
                else:
                    self._serve_404()
            
            def do_DELETE(self):
                if self.path.startswith('/api/sources/'):
                    source_id = self.path.split('/')[-1]
                    self._delete_source(source_id)
                elif self.path.startswith('/api/targets/'):
                    target_id = self.path.split('/')[-1]
                    self._delete_target(target_id)
                elif self.path.startswith('/api/tasks/'):
                    task_id = self.path.split('/')[-1]
                    self._delete_task(task_id)
                else:
                    self._serve_404()
            
            def _serve_main_page(self):
                """提供主页面"""
                stats = self._get_dashboard_stats()
                html = self._get_main_html(stats)
                self.send_response(200)
                self.send_header('Content-Type', 'text/html; charset=utf-8')
                self.end_headers()
                self.wfile.write(html.encode('utf-8'))
            
            def _serve_manual_page(self):
                """提供手册页面"""
                html = self._get_manual_html()
                self.send_response(200)
                self.send_header('Content-Type', 'text/html; charset=utf-8')
                self.end_headers()
                self.wfile.write(html.encode('utf-8'))
            
            def _serve_sources(self):
                """提供数据源列表"""
                try:
                    sources = config_manager.get_all_sources()
                    self._send_json({'success': True, 'sources': sources})
                except Exception as e:
                    self._send_json({'success': False, 'message': str(e), 'sources': {}})
            
            def _serve_targets(self):
                """提供目标存储列表"""
                try:
                    targets = config_manager.get_all_targets()
                    self._send_json({'success': True, 'targets': targets})
                except Exception as e:
                    self._send_json({'success': False, 'message': str(e), 'targets': {}})
            
            def _serve_tasks(self):
                """提供任务列表"""
                try:
                    tasks = config_manager.get_all_tasks()
                    self._send_json({'success': True, 'tasks': tasks})
                except Exception as e:
                    self._send_json({'success': False, 'message': str(e), 'tasks': {}})
            
            def _serve_task_status(self):
                """提供任务状态"""
                try:
                    status = task_manager.get_all_task_status() if hasattr(task_manager, 'get_all_task_status') else {}
                    self._send_json({'success': True, 'status': status})
                except Exception as e:
                    self._send_json({'success': False, 'message': str(e), 'status': {}})
            
            def _serve_task_executions(self):
                """提供任务执行记录"""
                try:
                    executions = db_manager.get_recent_executions(20) if db_manager else []
                    self._send_json({'success': True, 'executions': executions})
                except Exception as e:
                    self._send_json({'success': False, 'message': str(e), 'executions': []})
            
            def _serve_task_logs(self, execution_id):
                """提供特定任务的日志数据"""
                try:
                    logs = db_manager.get_execution_logs(execution_id) if db_manager else []
                    self._send_json({'success': True, 'logs': logs})
                except Exception as e:
                    self._send_json({'success': False, 'message': str(e), 'logs': []})
            
            def _serve_statistics(self):
                """提供统计信息"""
                try:
                    stats = self._get_dashboard_stats()
                    self._send_json({'success': True, 'statistics': stats})
                except Exception as e:
                    self._send_json({'success': False, 'message': str(e), 'statistics': {}})
            
            def _serve_optimization_config(self):
                """提供优化配置"""
                try:
                    config = config_manager.get_optimization_config()
                    self._send_json({'success': True, 'config': config})
                except Exception as e:
                    self._send_json({'success': False, 'message': str(e), 'config': {}})
            
            def _serve_404(self):
                """提供404页面"""
                self.send_response(404)
                self.send_header('Content-Type', 'text/html; charset=utf-8')
                self.end_headers()
                html = """
                <!DOCTYPE html>
                <html>
                <head>
                    <title>404 - 页面未找到</title>
                    <style>
                        body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
                        h1 { color: #ff6b35; }
                    </style>
                </head>
                <body>
                    <h1>404 - 页面未找到</h1>
                    <p>请求的页面不存在</p>
                    <a href="/">返回首页</a>
                </body>
                </html>
                """
                self.wfile.write(html.encode('utf-8'))
            
            def _send_json(self, data):
                """发送JSON响应"""
                self.send_response(200)
                self.send_header('Content-Type', 'application/json; charset=utf-8')
                self.end_headers()
                self.wfile.write(json.dumps(data, ensure_ascii=False).encode('utf-8'))
            
            def _get_dashboard_stats(self):
                """获取仪表盘统计数据"""
                try:
                    sources = config_manager.get_all_sources()
                    targets = config_manager.get_all_targets()
                    tasks = config_manager.get_all_tasks()
                    
                    stats = {
                        'sources': len(sources),
                        'targets': len(targets),
                        'tasks': len(tasks),
                        'executions': 0
                    }
                    
                    if db_manager:
                        stats['executions'] = len(db_manager.get_recent_executions(1000))
                    
                    return stats
                except Exception as e:
                    print(f"获取统计数据失败: {e}")
                    return {
                        'sources': 0,
                        'targets': 0,
                        'tasks': 0,
                        'executions': 0
                    }

            def _get_main_html(self, stats):
                """获取主页面HTML"""
                logo_img = f'<img src="data:image/png;base64,{logo_base64}" class="logo-img" alt="Lightrek Logo">' if logo_base64 else '🚀'
                js_stats = json.dumps(stats)

                return f"""
                <!DOCTYPE html>
                <html lang="zh-CN">
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <title>Lightrek 统一存储同步工具</title>
                    <style>
                        * {{
                            margin: 0;
                            padding: 0;
                            box-sizing: border-box;
                        }}

                        body {{
                            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
                            min-height: 100vh;
                            display: flex;
                            color: #333333;
                        }}

                        .sidebar {{
                            width: 280px;
                            background: rgba(255, 255, 255, 0.95);
                            backdrop-filter: blur(10px);
                            box-shadow: 2px 0 20px rgba(0, 0, 0, 0.1);
                            padding: 20px;
                            overflow-y: auto;
                            border-right: 1px solid #e0e0e0;
                        }}

                        .logo {{
                            text-align: center;
                            margin-bottom: 30px;
                            padding-bottom: 20px;
                            border-bottom: 2px solid #e0e0e0;
                        }}

                        .logo-img {{
                            width: 48px;
                            height: 48px;
                            margin-bottom: 10px;
                        }}

                        .logo h1 {{
                            color: #ff6b35;
                            font-size: 1.8rem;
                            margin-bottom: 5px;
                            font-weight: 700;
                        }}

                        .logo p {{
                            color: #666;
                            font-size: 0.9rem;
                        }}

                        .nav-menu {{
                            list-style: none;
                        }}

                        .nav-item {{
                            margin-bottom: 8px;
                        }}

                        .nav-link {{
                            display: flex;
                            align-items: center;
                            padding: 12px 16px;
                            color: #555;
                            text-decoration: none;
                            border-radius: 8px;
                            transition: all 0.3s ease;
                            font-weight: 500;
                            cursor: pointer;
                        }}

                        .nav-link:hover {{
                            background: rgba(255, 107, 53, 0.1);
                            color: #ff6b35;
                            transform: translateX(5px);
                        }}

                        .nav-link.active {{
                            background: linear-gradient(135deg, #ff6b35, #ff8c42);
                            color: white;
                            box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
                        }}

                        .nav-icon {{
                            margin-right: 12px;
                            font-size: 1.2rem;
                        }}

                        .nav-group-header {{
                            position: relative;
                        }}

                        .nav-arrow {{
                            position: absolute;
                            right: 16px;
                            transition: transform 0.3s ease;
                            font-size: 0.8rem;
                        }}

                        .nav-arrow.expanded {{
                            transform: rotate(180deg);
                        }}

                        .nav-submenu {{
                            list-style: none;
                            max-height: 0;
                            overflow: hidden;
                            transition: max-height 0.3s ease;
                            background: rgba(0, 0, 0, 0.05);
                            border-radius: 0 0 8px 8px;
                            margin-top: 4px;
                        }}

                        .nav-submenu.expanded {{
                            max-height: 200px;
                        }}

                        .nav-subitem {{
                            margin-bottom: 4px;
                        }}

                        .nav-sublink {{
                            display: flex;
                            align-items: center;
                            padding: 8px 16px 8px 32px;
                            color: #666;
                            text-decoration: none;
                            border-radius: 6px;
                            transition: all 0.3s ease;
                            font-weight: 400;
                            font-size: 0.9rem;
                            cursor: pointer;
                        }}

                        .nav-sublink:hover {{
                            background: rgba(255, 107, 53, 0.15);
                            color: #ff6b35;
                            transform: translateX(5px);
                        }}

                        .nav-sublink.active {{
                            background: linear-gradient(135deg, #ff6b35, #ff8c42);
                            color: white;
                            box-shadow: 0 2px 8px rgba(255, 107, 53, 0.3);
                        }}

                        .nav-sublink .nav-icon {{
                            font-size: 1rem;
                            margin-right: 8px;
                        }}

                        .main-content {{
                            flex: 1;
                            padding: 20px;
                            overflow-y: auto;
                        }}

                        .page-header {{
                            text-align: center;
                            color: #333;
                            margin-bottom: 30px;
                        }}

                        .page-header h2 {{
                            font-size: 2rem;
                            margin-bottom: 10px;
                            color: #ff6b35;
                        }}

                        .page-header p {{
                            font-size: 1rem;
                            opacity: 0.8;
                            color: #666;
                        }}

                        .stats-grid {{
                            display: grid;
                            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
                            gap: 20px;
                            margin-bottom: 30px;
                        }}

                        .stat-card {{
                            background: rgba(255, 255, 255, 0.95);
                            border-radius: 15px;
                            padding: 25px;
                            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
                            backdrop-filter: blur(10px);
                            border: 1px solid #e0e0e0;
                            transition: transform 0.3s ease;
                        }}

                        .stat-card:hover {{
                            transform: translateY(-5px);
                            border-color: #ff6b35;
                        }}

                        .stat-header {{
                            display: flex;
                            align-items: center;
                            gap: 12px;
                            margin-bottom: 15px;
                        }}

                        .stat-icon {{
                            font-size: 2rem;
                            width: 50px;
                            height: 50px;
                            border-radius: 12px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            color: white;
                        }}

                        .stat-icon.sources {{ background: linear-gradient(45deg, #4CAF50, #45a049); }}
                        .stat-icon.targets {{ background: linear-gradient(45deg, #2196F3, #1976D2); }}
                        .stat-icon.tasks {{ background: linear-gradient(45deg, #FF9800, #F57C00); }}
                        .stat-icon.executions {{ background: linear-gradient(45deg, #9C27B0, #7B1FA2); }}

                        .stat-title {{
                            font-size: 1.1rem;
                            color: #333;
                            font-weight: 600;
                        }}

                        .stat-value {{
                            font-size: 2.5rem;
                            font-weight: bold;
                            color: #ff6b35;
                            margin-bottom: 8px;
                        }}

                        .stat-description {{
                            color: #666;
                            font-size: 0.9rem;
                        }}

                        .recent-section {{
                            background: rgba(255, 255, 255, 0.95);
                            border-radius: 15px;
                            padding: 25px;
                            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
                            border: 1px solid #e0e0e0;
                        }}

                        .section-title {{
                            font-size: 1.4rem;
                            font-weight: 600;
                            color: #333;
                            margin-bottom: 20px;
                            display: flex;
                            align-items: center;
                            gap: 10px;
                        }}

                        .recent-item {{
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                            padding: 15px;
                            border-radius: 10px;
                            margin-bottom: 10px;
                            background: rgba(0, 0, 0, 0.02);
                            border-left: 4px solid #ff6b35;
                            border: 1px solid #e0e0e0;
                            transition: all 0.3s ease;
                        }}

                        .recent-item:hover {{
                            background: #f8f9fa;
                            border-color: #ff6b35;
                            transform: translateX(5px);
                            box-shadow: 0 4px 15px rgba(255, 107, 53, 0.2);
                        }}

                        .recent-info {{
                            flex: 1;
                        }}

                        .recent-name {{
                            font-weight: 600;
                            color: #333;
                            margin-bottom: 5px;
                        }}

                        .recent-details {{
                            color: #666;
                            font-size: 0.9rem;
                        }}

                        .recent-status {{
                            padding: 6px 12px;
                            border-radius: 20px;
                            font-size: 0.8rem;
                            font-weight: 600;
                            text-transform: uppercase;
                        }}

                        .status-completed {{ background: #d4edda; color: #155724; }}
                        .status-running {{ background: #d1ecf1; color: #0c5460; }}
                        .status-failed {{ background: #f8d7da; color: #721c24; }}
                        .status-idle {{ background: #e2e3e5; color: #383d41; }}

                        .empty-state {{
                            text-align: center;
                            padding: 60px 20px;
                            color: #666;
                        }}

                        .empty-state h3 {{
                            margin-bottom: 10px;
                            color: #999;
                        }}

                        .page-content {{
                            display: none;
                        }}

                        .page-content.active {{
                            display: block;
                        }}

                        .btn {{
                            padding: 10px 20px;
                            border: none;
                            border-radius: 8px;
                            cursor: pointer;
                            font-weight: 500;
                            transition: all 0.3s ease;
                            text-decoration: none;
                            display: inline-block;
                        }}

                        .btn-primary {{
                            background: linear-gradient(135deg, #ff6b35, #ff8c42);
                            color: white;
                        }}

                        .btn-primary:hover {{
                            transform: translateY(-2px);
                            box-shadow: 0 4px 15px rgba(255, 107, 53, 0.4);
                        }}

                        .alert {{
                            padding: 15px;
                            border-radius: 8px;
                            margin-bottom: 20px;
                        }}

                        .alert-info {{
                            background: #d1ecf1;
                            color: #0c5460;
                            border: 1px solid #bee5eb;
                        }}
                    </style>
                </head>
                <body>
                    <div class="sidebar">
                        <div class="logo">
                            {logo_img}
                            <h1>Lightrek</h1>
                            <p>统一存储同步工具</p>
                        </div>

                        <nav>
                            <ul class="nav-menu">
                                <li class="nav-item">
                                    <a class="nav-link active" onclick="showPage('dashboard')">
                                        <span class="nav-icon">📊</span>
                                        仪表盘
                                    </a>
                                </li>

                                <li class="nav-item">
                                    <div class="nav-link nav-group-header" onclick="toggleSubmenu('config-menu')">
                                        <span class="nav-icon">⚙️</span>
                                        配置管理
                                        <span class="nav-arrow">▼</span>
                                    </div>
                                    <ul class="nav-submenu expanded" id="config-menu">
                                        <li class="nav-subitem">
                                            <a class="nav-sublink" onclick="showPage('sources')">
                                                <span class="nav-icon">📁</span>
                                                数据源
                                            </a>
                                        </li>
                                        <li class="nav-subitem">
                                            <a class="nav-sublink" onclick="showPage('targets')">
                                                <span class="nav-icon">🎯</span>
                                                目标存储
                                            </a>
                                        </li>
                                        <li class="nav-subitem">
                                            <a class="nav-sublink" onclick="showPage('tasks')">
                                                <span class="nav-icon">📋</span>
                                                同步任务
                                            </a>
                                        </li>
                                    </ul>
                                </li>

                                <li class="nav-item">
                                    <a class="nav-link" onclick="showPage('logs')">
                                        <span class="nav-icon">📄</span>
                                        任务日志
                                    </a>
                                </li>

                                <li class="nav-item">
                                    <a class="nav-link" onclick="showPage('optimization')">
                                        <span class="nav-icon">🚀</span>
                                        性能优化
                                    </a>
                                </li>

                                <li class="nav-item">
                                    <a class="nav-link" href="/manual" target="_blank">
                                        <span class="nav-icon">📖</span>
                                        用户手册
                                    </a>
                                </li>
                            </ul>
                        </nav>
                    </div>

                    <div class="main-content">
                        <!-- 仪表盘页面 -->
                        <div id="dashboard-page" class="page-content active">
                            <div class="page-header">
                                <h2>📊 系统仪表盘</h2>
                                <p>实时监控同步任务状态和系统性能</p>
                            </div>

                            <div class="stats-grid" id="stats-grid">
                                <!-- 统计卡片将通过JavaScript动态生成 -->
                            </div>

                            <div class="recent-section">
                                <div class="section-title">
                                    <span>🕒 最近执行记录</span>
                                </div>
                                <div id="recent-executions">
                                    <!-- 最近执行记录将通过JavaScript动态加载 -->
                                </div>
                            </div>
                        </div>

                        <!-- 数据源页面 -->
                        <div id="sources-page" class="page-content">
                            <div class="page-header">
                                <h2>📁 数据源管理</h2>
                                <p>配置和管理各种类型的数据源</p>
                            </div>

                            <div class="alert alert-info">
                                <strong>提示：</strong> 数据源管理功能正在开发中，敬请期待！
                            </div>

                            <button class="btn btn-primary" onclick="alert('功能开发中...')">
                                ➕ 添加数据源
                            </button>
                        </div>

                        <!-- 目标存储页面 -->
                        <div id="targets-page" class="page-content">
                            <div class="page-header">
                                <h2>🎯 目标存储管理</h2>
                                <p>配置和管理目标存储位置</p>
                            </div>

                            <div class="alert alert-info">
                                <strong>提示：</strong> 目标存储管理功能正在开发中，敬请期待！
                            </div>

                            <button class="btn btn-primary" onclick="alert('功能开发中...')">
                                ➕ 添加目标存储
                            </button>
                        </div>

                        <!-- 同步任务页面 -->
                        <div id="tasks-page" class="page-content">
                            <div class="page-header">
                                <h2>📋 同步任务管理</h2>
                                <p>创建和管理同步任务</p>
                            </div>

                            <div class="alert alert-info">
                                <strong>提示：</strong> 同步任务管理功能正在开发中，敬请期待！
                            </div>

                            <button class="btn btn-primary" onclick="alert('功能开发中...')">
                                ➕ 创建同步任务
                            </button>
                        </div>

                        <!-- 任务日志页面 -->
                        <div id="logs-page" class="page-content">
                            <div class="page-header">
                                <h2>📄 任务日志</h2>
                                <p>查看任务执行日志和状态</p>
                            </div>

                            <div class="alert alert-info">
                                <strong>提示：</strong> 任务日志功能正在开发中，敬请期待！
                            </div>
                        </div>

                        <!-- 性能优化页面 -->
                        <div id="optimization-page" class="page-content">
                            <div class="page-header">
                                <h2>🚀 性能优化</h2>
                                <p>调整系统性能参数</p>
                            </div>

                            <div class="alert alert-info">
                                <strong>提示：</strong> 性能优化功能正在开发中，敬请期待！
                            </div>
                        </div>
                    </div>

                    <script>
                        // 统计数据
                        const stats = {js_stats};

                        // 页面切换功能
                        function showPage(page) {{
                            console.log('切换到页面:', page);

                            // 隐藏所有页面
                            document.querySelectorAll('.page-content').forEach(content => {{
                                content.classList.remove('active');
                            }});

                            // 显示目标页面
                            const targetPage = document.getElementById(page + '-page');
                            if (targetPage) {{
                                targetPage.classList.add('active');
                            }}

                            // 更新导航状态
                            document.querySelectorAll('.nav-link, .nav-sublink').forEach(link => {{
                                link.classList.remove('active');
                            }});

                            const currentLink = document.querySelector(`[onclick*="showPage('${{page}}')"]`);
                            if (currentLink) {{
                                currentLink.classList.add('active');
                            }}

                            // 如果是仪表盘页面，重新加载数据
                            if (page === 'dashboard') {{
                                loadDashboard();
                            }}
                        }}

                        // 子菜单切换
                        function toggleSubmenu(menuId) {{
                            console.log('切换子菜单:', menuId);
                            const submenu = document.getElementById(menuId);
                            const arrow = submenu.previousElementSibling.querySelector('.nav-arrow');

                            if (submenu && arrow) {{
                                if (submenu.classList.contains('expanded')) {{
                                    submenu.classList.remove('expanded');
                                    arrow.classList.remove('expanded');
                                }} else {{
                                    submenu.classList.add('expanded');
                                    arrow.classList.add('expanded');
                                }}
                            }}
                        }}

                        // 加载仪表盘数据
                        function loadDashboard() {{
                            console.log('加载仪表盘数据');
                            generateStatsCards();
                            loadRecentExecutions();
                        }}

                        // 生成统计卡片
                        function generateStatsCards() {{
                            const statsGrid = document.getElementById('stats-grid');
                            if (!statsGrid) {{
                                console.error('找不到stats-grid元素');
                                return;
                            }}

                            const cards = [
                                {{
                                    icon: '📁',
                                    iconClass: 'sources',
                                    title: '数据源',
                                    value: stats.sources || 0,
                                    description: '已配置的数据源数量'
                                }},
                                {{
                                    icon: '🎯',
                                    iconClass: 'targets',
                                    title: '目标存储',
                                    value: stats.targets || 0,
                                    description: '已配置的目标存储数量'
                                }},
                                {{
                                    icon: '📋',
                                    iconClass: 'tasks',
                                    title: '同步任务',
                                    value: stats.tasks || 0,
                                    description: '已创建的同步任务数量'
                                }},
                                {{
                                    icon: '🔄',
                                    iconClass: 'executions',
                                    title: '执行次数',
                                    value: stats.executions || 0,
                                    description: '总任务执行次数'
                                }}
                            ];

                            statsGrid.innerHTML = cards.map(card => `
                                <div class="stat-card">
                                    <div class="stat-header">
                                        <div class="stat-icon ${{card.iconClass}}">${{card.icon}}</div>
                                        <div class="stat-title">${{card.title}}</div>
                                    </div>
                                    <div class="stat-value">${{card.value}}</div>
                                    <div class="stat-description">${{card.description}}</div>
                                </div>
                            `).join('');

                            console.log('统计卡片已生成');
                        }}

                        // 加载最近执行记录
                        function loadRecentExecutions() {{
                            console.log('加载最近执行记录');
                            fetch('/api/task-executions')
                                .then(response => response.json())
                                .then(data => {{
                                    console.log('执行记录数据:', data);
                                    const container = document.getElementById('recent-executions');
                                    if (!container) {{
                                        console.error('找不到recent-executions元素');
                                        return;
                                    }}

                                    if (data.success && data.executions && data.executions.length > 0) {{
                                        container.innerHTML = data.executions.slice(0, 5).map(exec => `
                                            <div class="recent-item">
                                                <div class="recent-info">
                                                    <div class="recent-name">${{exec.task_name || '未知任务'}}</div>
                                                    <div class="recent-details">
                                                        执行时间: ${{exec.start_time ? new Date(exec.start_time).toLocaleString() : '未知'}} |
                                                        耗时: ${{exec.duration || '未知'}}
                                                    </div>
                                                </div>
                                                <div class="recent-status status-${{exec.status || 'idle'}}">${{exec.status || 'idle'}}</div>
                                            </div>
                                        `).join('');
                                    }} else {{
                                        container.innerHTML = '<div class="empty-state"><h3>暂无执行记录</h3><p>还没有任务执行记录</p></div>';
                                    }}
                                }})
                                .catch(error => {{
                                    console.error('加载执行记录失败:', error);
                                    const container = document.getElementById('recent-executions');
                                    if (container) {{
                                        container.innerHTML = '<div class="empty-state"><h3>加载失败</h3><p>无法获取执行记录</p></div>';
                                    }}
                                }});
                        }}

                        // 页面加载完成后初始化
                        document.addEventListener('DOMContentLoaded', function() {{
                            console.log('页面加载完成，初始化仪表盘');
                            loadDashboard();

                            // 初始化子菜单状态
                            const configMenu = document.getElementById('config-menu');
                            if (configMenu) {{
                                configMenu.classList.add('expanded');
                                const arrow = document.querySelector('.nav-group-header .nav-arrow');
                                if (arrow) {{
                                    arrow.classList.add('expanded');
                                }}
                            }}
                        }});

                        // 全局错误处理
                        window.addEventListener('error', function(e) {{
                            console.error('JavaScript错误:', e.error);
                        }});
                    </script>
                </body>
                </html>
                """

        return RequestHandler
