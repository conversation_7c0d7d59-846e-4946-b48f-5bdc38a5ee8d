#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
编译脚本功能测试
"""

import os
import sys
import subprocess
import shutil
import tempfile
from pathlib import Path

def run_command(cmd, cwd=None, timeout=60):
    """执行命令并返回结果"""
    print(f'执行命令: {cmd}')
    try:
        result = subprocess.run(
            cmd, 
            shell=True, 
            cwd=cwd, 
            capture_output=True, 
            text=True, 
            timeout=timeout,
            encoding='utf-8',
            errors='replace'
        )
        
        if result.returncode != 0:
            print(f'命令执行失败 (返回码: {result.returncode})')
            if result.stderr:
                print(f'错误输出: {result.stderr[:500]}...')
            return False, result.stderr
        
        print('命令执行成功')
        return True, result.stdout
    
    except subprocess.TimeoutExpired:
        print(f'命令执行超时 ({timeout}秒)')
        return False, f'超时 ({timeout}秒)'
    except Exception as e:
        print(f'命令执行异常: {e}')
        return False, str(e)

def test_build_scripts():
    """测试编译脚本功能"""
    print('=== 编译脚本功能测试 ===')
    
    current_dir = os.getcwd()
    print(f'当前目录: {current_dir}')
    
    print('\n--- 检查编译脚本文件 ---')
    build_scripts = [
        'build.py',
        'build_simple.py', 
        'build_offline.py',
        'build_unified.py'
    ]
    
    available_scripts = []
    for script in build_scripts:
        if os.path.exists(script):
            print(f'✅ {script} 存在')
            available_scripts.append(script)
        else:
            print(f'❌ {script} 不存在')
    
    print(f'可用编译脚本: {len(available_scripts)}个')
    
    print('\n--- 检查依赖文件 ---')
    required_files = [
        'requirements.txt',
        'requirements_unified.txt',
        'lightrek.py',
        'main.py',
        'start.py'
    ]
    
    for file in required_files:
        if os.path.exists(file):
            print(f'✅ {file} 存在')
        else:
            print(f'❌ {file} 不存在')
    
    print('\n--- 检查Python环境 ---')
    success, python_version = run_command('python --version')
    if success:
        print(f'Python版本: {python_version.strip()}')
    
    success, pip_version = run_command('pip --version')
    if success:
        print(f'Pip版本: {pip_version.strip()}')
    
    print('\n--- 检查PyInstaller ---')
    success, pyinstaller_version = run_command('pyinstaller --version')
    if success:
        print(f'PyInstaller版本: {pyinstaller_version.strip()}')
    else:
        print('PyInstaller未安装，尝试安装...')
        success, output = run_command('pip install pyinstaller')
        if success:
            print('PyInstaller安装成功')
        else:
            print('PyInstaller安装失败')
    
    print('\n--- 测试依赖安装 ---')
    # 测试安装requirements.txt中的依赖
    if os.path.exists('requirements.txt'):
        print('测试安装requirements.txt依赖...')
        success, output = run_command('pip install -r requirements.txt', timeout=120)
        if success:
            print('requirements.txt依赖安装成功')
        else:
            print('requirements.txt依赖安装失败')
    
    if os.path.exists('requirements_unified.txt'):
        print('测试安装requirements_unified.txt依赖...')
        success, output = run_command('pip install -r requirements_unified.txt', timeout=120)
        if success:
            print('requirements_unified.txt依赖安装成功')
        else:
            print('requirements_unified.txt依赖安装失败')
    
    print('\n--- 测试程序基本运行 ---')
    # 测试主程序是否能正常启动
    test_commands = [
        'python lightrek.py --help',
        'python main.py --help',
        'python start.py --help'
    ]
    
    for cmd in test_commands:
        if os.path.exists(cmd.split()[1]):
            print(f'测试命令: {cmd}')
            success, output = run_command(cmd, timeout=10)
            if success:
                print(f'  ✅ 运行成功')
            else:
                print(f'  ❌ 运行失败')
        else:
            print(f'跳过不存在的文件: {cmd.split()[1]}')
    
    print('\n--- 测试编译脚本语法 ---')
    # 检查编译脚本的语法是否正确
    for script in available_scripts:
        print(f'检查 {script} 语法...')
        success, output = run_command(f'python -m py_compile {script}')
        if success:
            print(f'  ✅ {script} 语法正确')
        else:
            print(f'  ❌ {script} 语法错误: {output[:200]}...')
    
    print('\n--- 测试编译脚本帮助信息 ---')
    # 测试编译脚本的帮助信息
    for script in available_scripts:
        print(f'测试 {script} 帮助信息...')
        success, output = run_command(f'python {script} --help', timeout=10)
        if success:
            print(f'  ✅ {script} 帮助信息正常')
        else:
            # 尝试直接运行看是否有其他输出
            success2, output2 = run_command(f'python {script}', timeout=5)
            if success2:
                print(f'  ✅ {script} 可以运行')
            else:
                print(f'  ❌ {script} 运行失败')
    
    print('\n--- 测试简单编译（不实际编译） ---')
    # 测试编译脚本的参数解析和初始化
    if 'build_simple.py' in available_scripts:
        print('测试build_simple.py的初始化...')
        
        # 创建临时测试脚本
        test_script_content = '''
import sys
sys.path.insert(0, '.')

try:
    # 尝试导入编译脚本的主要函数
    import build_simple
    print("build_simple模块导入成功")
    
    # 检查主要函数是否存在
    if hasattr(build_simple, 'main'):
        print("main函数存在")
    if hasattr(build_simple, 'build_executable'):
        print("build_executable函数存在")
    if hasattr(build_simple, 'run_command'):
        print("run_command函数存在")
        
except ImportError as e:
    print(f"导入失败: {e}")
except Exception as e:
    print(f"其他错误: {e}")
'''
        
        temp_test_file = 'temp_build_test.py'
        with open(temp_test_file, 'w', encoding='utf-8') as f:
            f.write(test_script_content)
        
        try:
            success, output = run_command(f'python {temp_test_file}')
            if success:
                print('  ✅ build_simple模块测试通过')
                print(f'  输出: {output.strip()}')
            else:
                print('  ❌ build_simple模块测试失败')
        finally:
            if os.path.exists(temp_test_file):
                os.remove(temp_test_file)
    
    print('\n--- 检查编译输出目录 ---')
    output_dirs = ['dist', 'build', 'releases']
    for dir_name in output_dirs:
        if os.path.exists(dir_name):
            print(f'✅ {dir_name} 目录存在')
            # 列出目录内容
            try:
                items = os.listdir(dir_name)
                if items:
                    print(f'  内容: {items[:5]}{"..." if len(items) > 5 else ""}')
                else:
                    print('  目录为空')
            except Exception as e:
                print(f'  无法读取目录: {e}')
        else:
            print(f'❌ {dir_name} 目录不存在')
    
    print('\n--- 检查已有可执行文件 ---')
    # 检查releases目录中的可执行文件
    if os.path.exists('releases'):
        try:
            for item in os.listdir('releases'):
                item_path = os.path.join('releases', item)
                if os.path.isfile(item_path):
                    size = os.path.getsize(item_path)
                    print(f'文件: {item} ({size:,} 字节)')
                elif os.path.isdir(item_path):
                    try:
                        sub_items = os.listdir(item_path)
                        print(f'目录: {item} (包含 {len(sub_items)} 个项目)')
                    except:
                        print(f'目录: {item} (无法读取)')
        except Exception as e:
            print(f'读取releases目录失败: {e}')
    
    print('\n--- 测试配置文件处理 ---')
    config_files = ['lightrek_config.json', 'lightrek_unified_config.json', 'config_template.json']
    for config_file in config_files:
        if os.path.exists(config_file):
            print(f'✅ 配置文件 {config_file} 存在')
            try:
                import json
                with open(config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                print(f'  配置项数量: {len(config_data)}')
            except Exception as e:
                print(f'  配置文件格式错误: {e}')
        else:
            print(f'❌ 配置文件 {config_file} 不存在')
    
    print('\n--- 测试Web资源 ---')
    web_files = ['web/index.html', 'web/style.css', 'web/app.js']
    for web_file in web_files:
        if os.path.exists(web_file):
            print(f'✅ Web文件 {web_file} 存在')
            size = os.path.getsize(web_file)
            print(f'  大小: {size:,} 字节')
        else:
            print(f'❌ Web文件 {web_file} 不存在')
    
    print('\n--- 系统环境信息 ---')
    print(f'操作系统: {os.name}')
    print(f'平台: {sys.platform}')
    try:
        import platform
        print(f'系统详情: {platform.system()} {platform.release()}')
        print(f'架构: {platform.machine()}')
        print(f'处理器: {platform.processor()}')
    except:
        pass
    
    print('\n--- 磁盘空间检查 ---')
    try:
        import shutil
        total, used, free = shutil.disk_usage('.')
        print(f'磁盘空间: 总计 {total//1024//1024//1024}GB, 已用 {used//1024//1024//1024}GB, 可用 {free//1024//1024//1024}GB')
        
        # 检查是否有足够空间进行编译
        if free > 1024*1024*1024:  # 1GB
            print('✅ 磁盘空间充足')
        else:
            print('⚠️ 磁盘空间可能不足')
    except:
        print('无法获取磁盘空间信息')
    
    print('\n--- 编译建议 ---')
    print('基于测试结果的编译建议:')
    
    if 'build_simple.py' in available_scripts:
        print('1. 推荐使用 build_simple.py 进行编译')
        print('   命令: python build_simple.py')
    
    if os.path.exists('requirements_unified.txt'):
        print('2. 确保安装统一依赖: pip install -r requirements_unified.txt')
    
    print('3. 编译前确保所有测试通过')
    print('4. 编译可能需要较长时间，请耐心等待')
    
    print('编译脚本功能测试完成')

if __name__ == '__main__':
    test_build_scripts()
