{"summary": {"total": 12, "passed": 10, "failed": 2, "success_rate": 83.33333333333334}, "details": [{"test": "演示数据设置", "success": true, "message": "创建了4个测试文件", "timestamp": "2025-07-09 16:25:02"}, {"test": "Web界面可访问性", "success": true, "message": "Web界面正常访问", "timestamp": "2025-07-09 16:25:04"}, {"test": "API端点 /api/sources", "success": true, "message": "响应正常", "timestamp": "2025-07-09 16:25:06"}, {"test": "API端点 /api/targets", "success": true, "message": "响应正常", "timestamp": "2025-07-09 16:25:11"}, {"test": "API端点 /api/tasks", "success": true, "message": "响应正常", "timestamp": "2025-07-09 16:25:13"}, {"test": "API端点 /api/statistics", "success": true, "message": "响应正常", "timestamp": "2025-07-09 16:25:15"}, {"test": "添加存储配置 local_source", "success": true, "message": "配置添加成功", "timestamp": "2025-07-09 16:25:17"}, {"test": "添加存储配置 local_target", "success": true, "message": "配置添加成功", "timestamp": "2025-07-09 16:25:19"}, {"test": "添加存储配置 sftp_test", "success": true, "message": "配置添加成功", "timestamp": "2025-07-09 16:25:21"}, {"test": "连接测试 local", "success": false, "message": "参数验证失败: 名称不能为空", "timestamp": "2025-07-09 16:25:23"}, {"test": "任务创建", "success": true, "message": "同步任务创建成功", "timestamp": "2025-07-09 16:25:25"}, {"test": "任务执行", "success": false, "message": "任务启动失败", "timestamp": "2025-07-09 16:25:27"}]}