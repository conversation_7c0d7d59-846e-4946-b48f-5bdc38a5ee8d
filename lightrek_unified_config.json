{"version": "2.0", "sources": {"e79710f1-0bc0-457c-8e29-401c7b9760f6": {"name": "本地测试目标", "description": "本地文件系统测试目标", "root_path": "demo_data\\target", "storage_type": "local", "created_at": "2025-07-09T16:25:19.218058"}, "eb97ea80-4dd3-403e-b75d-06a323e4367f": {"name": "SFTP测试", "description": "SFTP连接测试", "hostname": "test.example.com", "port": 22, "username": "testuser", "password": "testpass", "root_path": "/home/<USER>", "storage_type": "sftp", "created_at": "2025-07-09T16:25:21.260438"}, "519aefd9-b529-4050-8ee9-89fc557cad3f": {"name": "S3测试存储", "description": "AWS S3兼容存储测试", "access_key": "AKIAIOSFODNN7EXAMPLE", "secret_key": "wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY", "endpoint": "https://s3.amazonaws.com", "region": "us-east-1", "bucket": "test-bucket", "storage_type": "s3", "created_at": "2025-07-09T16:27:20.406567"}, "6ec82b36-0159-4bb4-aba9-a1245be92558": {"name": "SFTP测试存储", "description": "SFTP服务器连接测试", "hostname": "sftp.example.com", "port": 22, "username": "testuser", "password": "testpass", "root_path": "/home/<USER>/data", "storage_type": "sftp", "created_at": "2025-07-09T16:27:22.456919"}, "731bd8f2-62bf-4e46-ba6b-e86ad64b68e3": {"name": "SMB测试存储", "description": "Windows网络共享测试", "hostname": "smb.example.com", "share_name": "shared_folder", "username": "domain\\testuser", "password": "testpass", "domain": "", "storage_type": "smb", "created_at": "2025-07-09T16:27:24.494306"}, "d953bf93-74d9-49c0-9742-ca5e26c30a97": {"name": "FTP测试存储", "description": "FTP服务器连接测试", "hostname": "ftp.example.com", "port": 21, "username": "ftpuser", "password": "ftppass", "use_tls": false, "storage_type": "ftp", "created_at": "2025-07-09T16:27:26.530666"}, "cf76a0da-da39-4dce-8af6-00e4e65b6f5e": {"name": "本地测试存储", "description": "本地文件系统测试", "root_path": "E:\\Augment Code\\S3 Migrate\\demo_data\\source", "storage_type": "local", "created_at": "2025-07-09T16:27:28.569593"}, "dc0c4d2d-2400-419d-8b29-a4ff7f950f55": {"name": "SFTP测试来源", "description": "", "hostname": "**************", "port": 22, "username": "root", "password": "Wa3152421", "root_path": "/root", "storage_type": "sftp", "created_at": "2025-07-09T16:43:39.502923"}, "test_hp_sftp": {"name": "集成测试SFTP源", "type": "sftp_hp", "hostname": "test.example.com", "port": 22, "username": "testuser", "password": "testpass", "max_connections": 8, "max_concurrent_transfers": 16, "performance_profile": "balanced", "storage_type": "sftp_hp", "created_at": "2025-07-10T16:23:17.588528"}, "8f74ee6b-93a0-49a6-8e79-39edba743c0b": {"name": "本地测试源", "description": "本地文件系统测试源", "storage_type": "local", "root_path": "e:\\test2", "created_at": "2025-07-10T17:53:31.794390"}, "878c8305-1ddf-4e41-b343-e70aec68b8c1": {"name": "smb", "description": "", "storage_type": "smb", "hostname": "jayce", "port": 445, "username": "smb", "password": "smbsmb", "domain": "", "share_name": "hlmj", "root_path": "", "created_at": "2025-07-10T18:59:13.638282"}, "49dd4db5-f6ad-4a71-9ee1-4399cb259fcf": {"name": "阿里云1", "description": "来源1", "storage_type": "s3", "endpoint": "https://oss-cn-shanghai.aliyuncs.com", "access_key": "LTAI5tR6qB4obJTCWFrDgkDc", "secret_key": "******************************", "bucket": "jayce-s3test", "region": "cn-shanghai", "created_at": "2025-07-14T12:46:26.530209"}, "fde6ac17-6151-45d4-b07a-6738acb7714c": {"name": "测试SFTP服务器-j", "description": "用于测试的SFTP服务器", "storage_type": "sftp", "hostname": "**************", "port": 22, "username": "root", "password": "Wa3152421", "private_key_path": "", "private_key_passphrase": "", "root_path": "/opt/", "created_at": "2025-07-14T16:11:47.862479"}}, "targets": {"928617e8-c878-4ab3-9857-bbb79674da9a": {"name": "test2", "description": "augment", "root_path": "e:\\cursor", "storage_type": "local", "created_at": "2025-07-09T16:24:38.105128"}, "96c476fe-3a1b-41b2-9db4-316275ea12b8": {"name": "API测试本地目标", "description": "通过API测试添加的本地目标存储", "storage_type": "local", "root_path": "C:/temp/api_test_target", "created_at": "2025-07-09T19:28:43.139913"}, "0fbbfd6f-88e0-45ea-bc63-04c9f76aa193": {"name": "test3", "description": "", "storage_type": "local", "root_path": "e:\\test3", "created_at": "2025-07-10T15:41:56.658152"}, "4c7da710-e9c9-4f82-ae61-56f7dd0b633d": {"name": "阿里云2", "description": "mubiao1", "storage_type": "s3", "endpoint": "https://oss-cn-hangzhou.aliyuncs.com", "access_key": "LTAI5tR6qB4obJTCWFrDgkDc", "secret_key": "******************************", "bucket": "jayce-s3test-2", "region": "cn-hangzhou", "created_at": "2025-07-14T15:16:01.474334"}, "9822d673-d0e7-4a8d-8f10-0c3151bc0eef": {"name": "腾讯云2", "description": "", "storage_type": "s3", "endpoint": "https://cos.ap-shanghai.myqcloud.com", "access_key": "AKIDeYNCta7Zu52mHYaP1OipjRwDICyukNlv", "secret_key": "zjGagXAamjZBcuyoPmuugFZieLn0lRzL", "bucket": "h3c-deom-1319219496", "region": "ap-shanghai", "created_at": "2025-07-14T16:10:08.529530"}, "9196815e-3026-4c49-ab7e-cb18f19e95f4": {"name": "jayce", "description": "", "storage_type": "sftp", "hostname": "**************", "port": 22, "username": "root", "password": "Wa3152421", "private_key_path": "", "private_key_passphrase": "", "root_path": "/opt/", "created_at": "2025-07-14T16:10:59.666354"}, "e8d7568c-0467-4734-b663-7cfb0f3298cb": {"name": "本地目标存储", "description": "本地文件系统目标", "storage_type": "local", "root_path": "/Volumes/HIKSEMI/AUGMENT/test", "created_at": "2025-07-14T17:06:47.294889"}}, "tasks": {"fd7ab0e4-2931-45f9-8971-3f5be1308a10": {"task_id": "fd7ab0e4-2931-45f9-8971-3f5be1308a10", "name": "测试同步任务", "description": "本地到本地的测试同步任务", "source_id": "local_source", "target_id": "local_target", "prefix": "", "sync_mode": "incremental", "delete_extra": false, "file_filter": "", "exclude_filter": "", "max_workers": 20, "retry_times": 5, "retry_delay": 3, "verify_integrity": true, "bandwidth_limit": 0, "chunk_threshold": 100, "chunk_size": 10, "schedule_type": "manual", "schedule_interval": 1, "schedule_time": "00:00", "enabled": true, "created_at": "2025-07-09T16:25:25.368291"}, "0e7f7f29-b5aa-49bc-aee8-311698db50df": {"name": "API测试同步任务", "description": "通过API创建的测试同步任务", "source_id": "d8c715ec-5988-46bf-bae2-908216911ff1", "target_id": "96c476fe-3a1b-41b2-9db4-316275ea12b8", "sync_mode": "incremental", "max_workers": 5, "verify_integrity": true, "created_at": "2025-07-09T19:28:50.286431", "task_id": "0e7f7f29-b5aa-49bc-aee8-311698db50df"}, "1e011904-7972-430e-b6ce-4fe21c651e90": {"name": "阿里云 1to2", "description": "", "source_id": "49dd4db5-f6ad-4a71-9ee1-4399cb259fcf", "target_id": "4c7da710-e9c9-4f82-ae61-56f7dd0b633d", "prefix": "", "sync_mode": "incremental", "delete_extra": false, "verify_integrity": true, "file_filter": "", "exclude_filter": "", "max_workers": 20, "bandwidth_limit": 0, "chunk_threshold": 100, "chunk_size": 10, "retry_times": 5, "retry_delay": 3, "schedule_type": "minutely", "schedule_interval": 10, "schedule_time": "15:57", "enabled": true, "created_at": "2025-07-14T12:26:49.584568", "updated_at": "2025-07-14T16:28:43.080408", "task_id": "1e011904-7972-430e-b6ce-4fe21c651e90", "last_run": "2025-07-14T16:27:22.238711", "last_status": "成功"}, "a11e3415-5482-49bd-8810-8a49711c399e": {"name": "test1", "description": "", "source_id": "49dd4db5-f6ad-4a71-9ee1-4399cb259fcf", "target_id": "e8d7568c-0467-4734-b663-7cfb0f3298cb", "prefix": "", "sync_mode": "incremental", "verify_integrity": true, "file_filter": "", "exclude_filter": "", "max_workers": 20, "bandwidth_limit": 0, "chunk_threshold": 100, "chunk_size": 10, "retry_times": 5, "retry_delay": 3, "schedule_type": "manual", "schedule_interval": 1, "schedule_time": "00:00", "enabled": true, "delete_extra": false, "created_at": "2025-07-14T15:05:53.270819", "updated_at": "2025-07-14T17:07:14.438123", "task_id": "a11e3415-5482-49bd-8810-8a49711c399e", "last_run": "2025-07-14T17:07:14.438119", "last_status": "成功"}, "4f498954-a2e6-4cb0-b5f4-b6c408a8e7c4": {"name": "sftptolocal", "description": "", "source_id": "fde6ac17-6151-45d4-b07a-6738acb7714c", "target_id": "e8d7568c-0467-4734-b663-7cfb0f3298cb", "prefix": "", "sync_mode": "incremental", "verify_integrity": true, "file_filter": "", "exclude_filter": "", "max_workers": 20, "bandwidth_limit": 0, "chunk_threshold": 100, "chunk_size": 10, "retry_times": 5, "retry_delay": 3, "schedule_type": "manual", "schedule_interval": 1, "schedule_time": "00:00", "enabled": true, "delete_extra": false, "created_at": "2025-07-14T16:11:34.702346", "updated_at": "2025-07-14T17:11:48.507062", "task_id": "4f498954-a2e6-4cb0-b5f4-b6c408a8e7c4", "last_run": "2025-07-14T17:11:48.507058", "last_status": "成功"}}, "global_settings": {"default_max_workers": 20, "default_retry_times": 5, "default_retry_delay": 3, "default_chunk_size_mb": 10, "default_bandwidth_limit": 0}, "optimization": {"max_workers": 4, "chunk_size_mb": 10, "retry_times": 5, "enable_parallel_scan": true, "enable_cache": false, "cache_ttl_hours": 48, "success": true, "config": {"max_workers": 6, "chunk_size_mb": 12, "retry_times": 5, "enable_parallel_scan": true, "enable_cache": true, "cache_ttl_hours": 48, "success": true, "config": {"max_workers": 8, "chunk_size_mb": 10, "retry_times": 5, "enable_parallel_scan": true, "enable_cache": true, "cache_ttl_hours": 48}}, "retry_delay": 3, "verify_integrity": true}}