#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试SMB路径修复
"""

import smb_storage_adapter
from storage_abstraction import StorageFactory, SMBStorageConfig, StorageType

def test_path_normalization():
    """测试路径规范化"""
    print("🧪 测试SMB路径规范化")
    
    config = SMBStorageConfig(
        storage_type=StorageType.SMB,
        hostname='Jayce',
        port=445,
        username='smb',
        password='smbsmb',
        domain='WORKGROUP',
        share_name='hlmj',
        root_path=''
    )
    
    adapter = StorageFactory.create_adapter(config)
    
    # 测试路径规范化
    test_paths = [
        '',
        '398.xml',
        '398_MonitorData.ini', 
        'BugCrashReporter.exe',
        'subfolder/file.txt',
        '/398.xml',
        '\\398.xml'
    ]
    
    print("路径规范化测试:")
    for path in test_paths:
        try:
            normalized = adapter._normalize_path(path)
            print(f'  原始: "{path}" -> 规范化: "{normalized}"')
        except Exception as e:
            print(f'  原始: "{path}" -> 错误: {e}')

def test_file_download():
    """测试文件下载"""
    print("\n🧪 测试SMB文件下载")
    
    config = SMBStorageConfig(
        storage_type=StorageType.SMB,
        hostname='Jayce',
        port=445,
        username='smb',
        password='smbsmb',
        domain='WORKGROUP',
        share_name='hlmj',
        root_path=''
    )
    
    adapter = StorageFactory.create_adapter(config)
    
    # 先获取文件列表
    print("📁 获取文件列表...")
    try:
        result = adapter.list_files("", max_keys=3)
        print(f"找到 {len(result.files)} 个文件")
        
        if result.files:
            # 测试下载第一个文件
            test_file = result.files[0]
            print(f"\n🎯 测试下载: {test_file.key}")
            
            # 显示规范化后的路径
            normalized_path = adapter._normalize_path(test_file.key)
            print(f"规范化路径: \"{normalized_path}\"")
            
            # 尝试下载
            data = adapter.get_file(test_file.key)
            if data:
                print(f"✅ 下载成功: {len(data)} bytes")
                print(f"前16字节: {data[:16].hex()}")
            else:
                print("❌ 下载失败")
        else:
            print("❌ 没有找到文件")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_path_normalization()
    test_file_download()
