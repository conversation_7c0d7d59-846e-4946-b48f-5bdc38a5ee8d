#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
迁移现有SFTP配置到高性能版本
"""

import json
import logging
import os
from pathlib import Path

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def migrate_sftp_configs():
    """迁移SFTP配置"""
    logger = setup_logging()
    
    logger.info("🚀 开始迁移SFTP配置到高性能版本")
    
    try:
        from unified_config_manager import UnifiedConfigManager
        
        config_manager = UnifiedConfigManager()
        migrated_count = 0
        
        # 获取所有数据源
        logger.info("📁 检查数据源配置...")
        sources = config_manager.get_all_sources()
        
        for source_id, source_config in sources.items():
            if source_config.get('type') == 'sftp':
                logger.info(f"🔄 迁移数据源: {source_config.get('name', source_id)}")
                
                # 更新配置类型为高性能SFTP
                source_config['type'] = 'sftp_hp'
                
                # 添加性能优化配置
                if 'max_connections' not in source_config:
                    source_config['max_connections'] = 8
                if 'max_concurrent_transfers' not in source_config:
                    source_config['max_concurrent_transfers'] = 16
                if 'chunk_size' not in source_config:
                    source_config['chunk_size'] = 64 * 1024  # 64KB
                if 'compression' not in source_config:
                    source_config['compression'] = True
                if 'enable_stat_cache' not in source_config:
                    source_config['enable_stat_cache'] = True
                if 'performance_profile' not in source_config:
                    source_config['performance_profile'] = 'balanced'
                
                # 保存更新的配置
                config_manager.update_source(source_id, source_config)
                migrated_count += 1
                logger.info(f"✅ 数据源迁移完成: {source_config.get('name')}")
        
        # 获取所有目标存储
        logger.info("🎯 检查目标存储配置...")
        targets = config_manager.get_all_targets()
        
        for target_id, target_config in targets.items():
            if target_config.get('type') == 'sftp':
                logger.info(f"🔄 迁移目标存储: {target_config.get('name', target_id)}")
                
                # 更新配置类型为高性能SFTP
                target_config['type'] = 'sftp_hp'
                
                # 添加性能优化配置
                if 'max_connections' not in target_config:
                    target_config['max_connections'] = 8
                if 'max_concurrent_transfers' not in target_config:
                    target_config['max_concurrent_transfers'] = 16
                if 'chunk_size' not in target_config:
                    target_config['chunk_size'] = 64 * 1024  # 64KB
                if 'compression' not in target_config:
                    target_config['compression'] = True
                if 'enable_stat_cache' not in target_config:
                    target_config['enable_stat_cache'] = True
                if 'performance_profile' not in target_config:
                    target_config['performance_profile'] = 'balanced'
                
                # 保存更新的配置
                config_manager.update_target(target_id, target_config)
                migrated_count += 1
                logger.info(f"✅ 目标存储迁移完成: {target_config.get('name')}")
        
        logger.info(f"🎉 迁移完成！共迁移 {migrated_count} 个SFTP配置")
        
        if migrated_count > 0:
            logger.info("📋 迁移后的优化特性:")
            logger.info("  ✅ 连接池: 8个预建连接")
            logger.info("  ✅ 并发传输: 16个并发任务")
            logger.info("  ✅ 智能缓存: 启用stat缓存")
            logger.info("  ✅ 数据压缩: 启用传输压缩")
            logger.info("  ✅ 性能配置: 平衡模式")
            logger.info("")
            logger.info("💡 建议:")
            logger.info("  - 重启 lightrek.py 以应用新配置")
            logger.info("  - 监控任务执行性能")
            logger.info("  - 根据网络条件调整性能配置")
        else:
            logger.info("ℹ️ 没有找到需要迁移的SFTP配置")
        
        return True
        
    except ImportError as e:
        logger.error(f"❌ 导入模块失败: {e}")
        logger.error("请确保所有依赖模块都已正确安装")
        return False
    except Exception as e:
        logger.error(f"❌ 迁移失败: {e}")
        return False

def create_performance_test():
    """创建性能测试任务"""
    logger = logging.getLogger(__name__)
    
    try:
        from unified_config_manager import UnifiedConfigManager
        
        config_manager = UnifiedConfigManager()
        
        # 检查是否有高性能SFTP配置
        sources = config_manager.get_all_sources()
        hp_sftp_sources = [
            (sid, sconfig) for sid, sconfig in sources.items() 
            if sconfig.get('type') == 'sftp_hp'
        ]
        
        if not hp_sftp_sources:
            logger.info("ℹ️ 没有找到高性能SFTP数据源，跳过性能测试任务创建")
            return True
        
        # 创建本地测试目标
        test_target_id = "hp_sftp_test_target"
        test_target_config = {
            'name': '高性能SFTP测试目标',
            'type': 'local',
            'path': 'C:\\temp\\hp_sftp_test',
            'description': '用于测试高性能SFTP的本地目标'
        }
        
        config_manager.add_target(test_target_id, 'local', test_target_config)
        logger.info("✅ 创建测试目标存储")
        
        # 为每个高性能SFTP源创建测试任务
        for source_id, source_config in hp_sftp_sources:
            task_id = f"hp_sftp_test_{source_id[:8]}"
            task_config = {
                'name': f'高性能SFTP测试 - {source_config.get("name", "未命名")}',
                'description': '测试高性能SFTP适配器的性能和稳定性',
                'source_id': source_id,
                'target_id': test_target_id,
                'sync_mode': 'incremental',
                'verify_integrity': True,
                'max_workers': 4,
                'retry_times': 2
            }
            
            config_manager.add_task(task_id, task_config)
            logger.info(f"✅ 创建测试任务: {task_config['name']}")
        
        logger.info("🧪 性能测试任务创建完成")
        logger.info("💡 您可以在Web界面中运行这些测试任务来验证高性能SFTP的效果")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 创建性能测试失败: {e}")
        return False

def main():
    """主函数"""
    logger = setup_logging()
    
    logger.info("🔧 LightRek 高性能SFTP迁移工具")
    logger.info("=" * 50)
    
    # 步骤1: 迁移配置
    if migrate_sftp_configs():
        logger.info("✅ 配置迁移成功")
        
        # 步骤2: 创建性能测试任务
        if create_performance_test():
            logger.info("✅ 性能测试任务创建成功")
        else:
            logger.warning("⚠️ 性能测试任务创建失败")
    else:
        logger.error("❌ 配置迁移失败")
        return 1
    
    logger.info("=" * 50)
    logger.info("🎉 高性能SFTP迁移完成！")
    logger.info("")
    logger.info("📋 后续步骤:")
    logger.info("1. 重启 lightrek.py 应用新配置")
    logger.info("2. 在Web界面中查看迁移后的SFTP配置")
    logger.info("3. 运行测试任务验证性能提升")
    logger.info("4. 监控任务执行日志")
    logger.info("")
    logger.info("🔧 性能调优建议:")
    logger.info("- 高带宽网络: 可增加max_connections到16")
    logger.info("- 低延迟网络: 可增加max_concurrent_transfers到32")
    logger.info("- 不稳定网络: 可启用更多重试和缓存")
    
    return 0

if __name__ == "__main__":
    exit(main())
