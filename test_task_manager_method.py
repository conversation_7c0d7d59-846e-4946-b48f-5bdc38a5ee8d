#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试任务管理器的SMB下载方法
"""

import logging
import sys
import os

# 设置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_task_manager_smb_method():
    """测试任务管理器的SMB下载方法"""
    print("🧪 测试任务管理器的SMB下载方法")
    
    try:
        from unified_task_manager import UnifiedTaskManager
        from unified_config_manager import UnifiedConfigManager
        from storage_abstraction import SMBStorageConfig, StorageType

        # 创建配置管理器和任务管理器
        config_manager = UnifiedConfigManager()
        task_manager = UnifiedTaskManager(config_manager)
        
        # 创建SMB配置
        smb_config = SMBStorageConfig(
            storage_type=StorageType.SMB,
            hostname='<PERSON><PERSON>',
            port=445,
            username='smb',
            password='smbsmb',
            domain='WORKGROUP',
            share_name='hlmj',
            root_path=''
        )
        
        # 测试文件
        test_file = '398.xml'
        
        print(f"测试下载: {test_file}")
        
        # 调用任务管理器的SMB下载方法
        data = task_manager._download_smb_file_with_subprocess(smb_config, test_file)
        
        if data:
            print(f"✅ 成功: {len(data)} bytes")
            return True
        else:
            print("❌ 失败: 返回None")
            return False
            
    except Exception as e:
        print(f"❌ 异常: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    if test_task_manager_smb_method():
        print("🎉 任务管理器SMB方法测试成功！")
        sys.exit(0)
    else:
        print("❌ 任务管理器SMB方法测试失败！")
        sys.exit(1)
