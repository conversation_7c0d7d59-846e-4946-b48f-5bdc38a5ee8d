#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接测试SMB下载（使用成功的方法）
"""

import smb_storage_adapter
from storage_abstraction import StorageFactory, SMBStorageConfig, StorageType

def test_direct_download():
    """直接测试文件下载"""
    print("🧪 直接测试SMB文件下载")
    
    config = SMBStorageConfig(
        storage_type=StorageType.SMB,
        hostname='<PERSON><PERSON>',
        port=445,
        username='smb',
        password='smbsmb',
        domain='WORKGROUP',
        share_name='hlmj',
        root_path=''
    )
    
    adapter = StorageFactory.create_adapter(config)
    
    try:
        # 使用调试中成功的方法
        from smbprotocol.open import CreateDisposition, FilePipePrinterAccessMask
        
        # 建立连接
        adapter._connect()
        print("✅ SMB连接成功")
        
        # 测试文件
        test_file = "398.xml"
        print(f"🎯 测试下载: {test_file}")
        
        # 直接创建文件对象（这个方法在调试中成功了）
        file_obj = adapter._create_file_object(
            test_file,
            desired_access=FilePipePrinterAccessMask.GENERIC_READ,
            create_disposition=CreateDisposition.FILE_OPEN
        )
        print("✅ 文件对象创建成功")
        
        # 读取文件内容
        try:
            # 分块读取
            chunk_size = 64 * 1024  # 64KB
            all_data = bytearray()
            offset = 0
            
            while True:
                chunk = file_obj.read(offset, chunk_size)
                if not chunk or len(chunk) == 0:
                    break
                all_data.extend(chunk)
                offset += len(chunk)
                
                # 如果读取的数据少于请求的大小，说明已到文件末尾
                if len(chunk) < chunk_size:
                    break
                    
                # 防止无限循环
                if len(all_data) > 10 * 1024 * 1024:  # 10MB limit
                    break
            
            file_obj.close()
            
            if all_data:
                print(f"✅ 下载成功: {len(all_data)} bytes")
                print(f"📊 前32字节: {all_data[:32].hex()}")
                
                # 尝试解码为文本（如果是文本文件）
                try:
                    text_preview = all_data[:200].decode('utf-8', errors='ignore')
                    print(f"📄 文本预览: {text_preview[:100]}...")
                except:
                    pass
                
                return True
            else:
                print("❌ 文件为空")
                return False
                
        except Exception as read_error:
            file_obj.close()
            print(f"❌ 读取失败: {read_error}")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        try:
            adapter._disconnect()
        except:
            pass

def test_multiple_files():
    """测试多个文件下载"""
    print("\n🧪 测试多个文件下载")
    
    config = SMBStorageConfig(
        storage_type=StorageType.SMB,
        hostname='Jayce',
        port=445,
        username='smb',
        password='smbsmb',
        domain='WORKGROUP',
        share_name='hlmj',
        root_path=''
    )
    
    adapter = StorageFactory.create_adapter(config)
    
    # 测试文件列表
    test_files = ["398.xml", "398_MonitorData.ini", "BugCrashReporter.exe"]
    
    success_count = 0
    
    for test_file in test_files:
        print(f"\n📥 下载文件: {test_file}")
        
        try:
            # 每个文件使用独立连接
            adapter._disconnect()
            adapter._connect()
            
            from smbprotocol.open import CreateDisposition, FilePipePrinterAccessMask
            
            # 创建文件对象
            file_obj = adapter._create_file_object(
                test_file,
                desired_access=FilePipePrinterAccessMask.GENERIC_READ,
                create_disposition=CreateDisposition.FILE_OPEN
            )
            
            # 读取前1KB作为测试
            data = file_obj.read(0, 1024)
            file_obj.close()
            
            if data:
                print(f"  ✅ 成功: {len(data)} bytes (前1KB)")
                success_count += 1
            else:
                print(f"  ❌ 失败: 数据为空")
                
        except Exception as e:
            print(f"  ❌ 失败: {e}")
        finally:
            try:
                adapter._disconnect()
            except:
                pass
    
    print(f"\n📊 结果: {success_count}/{len(test_files)} 成功")
    return success_count > 0

if __name__ == "__main__":
    success1 = test_direct_download()
    success2 = test_multiple_files()
    
    if success1 and success2:
        print("\n🎉 SMB文件下载功能正常！")
        print("现在可以修复 get_file 方法了。")
    else:
        print("\n❌ 还需要进一步调试。")
