#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试SMB文件访问问题
"""

import smb_storage_adapter
from storage_abstraction import StorageFactory, SMBStorageConfig, StorageType

def debug_smb_file_access():
    """调试SMB文件访问"""
    print("🔍 调试SMB文件访问问题")
    
    config = SMBStorageConfig(
        storage_type=StorageType.SMB,
        hostname='<PERSON><PERSON>',
        port=445,
        username='smb',
        password='smbsmb',
        domain='WORKGROUP',
        share_name='hlmj',
        root_path=''
    )
    
    adapter = StorageFactory.create_adapter(config)
    
    try:
        # 建立连接
        adapter._connect()
        print("✅ SMB连接成功")
        
        # 手动测试文件访问
        from smbprotocol.open import CreateDisposition, CreateOptions, FilePipePrinterAccessMask
        from smbprotocol.file_info import FileInformationClass, FileAttributes

        # 使用正确的访问掩码
        FileAccessMask = FilePipePrinterAccessMask
        
        # 测试不同的文件路径格式
        test_files = [
            "398.xml",
            ".\\398.xml", 
            "\\398.xml",
            "398.xml",
        ]
        
        for file_path in test_files:
            print(f"\n🧪 测试文件路径: '{file_path}'")
            
            try:
                # 尝试创建文件对象
                file_obj = adapter._create_file_object(
                    file_path,
                    desired_access=FileAccessMask.GENERIC_READ,
                    create_disposition=CreateDisposition.FILE_OPEN
                )
                print(f"  ✅ 文件对象创建成功")
                
                # 尝试读取少量数据
                try:
                    data = file_obj.read(0, 100)  # 读取前100字节
                    print(f"  ✅ 读取成功: {len(data)} bytes")
                    print(f"  📊 数据预览: {data[:20].hex()}")
                except Exception as read_error:
                    print(f"  ❌ 读取失败: {read_error}")
                
                file_obj.close()
                
            except Exception as e:
                print(f"  ❌ 文件访问失败: {e}")
        
        # 测试目录列表中的原始文件名
        print(f"\n📁 获取目录列表中的原始文件信息...")
        
        # 打开根目录
        dir_obj = adapter._create_file_object(
            "",
            desired_access=FileAccessMask.GENERIC_READ,
            create_disposition=CreateDisposition.FILE_OPEN,
            create_options=CreateOptions.FILE_DIRECTORY_FILE,
            file_attributes=FileAttributes.FILE_ATTRIBUTE_DIRECTORY
        )
        
        # 查询目录内容
        items = dir_obj.query_directory(
            pattern="*",
            file_information_class=FileInformationClass.FILE_DIRECTORY_INFORMATION
        )
        
        print(f"找到 {len(items)} 个项目:")
        for i, item in enumerate(items[:5]):  # 只显示前5个
            file_name = item['file_name']
            if hasattr(file_name, 'get_value'):
                file_name = file_name.get_value()
            if isinstance(file_name, bytes):
                file_name = file_name.decode('utf-8', errors='ignore')
            
            if file_name not in ['.', '..']:
                print(f"  {i+1}. 原始文件名: '{file_name}' (类型: {type(file_name)})")
                
                # 尝试直接使用这个文件名访问文件
                try:
                    test_file_obj = adapter._create_file_object(
                        file_name,
                        desired_access=FileAccessMask.GENERIC_READ,
                        create_disposition=CreateDisposition.FILE_OPEN
                    )
                    print(f"     ✅ 直接访问成功")
                    test_file_obj.close()
                except Exception as e:
                    print(f"     ❌ 直接访问失败: {e}")
        
        dir_obj.close()
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        try:
            adapter._disconnect()
        except:
            pass

if __name__ == "__main__":
    debug_smb_file_access()
