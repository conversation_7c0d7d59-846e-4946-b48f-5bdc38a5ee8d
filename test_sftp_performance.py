#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高性能SFTP适配器性能测试
"""

import asyncio
import time
import logging
from high_performance_sftp_adapter import HighPerformanceSFTPAdapter, SFTPPerformanceOptimizer

async def test_sftp_performance():
    """测试SFTP性能"""
    logger = logging.getLogger(__name__)
    
    # 创建测试配置
    config = SFTPPerformanceOptimizer.create_optimized_config(
        hostname="your-sftp-server.com",
        username="testuser",
        password="testpass",
        performance_profile="high_performance"
    )
    
    adapter = HighPerformanceSFTPAdapter(config)
    
    try:
        # 连接测试
        logger.info("🔗 测试连接...")
        start_time = time.time()
        
        if await adapter.connect():
            connect_time = time.time() - start_time
            logger.info(f"✅ 连接成功，耗时: {connect_time:.2f}秒")
            
            # 获取连接统计
            stats = await adapter.get_connection_stats()
            logger.info(f"📊 连接统计: {stats}")
            
            # 文件列表测试
            logger.info("📂 测试文件列表...")
            start_time = time.time()
            
            file_count = 0
            async for file_meta in adapter.list_files():
                file_count += 1
                if file_count >= 100:  # 限制测试文件数量
                    break
            
            list_time = time.time() - start_time
            logger.info(f"✅ 列出 {file_count} 个文件，耗时: {list_time:.2f}秒")
            
            # 性能指标
            if file_count > 0:
                files_per_second = file_count / list_time
                logger.info(f"📈 文件列表性能: {files_per_second:.1f} 文件/秒")
            
        else:
            logger.error("❌ 连接失败")
            
    except Exception as e:
        logger.error(f"测试异常: {e}")
    finally:
        await adapter.disconnect()

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    asyncio.run(test_sftp_performance())
