#!/usr/bin/env python3
"""
调试SMB适配器问题
"""

import sys
sys.path.insert(0, '.')

def debug_smb_adapter():
    """调试SMB适配器"""
    print("🔍 调试SMB适配器问题")
    print("=" * 50)
    
    print("1. 检查smbprotocol库...")
    try:
        from smbprotocol.connection import Connection
        from smbprotocol.session import Session
        from smbprotocol.tree import TreeConnect
        from smbprotocol.open import Open, CreateDisposition, CreateOptions, DirectoryAccessMask
        from smbprotocol.file_info import FileAttributes
        from smbprotocol import exceptions as smb_exceptions
        print("✅ smbprotocol库导入成功")
    except ImportError as e:
        print(f"❌ smbprotocol库导入失败: {e}")
        return
    
    print("\n2. 导入存储抽象...")
    try:
        from storage_abstraction import StorageFactory, StorageType
        print("✅ 存储抽象导入成功")
        print(f"初始支持的类型: {[t.value for t in StorageFactory.get_supported_types()]}")
    except Exception as e:
        print(f"❌ 存储抽象导入失败: {e}")
        return
    
    print("\n3. 导入SMB适配器...")
    try:
        import smb_storage_adapter
        print("✅ SMB适配器导入成功")
        print(f"导入后支持的类型: {[t.value for t in StorageFactory.get_supported_types()]}")
        
        # 检查SMBPROTOCOL_AVAILABLE变量
        print(f"SMBPROTOCOL_AVAILABLE: {smb_storage_adapter.SMBPROTOCOL_AVAILABLE}")
        
    except Exception as e:
        print(f"❌ SMB适配器导入失败: {e}")
        import traceback
        traceback.print_exc()
        return
    
    print("\n4. 手动注册SMB适配器...")
    try:
        if hasattr(smb_storage_adapter, 'SMBStorageAdapter'):
            StorageFactory.register_adapter(StorageType.SMB, smb_storage_adapter.SMBStorageAdapter)
            print("✅ SMB适配器手动注册成功")
            print(f"注册后支持的类型: {[t.value for t in StorageFactory.get_supported_types()]}")
        else:
            print("❌ SMBStorageAdapter类不存在")
    except Exception as e:
        print(f"❌ SMB适配器手动注册失败: {e}")
    
    print("\n5. 测试SMB适配器创建...")
    try:
        from storage_abstraction import SMBStorageConfig
        config = SMBStorageConfig(
            hostname="test.example.com",
            username="test",
            password="test",
            share_name="shared"
        )
        adapter = StorageFactory.create_adapter(config)
        print(f"✅ SMB适配器创建成功: {type(adapter).__name__}")
    except Exception as e:
        print(f"❌ SMB适配器创建失败: {e}")

if __name__ == "__main__":
    debug_smb_adapter()
