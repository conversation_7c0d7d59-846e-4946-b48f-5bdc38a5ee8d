#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据库表结构
"""

import sqlite3
import logging

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def check_table_schema():
    """检查表结构"""
    logger = setup_logging()
    
    try:
        with sqlite3.connect("lightrek_data.db") as conn:
            cursor = conn.cursor()
            
            # 检查所有表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            
            logger.info(f"📋 数据库中的表: {tables}")
            
            for table in tables:
                if table.startswith('sqlite_'):
                    continue
                    
                logger.info(f"\n📊 表 {table} 的结构:")
                cursor.execute(f"PRAGMA table_info({table})")
                columns = cursor.fetchall()
                
                for col in columns:
                    logger.info(f"  🔹 {col[1]} ({col[2]}) - {'NOT NULL' if col[3] else 'NULL'} - {'PK' if col[5] else ''}")
                
                # 显示一些示例数据
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                logger.info(f"  📊 记录数: {count}")
                
                if count > 0:
                    cursor.execute(f"SELECT * FROM {table} LIMIT 3")
                    rows = cursor.fetchall()
                    logger.info(f"  📋 示例数据:")
                    for i, row in enumerate(rows):
                        logger.info(f"    {i+1}: {row}")
                        
    except Exception as e:
        logger.error(f"❌ 检查表结构失败: {e}")

if __name__ == "__main__":
    check_table_schema()
