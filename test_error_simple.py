#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的错误处理测试
"""

import os
import tempfile
import json
from unified_config_manager import UnifiedConfigManager
from unified_task_manager import UnifiedTaskManager
from storage_abstraction import LocalStorageConfig
from local_storage_adapter import LocalStorageAdapter

def test_error_handling_simple():
    """简化的错误处理测试"""
    print('=== 简化错误处理测试 ===')
    
    # 创建临时配置文件
    temp_config = tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False)
    temp_config.close()
    config_file = temp_config.name
    
    try:
        print('\n--- 测试正常配置管理器创建 ---')
        config_manager = UnifiedConfigManager(config_file)
        print(f'配置管理器创建成功，版本: {config_manager.config_data.get("version")}')
        
        print('\n--- 测试添加有效本地存储 ---')
        # 创建临时目录
        temp_dir = tempfile.mkdtemp()
        
        local_config = {
            'name': '测试本地存储',
            'description': '用于测试的本地存储',
            'root_path': temp_dir
        }
        
        success = config_manager.add_source('test_local', 'local', local_config)
        print(f'添加本地存储源: {success}')
        
        success = config_manager.add_target('test_local_target', 'local', local_config)
        print(f'添加本地存储目标: {success}')
        
        print('\n--- 测试获取配置 ---')
        source = config_manager.get_source('test_local')
        target = config_manager.get_target('test_local_target')
        print(f'获取源配置: {source is not None}')
        print(f'获取目标配置: {target is not None}')
        
        if source:
            print(f'源配置详情: name={source.name}, type={source.storage_type}, path={source.root_path}')
        
        print('\n--- 测试任务管理器 ---')
        task_manager = UnifiedTaskManager(config_manager)
        
        # 创建有效任务
        task_id = task_manager.create_task(
            name='测试任务',
            description='简单测试任务',
            source_id='test_local',
            target_id='test_local_target',
            sync_mode='incremental'
        )
        print(f'创建任务: {task_id is not None}')
        
        if task_id:
            task = task_manager.get_task(task_id)
            print(f'获取任务: {task is not None}')
            if task:
                print(f'任务详情: name={task.name}, mode={task.sync_mode}')
        
        print('\n--- 测试存储适配器错误处理 ---')
        
        # 测试本地存储适配器
        local_storage_config = LocalStorageConfig(
            name='测试存储',
            root_path=temp_dir
        )
        adapter = LocalStorageAdapter(local_storage_config)
        
        # 测试连接
        success, message = adapter.test_connection()
        print(f'存储连接测试: {success}, 消息: {message}')
        
        # 测试文件操作错误
        print('\n--- 测试文件操作错误 ---')
        
        # 获取不存在的文件
        nonexistent = adapter.get_file('nonexistent.txt')
        print(f'获取不存在文件: {nonexistent is None}')
        
        # 检查不存在文件
        exists = adapter.file_exists('nonexistent.txt')
        print(f'检查不存在文件: {exists}')
        
        # 删除不存在文件
        delete_result = adapter.delete_file('nonexistent.txt')
        print(f'删除不存在文件: {delete_result}')
        
        # 获取不存在文件元数据
        metadata = adapter.get_file_metadata('nonexistent.txt')
        print(f'获取不存在文件元数据: {metadata is None}')
        
        print('\n--- 测试无效配置处理 ---')
        
        # 测试无效存储类型
        invalid_result = config_manager.add_source('invalid_test', 'invalid_type', {
            'name': '无效类型测试',
            'some_param': 'value'
        })
        print(f'添加无效存储类型: {invalid_result}')
        
        # 测试缺少必需参数
        incomplete_result = config_manager.add_source('incomplete_test', 'local', {
            'name': '不完整配置'
            # 缺少root_path
        })
        print(f'添加不完整配置: {incomplete_result}')
        
        print('\n--- 测试任务错误处理 ---')
        
        # 测试无效源ID
        try:
            invalid_task = task_manager.create_task(
                name='无效源任务',
                source_id='nonexistent_source',
                target_id='test_local_target'
            )
            print(f'无效源任务创建: {invalid_task is not None}')
        except Exception as e:
            print(f'无效源任务异常（预期）: {e}')
        
        # 测试无效目标ID
        try:
            invalid_task = task_manager.create_task(
                name='无效目标任务',
                source_id='test_local',
                target_id='nonexistent_target'
            )
            print(f'无效目标任务创建: {invalid_task is not None}')
        except Exception as e:
            print(f'无效目标任务异常（预期）: {e}')
        
        print('\n--- 测试边界值 ---')
        
        # 测试极值参数
        try:
            extreme_task = task_manager.create_task(
                name='极值测试',
                source_id='test_local',
                target_id='test_local_target',
                max_workers=1000,
                retry_times=100,
                chunk_size=10000
            )
            print(f'极值任务创建: {extreme_task is not None}')
        except Exception as e:
            print(f'极值任务异常: {e}')
        
        # 测试零值参数
        try:
            zero_task = task_manager.create_task(
                name='零值测试',
                source_id='test_local',
                target_id='test_local_target',
                max_workers=0,
                retry_times=0
            )
            print(f'零值任务创建: {zero_task is not None}')
        except Exception as e:
            print(f'零值任务异常: {e}')
        
        print('\n--- 测试网络存储连接错误 ---')
        
        # 测试无效S3配置
        invalid_s3_config = {
            'name': '无效S3',
            'access_key': 'invalid',
            'secret_key': 'invalid',
            'endpoint': 'https://invalid.com',
            'region': 'invalid',
            'bucket': 'invalid'
        }
        
        s3_result = config_manager.add_source('invalid_s3', 's3', invalid_s3_config)
        print(f'添加无效S3配置: {s3_result}')
        
        if s3_result:
            # 测试连接
            try:
                s3_source = config_manager.get_source('invalid_s3')
                if s3_source:
                    from s3_storage_adapter import S3StorageAdapter
                    s3_adapter = S3StorageAdapter(s3_source)
                    success, message = s3_adapter.test_connection()
                    print(f'S3连接测试: {success}, 消息: {message[:100]}...')
            except Exception as e:
                print(f'S3连接测试异常: {e}')
        
        print('\n--- 测试配置统计 ---')
        all_sources = config_manager.get_all_sources()
        all_targets = config_manager.get_all_targets()
        all_tasks = task_manager.get_all_tasks()
        
        print(f'总配置统计:')
        print(f'  数据源: {len(all_sources)}')
        print(f'  目标存储: {len(all_targets)}')
        print(f'  任务: {len(all_tasks)}')
        
        # 清理临时目录
        import shutil
        shutil.rmtree(temp_dir, ignore_errors=True)
        
    finally:
        # 清理临时配置文件
        try:
            if os.path.exists(config_file):
                os.unlink(config_file)
                print(f'\n清理临时配置文件: {config_file}')
        except Exception as e:
            print(f'\n清理配置文件失败: {e}')
    
    print('简化错误处理测试完成')

if __name__ == '__main__':
    test_error_handling_simple()
