#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LightRek 深度功能验证测试
验证所有功能、配置逻辑和参数有效性
"""

import requests
import json
import time
import tempfile
import os
import threading
from pathlib import Path
from typing import Dict, List, Any

class DeepFunctionalityTester:
    """深度功能测试器"""
    
    def __init__(self, base_url="http://localhost:8001"):
        self.base_url = base_url
        self.test_results = {}
        self.test_data = {}
        
    def run_deep_test(self):
        """运行深度测试"""
        print("🔬 LightRek 深度功能验证测试开始")
        print("=" * 80)
        
        # 1. 存储配置深度测试
        self.test_storage_configurations()
        
        # 2. 参数验证测试
        self.test_parameter_validation()
        
        # 3. 配置逻辑科学性测试
        self.test_configuration_logic()
        
        # 4. API完整性测试
        self.test_api_completeness()
        
        # 5. 错误处理测试
        self.test_error_handling()
        
        # 6. 边界条件测试
        self.test_boundary_conditions()
        
        # 7. Web界面功能测试
        self.test_web_interface_features()
        
        # 8. 实际传输测试
        self.test_actual_transfer()
        
        # 9. 生成详细报告
        self.generate_deep_report()
        
    def test_storage_configurations(self):
        """测试所有存储类型的配置"""
        print("\n💾 1. 存储配置深度测试")
        print("-" * 50)
        
        storage_configs = {
            "s3_aws": {
                "name": "AWS S3测试",
                "storage_type": "s3",
                "endpoint": "https://s3.amazonaws.com",
                "region": "us-east-1",
                "access_key": "AKIAIOSFODNN7EXAMPLE",
                "secret_key": "wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY",
                "bucket": "test-bucket"
            },
            "s3_aliyun": {
                "name": "阿里云OSS测试",
                "storage_type": "s3",
                "endpoint": "https://oss-cn-shanghai.aliyuncs.com",
                "region": "cn-shanghai",
                "access_key": "LTAI4G***EXAMPLE",
                "secret_key": "************************",
                "bucket": "test-bucket-aliyun"
            },
            "sftp": {
                "name": "SFTP测试",
                "storage_type": "sftp",
                "host": "example.com",
                "port": 22,
                "username": "testuser",
                "password": "testpass",
                "root_path": "/home/<USER>"
            },
            "smb": {
                "name": "SMB测试",
                "storage_type": "smb",
                "host": "*************",
                "port": 445,
                "username": "testuser",
                "password": "testpass",
                "share_name": "shared",
                "root_path": "/"
            },
            "ftp": {
                "name": "FTP测试",
                "storage_type": "ftp",
                "host": "ftp.example.com",
                "port": 21,
                "username": "testuser",
                "password": "testpass",
                "root_path": "/",
                "use_tls": False
            }
        }
        
        config_results = {}
        for storage_type, config in storage_configs.items():
            print(f"\n🔧 测试 {storage_type.upper()} 配置...")
            
            try:
                # 测试添加配置
                response = requests.post(
                    f"{self.base_url}/api/sources",
                    json=config,
                    timeout=10
                )
                
                if response.status_code == 200:
                    print(f"✅ {storage_type}: 配置添加成功")
                    
                    # 测试配置验证
                    validation_result = self.validate_storage_config(config)
                    config_results[storage_type] = {
                        "add_success": True,
                        "validation": validation_result
                    }
                else:
                    print(f"❌ {storage_type}: 配置添加失败 - HTTP {response.status_code}")
                    config_results[storage_type] = {
                        "add_success": False,
                        "error": f"HTTP {response.status_code}"
                    }
                    
            except Exception as e:
                print(f"❌ {storage_type}: 配置测试异常 - {e}")
                config_results[storage_type] = {
                    "add_success": False,
                    "error": str(e)
                }
        
        self.test_results['storage_configurations'] = config_results
        
    def validate_storage_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """验证存储配置的有效性"""
        validation = {
            "required_fields": True,
            "field_types": True,
            "logical_consistency": True,
            "issues": []
        }
        
        # 检查必需字段
        required_fields = ["name", "storage_type"]
        for field in required_fields:
            if field not in config:
                validation["required_fields"] = False
                validation["issues"].append(f"缺少必需字段: {field}")
        
        # 检查字段类型
        if "port" in config and not isinstance(config["port"], int):
            validation["field_types"] = False
            validation["issues"].append("端口必须是整数")
        
        # 检查逻辑一致性
        if config.get("storage_type") == "s3":
            s3_required = ["endpoint", "access_key", "secret_key", "bucket"]
            for field in s3_required:
                if field not in config:
                    validation["logical_consistency"] = False
                    validation["issues"].append(f"S3存储缺少必需字段: {field}")
        
        return validation
    
    def test_parameter_validation(self):
        """测试参数验证"""
        print("\n🔍 2. 参数验证测试")
        print("-" * 50)
        
        # 测试无效参数
        invalid_configs = [
            {
                "name": "无效端口测试",
                "storage_type": "sftp",
                "host": "example.com",
                "port": "invalid_port",  # 应该是整数
                "username": "test"
            },
            {
                "name": "缺少必需字段",
                "storage_type": "s3"
                # 缺少endpoint, access_key等
            },
            {
                "storage_type": "sftp",
                # 缺少name字段
                "host": "example.com"
            }
        ]
        
        validation_results = []
        for i, config in enumerate(invalid_configs):
            print(f"\n🧪 测试无效配置 {i+1}...")
            
            try:
                response = requests.post(
                    f"{self.base_url}/api/sources",
                    json=config,
                    timeout=5
                )
                
                # 期望返回错误
                if response.status_code >= 400:
                    print(f"✅ 正确拒绝无效配置: HTTP {response.status_code}")
                    validation_results.append({"passed": True, "status": response.status_code})
                else:
                    print(f"❌ 错误接受了无效配置: HTTP {response.status_code}")
                    validation_results.append({"passed": False, "status": response.status_code})
                    
            except Exception as e:
                print(f"❌ 参数验证测试异常: {e}")
                validation_results.append({"passed": False, "error": str(e)})
        
        self.test_results['parameter_validation'] = validation_results
    
    def test_configuration_logic(self):
        """测试配置逻辑科学性"""
        print("\n🧠 3. 配置逻辑科学性测试")
        print("-" * 50)
        
        logic_tests = []
        
        # 测试端口范围
        print("🔧 测试端口范围逻辑...")
        port_tests = [
            {"port": 0, "should_fail": True},      # 无效端口
            {"port": 65536, "should_fail": True},  # 超出范围
            {"port": 22, "should_fail": False},    # 有效端口
            {"port": 443, "should_fail": False}    # 有效端口
        ]
        
        for test in port_tests:
            config = {
                "name": f"端口测试_{test['port']}",
                "storage_type": "sftp",
                "host": "example.com",
                "port": test["port"],
                "username": "test"
            }
            
            try:
                response = requests.post(f"{self.base_url}/api/sources", json=config, timeout=5)
                
                if test["should_fail"]:
                    passed = response.status_code >= 400
                    status = "✅ 正确拒绝" if passed else "❌ 错误接受"
                else:
                    passed = response.status_code == 200
                    status = "✅ 正确接受" if passed else "❌ 错误拒绝"
                
                print(f"  {status} 端口 {test['port']}")
                logic_tests.append({"test": f"port_{test['port']}", "passed": passed})
                
            except Exception as e:
                print(f"  ❌ 端口 {test['port']} 测试异常: {e}")
                logic_tests.append({"test": f"port_{test['port']}", "passed": False, "error": str(e)})
        
        # 测试URL格式验证
        print("\n🔧 测试URL格式逻辑...")
        url_tests = [
            {"endpoint": "invalid-url", "should_fail": True},
            {"endpoint": "http://example.com", "should_fail": False},
            {"endpoint": "https://s3.amazonaws.com", "should_fail": False}
        ]
        
        for test in url_tests:
            config = {
                "name": f"URL测试",
                "storage_type": "s3",
                "endpoint": test["endpoint"],
                "access_key": "test",
                "secret_key": "test",
                "bucket": "test"
            }
            
            try:
                response = requests.post(f"{self.base_url}/api/sources", json=config, timeout=5)
                
                if test["should_fail"]:
                    passed = response.status_code >= 400
                    status = "✅ 正确拒绝" if passed else "❌ 错误接受"
                else:
                    passed = response.status_code == 200
                    status = "✅ 正确接受" if passed else "❌ 错误拒绝"
                
                print(f"  {status} URL: {test['endpoint']}")
                logic_tests.append({"test": f"url_{test['endpoint']}", "passed": passed})
                
            except Exception as e:
                print(f"  ❌ URL {test['endpoint']} 测试异常: {e}")
                logic_tests.append({"test": f"url_{test['endpoint']}", "passed": False, "error": str(e)})
        
        self.test_results['configuration_logic'] = logic_tests
    
    def test_api_completeness(self):
        """测试API完整性"""
        print("\n🔌 4. API完整性测试")
        print("-" * 50)
        
        api_endpoints = {
            "GET": [
                "/api/sources",
                "/api/targets", 
                "/api/tasks",
                "/api/task-executions",
                "/api/file-tree?storage_id=test&path="
            ],
            "POST": [
                "/api/sources",
                "/api/targets",
                "/api/tasks",
                "/api/compressed-transfer"
            ],
            "PUT": [
                "/api/sources/test",
                "/api/targets/test",
                "/api/tasks/test"
            ],
            "DELETE": [
                "/api/sources/test",
                "/api/targets/test", 
                "/api/tasks/test"
            ]
        }
        
        api_results = {}
        for method, endpoints in api_endpoints.items():
            print(f"\n🔧 测试 {method} 方法...")
            method_results = {}
            
            for endpoint in endpoints:
                try:
                    if method == "GET":
                        response = requests.get(f"{self.base_url}{endpoint}", timeout=5)
                    elif method == "POST":
                        response = requests.post(f"{self.base_url}{endpoint}", 
                                               json={"test": "data"}, timeout=5)
                    elif method == "PUT":
                        response = requests.put(f"{self.base_url}{endpoint}", 
                                              json={"test": "data"}, timeout=5)
                    elif method == "DELETE":
                        response = requests.delete(f"{self.base_url}{endpoint}", timeout=5)
                    
                    # API应该响应（即使是错误响应也比404好）
                    if response.status_code != 404:
                        print(f"  ✅ {endpoint}: HTTP {response.status_code}")
                        method_results[endpoint] = {"status": response.status_code, "available": True}
                    else:
                        print(f"  ❌ {endpoint}: 未实现 (404)")
                        method_results[endpoint] = {"status": 404, "available": False}
                        
                except Exception as e:
                    print(f"  ❌ {endpoint}: 异常 - {e}")
                    method_results[endpoint] = {"available": False, "error": str(e)}
            
            api_results[method] = method_results
        
        self.test_results['api_completeness'] = api_results

    def test_error_handling(self):
        """测试错误处理"""
        print("\n🛡️ 5. 错误处理测试")
        print("-" * 50)

        error_scenarios = [
            {
                "name": "无效JSON",
                "method": "POST",
                "endpoint": "/api/sources",
                "data": "invalid json",
                "headers": {"Content-Type": "application/json"}
            },
            {
                "name": "超大请求",
                "method": "POST",
                "endpoint": "/api/sources",
                "data": {"name": "x" * 10000}  # 超大数据
            },
            {
                "name": "不存在的资源",
                "method": "GET",
                "endpoint": "/api/sources/nonexistent"
            },
            {
                "name": "无效参数",
                "method": "GET",
                "endpoint": "/api/file-tree?storage_id=&path="
            }
        ]

        error_results = []
        for scenario in error_scenarios:
            print(f"\n🧪 测试错误场景: {scenario['name']}")

            try:
                if scenario["method"] == "GET":
                    response = requests.get(f"{self.base_url}{scenario['endpoint']}", timeout=5)
                elif scenario["method"] == "POST":
                    if isinstance(scenario["data"], str):
                        response = requests.post(
                            f"{self.base_url}{scenario['endpoint']}",
                            data=scenario["data"],
                            headers=scenario.get("headers", {}),
                            timeout=5
                        )
                    else:
                        response = requests.post(
                            f"{self.base_url}{scenario['endpoint']}",
                            json=scenario["data"],
                            timeout=5
                        )

                # 检查是否有合理的错误响应
                if 400 <= response.status_code < 600:
                    print(f"  ✅ 正确处理错误: HTTP {response.status_code}")
                    error_results.append({
                        "scenario": scenario["name"],
                        "handled_correctly": True,
                        "status_code": response.status_code
                    })
                else:
                    print(f"  ❌ 错误处理不当: HTTP {response.status_code}")
                    error_results.append({
                        "scenario": scenario["name"],
                        "handled_correctly": False,
                        "status_code": response.status_code
                    })

            except Exception as e:
                print(f"  ❌ 错误处理测试异常: {e}")
                error_results.append({
                    "scenario": scenario["name"],
                    "handled_correctly": False,
                    "error": str(e)
                })

        self.test_results['error_handling'] = error_results

    def test_boundary_conditions(self):
        """测试边界条件"""
        print("\n🎯 6. 边界条件测试")
        print("-" * 50)

        boundary_tests = []

        # 测试字符串长度边界
        print("🔧 测试字符串长度边界...")
        string_tests = [
            {"name": "", "should_fail": True},           # 空字符串
            {"name": "a", "should_fail": False},         # 最短有效
            {"name": "a" * 255, "should_fail": False},   # 正常长度
            {"name": "a" * 1000, "should_fail": True}    # 过长
        ]

        for test in string_tests:
            config = {
                "name": test["name"],
                "storage_type": "s3",
                "endpoint": "https://s3.amazonaws.com",
                "access_key": "test",
                "secret_key": "test",
                "bucket": "test"
            }

            try:
                response = requests.post(f"{self.base_url}/api/sources", json=config, timeout=5)

                if test["should_fail"]:
                    passed = response.status_code >= 400
                    status = "✅ 正确拒绝" if passed else "❌ 错误接受"
                else:
                    passed = response.status_code == 200
                    status = "✅ 正确接受" if passed else "❌ 错误拒绝"

                name_display = test["name"][:20] + "..." if len(test["name"]) > 20 else test["name"]
                print(f"  {status} 名称长度 {len(test['name'])}: '{name_display}'")
                boundary_tests.append({
                    "test": f"name_length_{len(test['name'])}",
                    "passed": passed
                })

            except Exception as e:
                print(f"  ❌ 名称长度测试异常: {e}")
                boundary_tests.append({
                    "test": f"name_length_{len(test['name'])}",
                    "passed": False,
                    "error": str(e)
                })

        # 测试数值边界
        print("\n🔧 测试数值边界...")
        numeric_tests = [
            {"port": -1, "should_fail": True},
            {"port": 0, "should_fail": True},
            {"port": 1, "should_fail": False},
            {"port": 65535, "should_fail": False},
            {"port": 65536, "should_fail": True}
        ]

        for test in numeric_tests:
            config = {
                "name": f"端口边界测试_{test['port']}",
                "storage_type": "sftp",
                "host": "example.com",
                "port": test["port"],
                "username": "test"
            }

            try:
                response = requests.post(f"{self.base_url}/api/sources", json=config, timeout=5)

                if test["should_fail"]:
                    passed = response.status_code >= 400
                    status = "✅ 正确拒绝" if passed else "❌ 错误接受"
                else:
                    passed = response.status_code == 200
                    status = "✅ 正确接受" if passed else "❌ 错误拒绝"

                print(f"  {status} 端口值 {test['port']}")
                boundary_tests.append({
                    "test": f"port_boundary_{test['port']}",
                    "passed": passed
                })

            except Exception as e:
                print(f"  ❌ 端口边界测试异常: {e}")
                boundary_tests.append({
                    "test": f"port_boundary_{test['port']}",
                    "passed": False,
                    "error": str(e)
                })

        self.test_results['boundary_conditions'] = boundary_tests

    def test_web_interface_features(self):
        """测试Web界面功能"""
        print("\n🌐 7. Web界面功能测试")
        print("-" * 50)

        web_tests = []

        # 测试主要页面
        pages = [
            "/",
            "/index.html",
            "/api/sources",
            "/api/targets",
            "/api/tasks"
        ]

        for page in pages:
            try:
                response = requests.get(f"{self.base_url}{page}", timeout=10)

                if response.status_code == 200:
                    print(f"✅ 页面可访问: {page}")
                    web_tests.append({"page": page, "accessible": True})
                else:
                    print(f"❌ 页面不可访问: {page} - HTTP {response.status_code}")
                    web_tests.append({"page": page, "accessible": False, "status": response.status_code})

            except Exception as e:
                print(f"❌ 页面测试异常: {page} - {e}")
                web_tests.append({"page": page, "accessible": False, "error": str(e)})

        # 测试静态资源
        print("\n🔧 测试静态资源...")
        # 这里可以添加CSS、JS等静态资源的测试

        self.test_results['web_interface'] = web_tests

    def test_actual_transfer(self):
        """测试实际传输功能"""
        print("\n📁 8. 实际传输测试")
        print("-" * 50)

        # 创建测试文件
        test_dir = Path(tempfile.mkdtemp(prefix="lightrek_test_"))
        test_file = test_dir / "test_file.txt"
        test_content = "LightRek测试文件内容\n" * 100

        try:
            test_file.write_text(test_content, encoding='utf-8')
            print(f"✅ 创建测试文件: {test_file}")

            # 配置本地文件系统存储
            local_source = {
                "name": "本地测试源",
                "storage_type": "local",
                "root_path": str(test_dir)
            }

            local_target = {
                "name": "本地测试目标",
                "storage_type": "local",
                "root_path": str(test_dir / "target")
            }

            # 添加存储配置
            source_response = requests.post(f"{self.base_url}/api/sources", json=local_source, timeout=10)
            target_response = requests.post(f"{self.base_url}/api/targets", json=local_target, timeout=10)

            if source_response.status_code == 200 and target_response.status_code == 200:
                print("✅ 本地存储配置添加成功")

                # 这里可以添加实际的传输任务测试
                # 由于需要获取存储ID，暂时标记为配置成功
                self.test_results['actual_transfer'] = {
                    "setup_success": True,
                    "test_file_created": True,
                    "storage_configured": True
                }
            else:
                print("❌ 本地存储配置失败")
                self.test_results['actual_transfer'] = {
                    "setup_success": False,
                    "error": "存储配置失败"
                }

        except Exception as e:
            print(f"❌ 传输测试异常: {e}")
            self.test_results['actual_transfer'] = {
                "setup_success": False,
                "error": str(e)
            }
        finally:
            # 清理测试文件
            try:
                import shutil
                shutil.rmtree(test_dir)
                print("✅ 清理测试文件")
            except:
                pass

    def generate_deep_report(self):
        """生成深度测试报告"""
        print("\n📊 9. 深度测试报告")
        print("-" * 50)

        # 计算总体统计
        total_categories = len(self.test_results)
        passed_categories = 0
        total_tests = 0
        passed_tests = 0

        for category, results in self.test_results.items():
            if isinstance(results, dict):
                if results.get("setup_success") or any(
                    isinstance(v, dict) and v.get("passed", False)
                    for v in results.values() if isinstance(v, dict)
                ):
                    passed_categories += 1
            elif isinstance(results, list):
                category_passed = sum(1 for r in results if isinstance(r, dict) and r.get("passed", False))
                total_tests += len(results)
                passed_tests += category_passed
                if category_passed > 0:
                    passed_categories += 1

        print(f"测试类别: {passed_categories}/{total_categories} 通过")
        print(f"具体测试: {passed_tests}/{total_tests} 通过")
        print(f"总体成功率: {(passed_tests/total_tests*100):.1f}%" if total_tests > 0 else "N/A")

        # 详细结果
        print("\n详细测试结果:")
        for category, results in self.test_results.items():
            print(f"\n📋 {category}:")

            if isinstance(results, dict):
                for key, value in results.items():
                    if isinstance(value, dict):
                        status = "✅" if value.get("passed", False) or value.get("add_success", False) else "❌"
                        print(f"  {status} {key}")
                    else:
                        print(f"  ℹ️ {key}: {value}")
            elif isinstance(results, list):
                for result in results:
                    if isinstance(result, dict):
                        status = "✅" if result.get("passed", False) else "❌"
                        test_name = result.get("test", result.get("scenario", "未知测试"))
                        print(f"  {status} {test_name}")

        # 保存详细报告
        report = {
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'summary': {
                'total_categories': total_categories,
                'passed_categories': passed_categories,
                'total_tests': total_tests,
                'passed_tests': passed_tests,
                'success_rate': f"{(passed_tests/total_tests*100):.1f}%" if total_tests > 0 else "0%"
            },
            'detailed_results': self.test_results
        }

        report_file = Path(__file__).parent / 'deep_functionality_report.json'
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)

        print(f"\n📄 详细报告已保存: {report_file}")

        # 生成建议
        self.generate_recommendations()

    def generate_recommendations(self):
        """生成改进建议"""
        print("\n💡 改进建议:")

        recommendations = []

        # 基于测试结果生成建议
        if 'parameter_validation' in self.test_results:
            failed_validations = [r for r in self.test_results['parameter_validation'] if not r.get('passed', False)]
            if failed_validations:
                recommendations.append("加强参数验证逻辑")

        if 'error_handling' in self.test_results:
            failed_errors = [r for r in self.test_results['error_handling'] if not r.get('handled_correctly', False)]
            if failed_errors:
                recommendations.append("改进错误处理机制")

        if 'boundary_conditions' in self.test_results:
            failed_boundaries = [r for r in self.test_results['boundary_conditions'] if not r.get('passed', False)]
            if failed_boundaries:
                recommendations.append("完善边界条件检查")

        if recommendations:
            for i, rec in enumerate(recommendations, 1):
                print(f"  {i}. {rec}")
        else:
            print("  🎉 所有测试表现良好，无需特别改进！")


if __name__ == "__main__":
    tester = DeepFunctionalityTester()
    tester.run_deep_test()
