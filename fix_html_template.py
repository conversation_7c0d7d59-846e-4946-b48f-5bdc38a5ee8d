#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复HTML模板中的格式化变量冲突
"""

import re

def fix_html_template():
    """修复HTML模板中的格式化变量冲突"""
    
    # 读取文件
    with open('complete_web_interface.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    print("开始修复HTML模板...")
    
    # 查找HTML模板部分
    template_start = content.find('html_template = """')
    template_end = content.find('"""', template_start + 20)
    
    if template_start == -1 or template_end == -1:
        print("❌ 未找到HTML模板")
        return False
    
    html_template = content[template_start + 19:template_end]
    print(f"原始模板长度: {len(html_template)} 字符")
    
    # 需要保留的Python格式化变量
    python_vars = {
        'logo_base64', 'sources_count', 'targets_count', 'tasks_count', 
        'active_tasks', 'total_executions', 'today_executions', 'total_files',
        'total_size_formatted', 'success_rate', 'avg_speed_formatted', 
        'running_tasks', 'recent_executions_html', 'js_stats'
    }
    
    # 修复模板
    fixed_template = html_template
    
    # 查找所有格式化变量
    format_vars = re.findall(r'(?<!\{)\{([^}]+)\}(?!\})', fixed_template)
    
    print(f"找到 {len(format_vars)} 个格式化变量")
    
    replacements = 0
    for var in format_vars:
        var_name = var.split(':')[0].split('.')[0]
        
        # 如果不是Python变量，需要转义
        if var_name not in python_vars:
            old_pattern = '{' + var + '}'
            new_pattern = '{{' + var + '}}'
            
            # 只替换第一个匹配项，避免重复替换
            if old_pattern in fixed_template:
                fixed_template = fixed_template.replace(old_pattern, new_pattern, 1)
                replacements += 1
                print(f"  转义: {old_pattern} -> {new_pattern}")
    
    print(f"完成 {replacements} 个变量的转义")
    
    # 替换原始模板
    new_content = content[:template_start + 19] + fixed_template + content[template_end:]
    
    # 写回文件
    with open('complete_web_interface.py', 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    print("✅ HTML模板修复完成")
    return True

def test_fixed_template():
    """测试修复后的模板"""
    print("\\n=== 测试修复后的模板 ===")
    
    # 重新读取文件
    with open('complete_web_interface.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找HTML模板部分
    template_start = content.find('html_template = """')
    template_end = content.find('"""', template_start + 20)
    
    html_template = content[template_start + 19:template_end]
    
    # 查找格式化变量
    format_vars = re.findall(r'(?<!\{)\{([^}]+)\}(?!\})', html_template)
    
    print(f"修复后找到 {len(format_vars)} 个格式化变量:")
    for var in sorted(set(format_vars)):
        print(f"  {var}")
    
    # 测试格式化
    try:
        test_stats = {
            'sources_count': 5,
            'targets_count': 3,
            'tasks_count': 2,
            'active_tasks': 1,
            'total_executions': 10,
            'today_executions': 2,
            'total_files': 100,
            'total_size_formatted': '1.5 GB',
            'success_rate': 95.5,
            'avg_speed_formatted': '10.5 MB/s',
            'running_tasks': 1
        }
        
        test_format = html_template.format(
            logo_base64='test_logo',
            sources_count=test_stats['sources_count'],
            targets_count=test_stats['targets_count'],
            tasks_count=test_stats['tasks_count'],
            active_tasks=test_stats['active_tasks'],
            total_executions=test_stats['total_executions'],
            today_executions=test_stats['today_executions'],
            total_files=test_stats['total_files'],
            total_size_formatted=test_stats['total_size_formatted'],
            success_rate=test_stats['success_rate'],
            avg_speed_formatted=test_stats['avg_speed_formatted'],
            running_tasks=test_stats['running_tasks'],
            recent_executions_html='<div>测试执行记录</div>',
            js_stats='{"test": true}'
        )
        
        print("✅ 修复后的HTML模板格式化成功")
        print(f"格式化后长度: {len(test_format)} 字符")
        return True
        
    except Exception as e:
        print(f"❌ 修复后的模板仍有问题: {e}")
        return False

if __name__ == '__main__':
    if fix_html_template():
        test_fixed_template()
