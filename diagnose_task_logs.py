#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断任务日志问题
"""

import sqlite3
import os
import logging
from datetime import datetime

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def check_database_file():
    """检查数据库文件"""
    logger = logging.getLogger(__name__)
    
    db_path = "lightrek_data.db"
    
    if os.path.exists(db_path):
        file_size = os.path.getsize(db_path)
        logger.info(f"✅ 数据库文件存在: {db_path} ({file_size} bytes)")
        return True
    else:
        logger.error(f"❌ 数据库文件不存在: {db_path}")
        return False

def check_database_tables():
    """检查数据库表结构"""
    logger = logging.getLogger(__name__)
    
    try:
        with sqlite3.connect("lightrek_data.db") as conn:
            cursor = conn.cursor()
            
            # 检查表是否存在
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            
            logger.info(f"📋 数据库中的表: {tables}")
            
            required_tables = ['task_executions', 'task_logs', 'file_sync_records']
            missing_tables = [table for table in required_tables if table not in tables]
            
            if missing_tables:
                logger.error(f"❌ 缺少必需的表: {missing_tables}")
                return False
            else:
                logger.info("✅ 所有必需的表都存在")
                
                # 检查表结构
                for table in required_tables:
                    cursor.execute(f"PRAGMA table_info({table})")
                    columns = cursor.fetchall()
                    logger.info(f"📊 表 {table} 的列: {[col[1] for col in columns]}")
                
                return True
                
    except Exception as e:
        logger.error(f"❌ 检查数据库表失败: {e}")
        return False

def check_task_executions():
    """检查任务执行记录"""
    logger = logging.getLogger(__name__)
    
    try:
        with sqlite3.connect("lightrek_data.db") as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # 获取执行记录总数
            cursor.execute("SELECT COUNT(*) FROM task_executions")
            total_executions = cursor.fetchone()[0]
            logger.info(f"📊 任务执行记录总数: {total_executions}")
            
            if total_executions == 0:
                logger.warning("⚠️ 没有任务执行记录")
                return False
            
            # 获取最近的执行记录
            cursor.execute("""
                SELECT id, task_id, task_name, status, start_time, end_time
                FROM task_executions
                ORDER BY start_time DESC
                LIMIT 5
            """)
            
            recent_executions = cursor.fetchall()
            logger.info("📋 最近的任务执行记录:")
            
            for execution in recent_executions:
                logger.info(f"  🔹 {execution['task_name']} ({execution['id'][:8]}...)")
                logger.info(f"     状态: {execution['status']}, 开始: {execution['start_time']}")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ 检查任务执行记录失败: {e}")
        return False

def check_task_logs():
    """检查任务日志"""
    logger = logging.getLogger(__name__)
    
    try:
        with sqlite3.connect("lightrek_data.db") as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # 获取日志总数
            cursor.execute("SELECT COUNT(*) FROM task_logs")
            total_logs = cursor.fetchone()[0]
            logger.info(f"📊 任务日志总数: {total_logs}")
            
            if total_logs == 0:
                logger.warning("⚠️ 没有任务日志记录")
                return False
            
            # 获取最近的日志
            cursor.execute("""
                SELECT execution_id, level, message, timestamp 
                FROM task_logs 
                ORDER BY timestamp DESC 
                LIMIT 10
            """)
            
            recent_logs = cursor.fetchall()
            logger.info("📋 最近的任务日志:")
            
            for log in recent_logs:
                logger.info(f"  🔹 [{log['level']}] {log['message'][:50]}...")
                logger.info(f"     执行ID: {log['execution_id'][:8]}..., 时间: {log['timestamp']}")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ 检查任务日志失败: {e}")
        return False

def check_database_manager():
    """检查数据库管理器"""
    logger = logging.getLogger(__name__)
    
    try:
        from database_manager import db_manager
        
        logger.info("✅ 数据库管理器导入成功")
        
        # 测试获取执行记录
        executions = db_manager.get_recent_executions(5)
        logger.info(f"📊 通过数据库管理器获取的执行记录数: {len(executions)}")
        
        if executions:
            logger.info("📋 执行记录示例:")
            for execution in executions[:3]:
                logger.info(f"  🔹 {execution.get('task_name', '未知任务')} - {execution.get('status', '未知状态')}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 检查数据库管理器失败: {e}")
        return False

def test_api_endpoint():
    """测试API端点"""
    logger = logging.getLogger(__name__)
    
    try:
        import requests
        
        # 测试任务执行记录API
        response = requests.get('http://localhost:8000/api/task-executions')
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                executions = data.get('executions', [])
                logger.info(f"✅ API返回执行记录数: {len(executions)}")
                
                if executions:
                    logger.info("📋 API返回的执行记录示例:")
                    for execution in executions[:3]:
                        logger.info(f"  🔹 {execution.get('task_name', '未知任务')} - {execution.get('status', '未知状态')}")
                else:
                    logger.warning("⚠️ API返回的执行记录为空")
                
                return len(executions) > 0
            else:
                logger.error(f"❌ API返回错误: {data.get('message')}")
                return False
        else:
            logger.error(f"❌ API请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        logger.error(f"❌ 测试API端点失败: {e}")
        return False

def create_test_execution():
    """创建测试执行记录"""
    logger = logging.getLogger(__name__)
    
    try:
        from database_manager import db_manager
        import uuid
        
        # 创建测试执行记录
        execution_id = str(uuid.uuid4())
        task_id = "test_task_" + str(uuid.uuid4())[:8]
        
        success = db_manager.start_task_execution(
            execution_id=execution_id,
            task_id=task_id,
            task_name="诊断测试任务",
            task_config={"test": True}
        )
        
        if success:
            logger.info(f"✅ 创建测试执行记录成功: {execution_id[:8]}...")
            
            # 添加测试日志
            db_manager.add_task_log(execution_id, "INFO", "这是一条测试日志")
            db_manager.add_task_log(execution_id, "DEBUG", "测试调试信息")
            
            # 完成执行
            db_manager.complete_task_execution(
                execution_id=execution_id,
                status="completed",
                files_processed=10,
                files_failed=0,
                bytes_transferred=1024
            )
            
            logger.info("✅ 测试执行记录创建完成")
            return True
        else:
            logger.error("❌ 创建测试执行记录失败")
            return False
            
    except Exception as e:
        logger.error(f"❌ 创建测试执行记录异常: {e}")
        return False

def main():
    """主函数"""
    logger = setup_logging()
    
    logger.info("🔍 LightRek 任务日志诊断工具")
    logger.info("=" * 60)
    
    checks = [
        ("数据库文件检查", check_database_file),
        ("数据库表检查", check_database_tables),
        ("任务执行记录检查", check_task_executions),
        ("任务日志检查", check_task_logs),
        ("数据库管理器检查", check_database_manager),
    ]
    
    passed_checks = 0
    
    for check_name, check_func in checks:
        logger.info(f"\n📋 执行检查: {check_name}")
        
        try:
            if check_func():
                logger.info(f"✅ {check_name} 通过")
                passed_checks += 1
            else:
                logger.error(f"❌ {check_name} 失败")
        except Exception as e:
            logger.error(f"❌ {check_name} 异常: {e}")
    
    logger.info(f"\n📊 检查结果: {passed_checks}/{len(checks)} 通过")
    
    # 如果没有执行记录，创建测试记录
    if passed_checks >= 3:  # 基础检查通过
        logger.info("\n🧪 测试API端点...")
        if not test_api_endpoint():
            logger.info("\n🔧 创建测试执行记录...")
            if create_test_execution():
                logger.info("\n🔄 重新测试API端点...")
                test_api_endpoint()
    
    logger.info("\n" + "=" * 60)
    logger.info("📋 诊断总结:")
    
    if passed_checks == len(checks):
        logger.info("✅ 所有检查通过，数据库和日志系统正常")
        logger.info("💡 如果Web界面仍显示空日志，请检查:")
        logger.info("  - Web服务器是否正在运行")
        logger.info("  - 浏览器缓存是否需要清理")
        logger.info("  - JavaScript控制台是否有错误")
    else:
        logger.error("❌ 部分检查失败，需要修复数据库或日志系统")
        logger.info("💡 建议:")
        logger.info("  - 重新初始化数据库")
        logger.info("  - 检查文件权限")
        logger.info("  - 运行一个测试任务生成日志")

if __name__ == "__main__":
    main()
