{"summary": {"total": 22, "passed": 20, "failed": 2, "warnings": 0, "success_rate": 90.9090909090909}, "results": [{"test": "编辑表单修复", "status": "PASS", "details": "已添加动态字段显示逻辑和存储类型选择", "timestamp": "2025-07-09 16:51:36"}, {"test": "任务功能 - 压缩传输选项", "status": "PASS", "details": "已在任务配置表单中实现", "timestamp": "2025-07-09 16:51:36"}, {"test": "任务功能 - 文件选择功能", "status": "PASS", "details": "已在任务配置表单中实现", "timestamp": "2025-07-09 16:51:36"}, {"test": "任务功能 - 带宽限制", "status": "PASS", "details": "已在任务配置表单中实现", "timestamp": "2025-07-09 16:51:36"}, {"test": "任务功能 - 文件切片", "status": "PASS", "details": "已在任务配置表单中实现", "timestamp": "2025-07-09 16:51:36"}, {"test": "任务功能 - 完整性验证", "status": "PASS", "details": "已在任务配置表单中实现", "timestamp": "2025-07-09 16:51:36"}, {"test": "任务功能 - 文件过滤说明", "status": "PASS", "details": "已在任务配置表单中实现", "timestamp": "2025-07-09 16:51:36"}, {"test": "任务功能 - 排除文件说明", "status": "PASS", "details": "已在任务配置表单中实现", "timestamp": "2025-07-09 16:51:36"}, {"test": "压缩传输兼容性检查", "status": "FAIL", "details": "测试失败: HTTPConnectionPool(host='localhost', port=8001): Max retries exceeded with url: /api/sources (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002325C933CB0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))", "timestamp": "2025-07-09 16:51:40"}, {"test": "文件树功能", "status": "FAIL", "details": "测试异常: HTTPConnectionPool(host='localhost', port=8001): Max retries exceeded with url: /api/sources (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002325C92A350>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))", "timestamp": "2025-07-09 16:51:44"}, {"test": "功能说明 - 文件过滤 (包含)", "status": "PASS", "details": "只同步匹配的文件，支持通配符", "timestamp": "2025-07-09 16:51:44"}, {"test": "功能说明 - 排除文件 (排除)", "status": "PASS", "details": "排除匹配的文件，支持通配符", "timestamp": "2025-07-09 16:51:44"}, {"test": "控制功能 - 带宽限制", "status": "PASS", "details": "0表示无限制，设置后将限制传输速度", "timestamp": "2025-07-09 16:51:44"}, {"test": "控制功能 - 文件切片开关", "status": "PASS", "details": "可启用/禁用大文件分片传输", "timestamp": "2025-07-09 16:51:44"}, {"test": "控制功能 - 切片阈值", "status": "PASS", "details": "超过此大小的文件将被分片传输", "timestamp": "2025-07-09 16:51:44"}, {"test": "控制功能 - 切片大小", "status": "PASS", "details": "每个分片的大小", "timestamp": "2025-07-09 16:51:44"}, {"test": "完整性验证", "status": "PASS", "details": "使用MD5/SHA256哈希校验确保文件传输完整性", "timestamp": "2025-07-09 16:51:44"}, {"test": "界面改进 - 存储类型标签显示", "status": "PASS", "details": "已实现", "timestamp": "2025-07-09 16:51:44"}, {"test": "界面改进 - 动态参数显示", "status": "PASS", "details": "已实现", "timestamp": "2025-07-09 16:51:44"}, {"test": "界面改进 - 任务状态标签", "status": "PASS", "details": "已实现", "timestamp": "2025-07-09 16:51:44"}, {"test": "界面改进 - 调度类型标签", "status": "PASS", "details": "已实现", "timestamp": "2025-07-09 16:51:44"}, {"test": "界面改进 - 功能说明文档", "status": "PASS", "details": "已实现", "timestamp": "2025-07-09 16:51:44"}]}