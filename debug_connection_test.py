#!/usr/bin/env python3
"""
调试连接测试问题
"""

import sys
sys.path.insert(0, '.')

from unified_config_manager import UnifiedConfigManager
from storage_abstraction import StorageFactory, StorageType

def debug_connection_test():
    """调试连接测试"""
    print("🔍 调试连接测试问题")
    print("=" * 50)
    
    # 创建配置管理器
    config_manager = UnifiedConfigManager()
    
    # SFTP配置数据
    sftp_data = {
        "name": "测试SFTP",
        "description": "调试用SFTP",
        "hostname": "**************",
        "port": 22,
        "username": "root",
        "password": "rootreadbook2",
        "root_path": "/"
    }
    
    print("1. 测试配置创建...")
    try:
        # 直接创建SFTP配置
        storage_config = config_manager._create_storage_config('sftp', sftp_data)
        print(f"✅ 配置创建成功: {type(storage_config).__name__}")
        print(f"   存储类型: {storage_config.storage_type}")
        print(f"   主机名: {storage_config.hostname}")
        print(f"   端口: {storage_config.port}")
        print(f"   用户名: {storage_config.username}")
        print(f"   根路径: {storage_config.root_path}")
    except Exception as e:
        print(f"❌ 配置创建失败: {e}")
        return
    
    print("\n2. 测试适配器创建...")
    try:
        # 创建存储适配器
        adapter = StorageFactory.create_adapter(storage_config)
        print(f"✅ 适配器创建成功: {type(adapter).__name__}")
    except Exception as e:
        print(f"❌ 适配器创建失败: {e}")
        return
    
    print("\n3. 测试直接连接...")
    try:
        # 直接测试连接
        success, message = adapter.test_connection()
        if success:
            print(f"✅ 直接连接测试成功: {message}")
        else:
            print(f"❌ 直接连接测试失败: {message}")
    except Exception as e:
        print(f"❌ 直接连接测试异常: {e}")
    
    print("\n4. 测试通过配置管理器...")
    try:
        # 添加到配置管理器
        temp_id = 'debug_test'
        if config_manager.add_source(temp_id, 'sftp', sftp_data):
            print("✅ 配置添加成功")
            
            # 通过配置管理器测试连接
            success, message = config_manager.test_storage_connection(temp_id, is_source=True)
            if success:
                print(f"✅ 配置管理器连接测试成功: {message}")
            else:
                print(f"❌ 配置管理器连接测试失败: {message}")
            
            # 清理
            config_manager.remove_source(temp_id)
        else:
            print("❌ 配置添加失败")
    except Exception as e:
        print(f"❌ 配置管理器测试异常: {e}")
    
    print("\n5. 检查支持的存储类型...")
    try:
        supported_types = StorageFactory.get_supported_types()
        print(f"支持的存储类型: {[t.value for t in supported_types]}")
        print(f"SFTP是否支持: {StorageType.SFTP in supported_types}")
    except Exception as e:
        print(f"❌ 检查存储类型失败: {e}")

if __name__ == "__main__":
    debug_connection_test()
