#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据库修复
"""

import logging
import uuid
from datetime import datetime

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def test_database_manager_methods():
    """测试数据库管理器方法"""
    logger = setup_logging()
    
    try:
        from database_manager import db_manager
        
        logger.info("✅ 数据库管理器导入成功")
        
        # 检查必要的方法
        required_methods = [
            'update_task_execution',
            'get_task_execution',
            'start_task_execution',
            'complete_task_execution'
        ]
        
        for method_name in required_methods:
            if hasattr(db_manager, method_name):
                logger.info(f"✅ 方法 {method_name} 存在")
            else:
                logger.error(f"❌ 方法 {method_name} 不存在")
                return False
        
        # 测试创建和更新任务执行记录
        execution_id = str(uuid.uuid4())
        task_id = "test_task_" + str(uuid.uuid4())[:8]
        
        # 创建任务执行记录
        success = db_manager.start_task_execution(
            execution_id=execution_id,
            task_id=task_id,
            task_name="测试任务",
            source_id="test_source",
            target_id="test_target"
        )
        
        if success:
            logger.info("✅ 创建任务执行记录成功")
            
            # 测试更新任务执行记录
            success = db_manager.update_task_execution(
                execution_id=execution_id,
                files_total=100,
                files_processed=50,
                bytes_transferred=1024000
            )
            
            if success:
                logger.info("✅ 更新任务执行记录成功")
                
                # 测试获取任务执行记录
                execution = db_manager.get_task_execution(execution_id)
                
                if execution:
                    logger.info("✅ 获取任务执行记录成功")
                    logger.info(f"   任务名: {execution['task_name']}")
                    logger.info(f"   文件总数: {execution['files_total']}")
                    logger.info(f"   已处理: {execution['files_processed']}")
                    logger.info(f"   传输字节: {execution['bytes_transferred']}")
                    
                    # 测试完成任务
                    success = db_manager.complete_task_execution(
                        execution_id=execution_id,
                        status='completed',
                        files_processed=100,
                        files_failed=0,
                        bytes_transferred=2048000
                    )
                    
                    if success:
                        logger.info("✅ 完成任务执行记录成功")
                        return True
                    else:
                        logger.error("❌ 完成任务执行记录失败")
                        return False
                else:
                    logger.error("❌ 获取任务执行记录失败")
                    return False
            else:
                logger.error("❌ 更新任务执行记录失败")
                return False
        else:
            logger.error("❌ 创建任务执行记录失败")
            return False
            
    except Exception as e:
        logger.error(f"❌ 测试数据库管理器方法失败: {e}")
        return False

def test_file_browser_api():
    """测试文件浏览器API"""
    logger = logging.getLogger(__name__)
    
    try:
        from file_browser_manager import get_file_browser_manager
        from unified_config_manager import UnifiedConfigManager
        
        config_manager = UnifiedConfigManager()
        browser = get_file_browser_manager(config_manager)
        
        logger.info("✅ 文件浏览器管理器创建成功")
        
        # 检查必要的方法
        required_methods = [
            'browse_storage',
            'download_files',
            'get_file_info'
        ]
        
        for method_name in required_methods:
            if hasattr(browser, method_name):
                logger.info(f"✅ 方法 {method_name} 存在")
            else:
                logger.error(f"❌ 方法 {method_name} 不存在")
                return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试文件浏览器API失败: {e}")
        return False

def test_web_server_api():
    """测试Web服务器API"""
    logger = logging.getLogger(__name__)
    
    try:
        from static_web_server import StaticWebServer
        from unified_config_manager import UnifiedConfigManager
        
        config_manager = UnifiedConfigManager()
        server = StaticWebServer(8001, config_manager)  # 使用不同端口避免冲突
        
        logger.info("✅ Web服务器创建成功")
        
        # 检查必要的方法
        required_methods = [
            '_api_browse_storage',
            '_api_download_files',
            '_api_get_file_info'
        ]
        
        for method_name in required_methods:
            if hasattr(server, method_name):
                logger.info(f"✅ 方法 {method_name} 存在")
            else:
                logger.error(f"❌ 方法 {method_name} 不存在")
                return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试Web服务器API失败: {e}")
        return False

def main():
    """主函数"""
    logger = setup_logging()
    
    logger.info("🧪 数据库和文件浏览器修复测试")
    logger.info("=" * 60)
    
    tests = [
        ("数据库管理器方法测试", test_database_manager_methods),
        ("文件浏览器API测试", test_file_browser_api),
        ("Web服务器API测试", test_web_server_api),
    ]
    
    passed_tests = 0
    
    for test_name, test_func in tests:
        logger.info(f"\n📋 执行测试: {test_name}")
        
        try:
            if test_func():
                logger.info(f"✅ {test_name} 通过")
                passed_tests += 1
            else:
                logger.error(f"❌ {test_name} 失败")
        except Exception as e:
            logger.error(f"❌ {test_name} 异常: {e}")
    
    logger.info(f"\n📊 测试结果: {passed_tests}/{len(tests)} 通过")
    
    if passed_tests == len(tests):
        logger.info("🎉 所有测试通过！修复成功")
        logger.info("")
        logger.info("📋 修复内容:")
        logger.info("  ✅ 添加了 update_task_execution 方法")
        logger.info("  ✅ 添加了 get_task_execution 方法")
        logger.info("  ✅ 修复了文件浏览器API响应问题")
        logger.info("  ✅ 完善了数据库任务执行记录管理")
        logger.info("")
        logger.info("🚀 现在可以:")
        logger.info("  1. 正常同步大文件而不会出现数据库错误")
        logger.info("  2. 使用文件浏览器功能浏览存储")
        logger.info("  3. 下载文件并打包为ZIP")
        logger.info("  4. 查看详细的任务执行统计")
        
        return 0
    else:
        logger.error("❌ 部分测试失败，需要进一步修复")
        return 1

if __name__ == "__main__":
    exit(main())
