#!/usr/bin/env python3
"""
测试列出桶功能的脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from storage_abstraction import S3StorageConfig, StorageType
from s3_storage_adapter import S3StorageAdapter

def test_list_buckets():
    """测试列出桶功能"""
    print("🔍 测试列出桶功能...")
    
    # 阿里云OSS配置 - 请替换为实际配置
    config = S3StorageConfig(
        storage_type=StorageType.S3,
        name="阿里云OSS测试",
        endpoint="https://oss-cn-shanghai.aliyuncs.com",  # 替换为你的区域
        access_key="your-access-key",  # 替换为你的Access Key
        secret_key="your-secret-key",  # 替换为你的Secret Key
        bucket="",  # 空bucket用于列出所有bucket
        region="cn-shanghai"  # 替换为你的区域
    )
    
    adapter = S3StorageAdapter(config)
    
    print(f"📋 配置信息:")
    print(f"   端点: {config.endpoint}")
    print(f"   区域: {config.region}")
    print(f"   Access Key: {config.access_key[:10]}...")
    
    # 测试列出存储桶
    print("\n📁 测试列出存储桶...")
    try:
        print("   调用 adapter.list_buckets()...")
        buckets = adapter.list_buckets()
        print(f"✅ 成功列出 {len(buckets)} 个存储桶:")
        for i, bucket in enumerate(buckets, 1):
            print(f"   {i}. {bucket}")
        
        return True
        
    except Exception as e:
        print(f"❌ 列出存储桶失败: {e}")
        
        # 详细调试信息
        print("\n🔧 详细调试信息:")
        try:
            print("   尝试调用 _list_aliyun_buckets()...")
            buckets = adapter._list_aliyun_buckets()
            print(f"   _list_aliyun_buckets() 返回: {buckets}")
        except Exception as e2:
            print(f"   _list_aliyun_buckets() 失败: {e2}")
            
            # 更详细的调试
            print("\n   尝试手动构建请求...")
            try:
                import http.client
                import ssl
                from urllib.parse import urlparse
                
                parsed_url = urlparse(config.endpoint)
                host = parsed_url.netloc
                print(f"   主机: {host}")
                
                # 构建请求头
                import time
                headers = {
                    'Host': host,
                    'Date': time.strftime('%a, %d %b %Y %H:%M:%S GMT', time.gmtime())
                }
                print(f"   基础头部: {headers}")
                
                # 尝试签名
                signed_headers = adapter._aliyun_oss_list_buckets_signature('GET', '/', {}, headers, b'')
                print(f"   签名后头部: {signed_headers}")
                
                # 尝试连接
                context = ssl.create_default_context()
                conn = http.client.HTTPSConnection(host, context=context, timeout=30)
                
                conn.request('GET', '/', headers=signed_headers)
                response = conn.getresponse()
                
                print(f"   响应状态: {response.status}")
                content = response.read().decode('utf-8', errors='ignore')
                print(f"   响应内容: {content[:500]}...")
                
                conn.close()
                
            except Exception as e3:
                print(f"   手动请求也失败: {e3}")
        
        return False

def test_web_api_format():
    """测试Web API格式的数据"""
    print("\n🌐 测试Web API格式...")
    
    # 模拟Web界面发送的数据
    data = {
        'endpoint': 'https://oss-cn-shanghai.aliyuncs.com',
        'access_key': 'your-access-key',  # 替换为实际值
        'secret_key': 'your-secret-key',  # 替换为实际值
        'region': 'cn-shanghai'
    }
    
    try:
        from storage_abstraction import S3StorageConfig, StorageType
        from s3_storage_adapter import S3StorageAdapter
        
        # 创建S3配置对象
        config = S3StorageConfig(
            storage_type=StorageType.S3,
            name="临时配置",
            endpoint=data.get('endpoint', ''),
            access_key=data.get('access_key', ''),
            secret_key=data.get('secret_key', ''),
            bucket="",  # 不指定bucket来列出所有bucket
            region=data.get('region', '')
        )
        
        adapter = S3StorageAdapter(config)
        buckets = adapter.list_buckets()
        
        print(f"✅ Web API格式测试成功: {len(buckets)} 个存储桶")
        return True
        
    except Exception as e:
        print(f"❌ Web API格式测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 列出桶功能测试")
    print("=" * 50)
    print("⚠️ 注意: 请在运行前修改配置中的Access Key和Secret Key")
    print()
    
    # 测试基本功能
    success1 = test_list_buckets()
    
    # 测试Web API格式
    success2 = test_web_api_format()
    
    print("\n📊 测试结果:")
    print(f"   基本功能: {'✅ 成功' if success1 else '❌ 失败'}")
    print(f"   Web API格式: {'✅ 成功' if success2 else '❌ 失败'}")
    
    if not success1 and not success2:
        print("\n💡 可能的问题:")
        print("   1. Access Key或Secret Key不正确")
        print("   2. 网络连接问题")
        print("   3. 区域设置不正确")
        print("   4. 签名算法问题")
        print("   5. 阿里云OSS权限设置问题")
