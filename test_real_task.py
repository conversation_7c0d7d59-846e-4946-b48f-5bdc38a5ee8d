#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试实际任务执行
"""

import os
import sys
import logging
import json
import tempfile
import subprocess
import base64

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def download_smb_file_with_subprocess(smb_config, file_key):
    """使用子进程隔离下载SMB文件"""
    temp_file = None
    try:
        # 准备配置数据
        config_data = {
            'hostname': smb_config['hostname'],
            'port': smb_config['port'],
            'username': smb_config['username'],
            'password': smb_config['password'],
            'domain': smb_config.get('domain', ''),
            'share_name': smb_config['share_name'],
            'root_path': smb_config.get('root_path', '')
        }
        
        # 创建临时文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False, encoding='utf-8') as f:
            json.dump({
                'config': config_data,
                'file_key': file_key
            }, f)
            temp_file = f.name
        
        # 构建命令
        script_path = os.path.join(os.path.dirname(__file__), 'smb_downloader.py')
        cmd = ['python', script_path, temp_file]
        
        logger.info(f"执行命令: {cmd}")
        
        # 执行子进程
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=30,
            cwd=os.path.dirname(__file__),
            encoding='utf-8',
            errors='ignore'
        )
        
        logger.info(f"子进程返回码: {result.returncode}")
        logger.info(f"子进程标准输出: {result.stdout}")
        logger.info(f"子进程标准错误: {result.stderr}")
        
        if result.returncode == 0:
            output = result.stdout.strip()
            # 查找SUCCESS行
            success_line = None
            for line in output.split('\n'):
                if line.startswith("SUCCESS:"):
                    success_line = line
                    break
            
            logger.info(f"SUCCESS行: {success_line}")
            
            if success_line:
                # 解析成功结果：SUCCESS:size:output_file
                parts = success_line.split(':', 2)
                if len(parts) == 3:
                    size = int(parts[1])
                    output_file = parts[2]
                    
                    logger.info(f"输出文件: {output_file}")
                    
                    # 从输出文件读取数据
                    try:
                        with open(output_file, 'rb') as f:
                            data = f.read()
                        # 清理输出文件
                        os.unlink(output_file)
                        logger.info(f"读取数据成功: {len(data)} bytes")
                        return data
                    except Exception as e:
                        logger.error(f"读取输出文件失败: {e}")
                        return None
                else:
                    logger.error(f"SUCCESS行格式错误: {success_line}")
                    return None
            else:
                # 查找FAILED或ERROR行
                for line in output.split('\n'):
                    if line.startswith("FAILED:"):
                        logger.error(f"子进程下载失败: {file_key}")
                        return None
                    elif line.startswith("ERROR:"):
                        error_msg = line[6:]  # 移除"ERROR:"前缀
                        logger.error(f"子进程错误 {file_key}: {error_msg}")
                        return None
                
                logger.error(f"子进程未知输出: {output}")
                return None
        else:
            logger.error(f"子进程失败 (返回码: {result.returncode}): {result.stderr}")
            return None
            
    except Exception as e:
        logger.error(f"子进程下载异常 {file_key}: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return None
    finally:
        # 清理临时文件
        if temp_file and os.path.exists(temp_file):
            try:
                os.unlink(temp_file)
            except:
                pass

def test_real_task():
    """测试实际任务执行"""
    logger.info("🧪 测试实际任务执行")
    
    # SMB配置
    smb_config = {
        'hostname': 'Jayce',
        'port': 445,
        'username': 'smb',
        'password': 'smbsmb',
        'domain': 'WORKGROUP',
        'share_name': 'hlmj',
        'root_path': ''
    }
    
    # 测试文件
    test_files = ['398.xml', '398_MonitorData.ini', 'BugCrashReporter.exe']
    
    success_count = 0
    
    for file_key in test_files:
        logger.info(f"下载文件: {file_key}")
        
        data = download_smb_file_with_subprocess(smb_config, file_key)
        
        if data:
            logger.info(f"✅ 成功: {len(data)} bytes")
            success_count += 1
        else:
            logger.error(f"❌ 失败")
    
    logger.info(f"总结: {success_count}/{len(test_files)} 成功")
    
    return success_count == len(test_files)

if __name__ == "__main__":
    if test_real_task():
        print("🎉 测试成功！")
        sys.exit(0)
    else:
        print("❌ 测试失败！")
        sys.exit(1)
