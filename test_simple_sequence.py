#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试最简单的序列：直接下载 vs 先列表再下载
"""

import smb_storage_adapter
from storage_abstraction import StorageFactory, SMBStorageConfig, StorageType

def test_direct_download():
    """测试直接下载"""
    print("🧪 测试1: 直接下载文件")
    
    config = SMBStorageConfig(
        storage_type=StorageType.SMB,
        hostname='<PERSON><PERSON>',
        port=445,
        username='smb',
        password='smbsmb',
        domain='WORKGROUP',
        share_name='hlmj',
        root_path=''
    )
    
    adapter = StorageFactory.create_adapter(config)
    
    # 直接下载，不调用任何其他方法
    print("   直接下载 398.xml...")
    data = adapter.get_file('398.xml')
    
    if data:
        print(f"   ✅ 成功: {len(data)} bytes")
        return True
    else:
        print("   ❌ 失败: 返回None")
        return False

def test_list_then_download():
    """测试先列表再下载"""
    print("\n🧪 测试2: 先列表再下载")
    
    config = SMBStorageConfig(
        storage_type=StorageType.SMB,
        hostname='Jayce',
        port=445,
        username='smb',
        password='smbsmb',
        domain='WORKGROUP',
        share_name='hlmj',
        root_path=''
    )
    
    adapter = StorageFactory.create_adapter(config)
    
    # 先调用 list_files
    print("   1. 调用 list_files...")
    try:
        result = adapter.list_files("", max_keys=3)
        print(f"      ✅ 成功: 找到 {len(result.files)} 个文件")
    except Exception as e:
        print(f"      ❌ 失败: {e}")
        return False
    
    # 然后下载文件
    print("   2. 下载 398.xml...")
    data = adapter.get_file('398.xml')
    
    if data:
        print(f"      ✅ 成功: {len(data)} bytes")
        return True
    else:
        print("      ❌ 失败: 返回None")
        return False

def test_separate_adapters():
    """测试使用分离的适配器"""
    print("\n🧪 测试3: 使用分离的适配器")
    
    config = SMBStorageConfig(
        storage_type=StorageType.SMB,
        hostname='Jayce',
        port=445,
        username='smb',
        password='smbsmb',
        domain='WORKGROUP',
        share_name='hlmj',
        root_path=''
    )
    
    # 用一个适配器列表
    adapter1 = StorageFactory.create_adapter(config)
    print("   1. 适配器1调用 list_files...")
    try:
        result = adapter1.list_files("", max_keys=3)
        print(f"      ✅ 成功: 找到 {len(result.files)} 个文件")
    except Exception as e:
        print(f"      ❌ 失败: {e}")
        return False
    
    # 用另一个适配器下载
    adapter2 = StorageFactory.create_adapter(config)
    print("   2. 适配器2下载 398.xml...")
    data = adapter2.get_file('398.xml')
    
    if data:
        print(f"      ✅ 成功: {len(data)} bytes")
        return True
    else:
        print("      ❌ 失败: 返回None")
        return False

def main():
    """主函数"""
    print("🧪 SMB序列测试")
    print("=" * 50)
    
    tests = [
        ("直接下载", test_direct_download),
        ("先列表再下载", test_list_then_download),
        ("分离适配器", test_separate_adapters),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"   ❌ 异常: {e}")
            results.append((test_name, False))
    
    print(f"\n📊 测试结果:")
    for test_name, success in results:
        status = "✅ 成功" if success else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    # 分析结果
    success_count = sum(1 for _, success in results if success)
    
    if results[0][1] and not results[1][1]:
        print("\n🔍 结论: list_files 破坏了下载功能")
    elif results[0][1] and results[1][1]:
        print("\n🔍 结论: 所有方法都正常工作")
    elif not results[0][1]:
        print("\n🔍 结论: 下载功能完全失败")
    
    if results[2][1] and not results[1][1]:
        print("💡 建议: 使用分离的适配器实例")
    
    return success_count

if __name__ == "__main__":
    main()
