#!/usr/bin/env python3
"""
腾讯云COS存储桶权限测试脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from storage_abstraction import S3StorageConfig, StorageType
from s3_storage_adapter import S3StorageAdapter

def test_bucket_permissions():
    """测试存储桶权限"""
    print("🔐 腾讯云COS存储桶权限测试")
    print("=" * 50)
    
    # 配置信息 - 请替换为实际配置
    config = S3StorageConfig(
        storage_type=StorageType.S3,
        name="腾讯云COS权限测试",
        endpoint="https://cos.ap-shanghai.myqcloud.com",  # 替换为正确的区域
        access_key="your-secret-id",  # 替换为SecretId
        secret_key="your-secret-key",  # 替换为SecretKey
        bucket="",  # 先测试列出桶
        region="ap-shanghai"  # 替换为正确的区域
    )
    
    adapter = S3StorageAdapter(config)
    
    # 测试1: 列出所有存储桶
    print("📋 测试1: 列出所有存储桶...")
    try:
        buckets = adapter.list_buckets()
        print(f"✅ 成功列出 {len(buckets)} 个存储桶:")
        for i, bucket in enumerate(buckets, 1):
            print(f"   {i}. {bucket}")
        
        if not buckets:
            print("❌ 没有找到任何存储桶")
            return False
            
    except Exception as e:
        print(f"❌ 列出存储桶失败: {e}")
        return False
    
    # 测试2: 测试特定存储桶权限
    test_bucket = "h3c-deom-1319219496"  # 问题中的存储桶
    
    if test_bucket in buckets:
        print(f"\n📦 测试2: 测试存储桶 '{test_bucket}' 权限...")
        config.bucket = test_bucket
        bucket_adapter = S3StorageAdapter(config)
        
        # 测试2.1: ListBucket权限
        print("   🔍 测试ListBucket权限...")
        try:
            result = bucket_adapter.list_files(max_keys=10)
            print(f"   ✅ ListBucket成功，找到 {len(result.files)} 个文件")
            
            if result.files:
                print("   📄 文件列表:")
                for file_meta in result.files[:3]:
                    print(f"      • {file_meta.key} ({file_meta.size} bytes)")
        except Exception as e:
            print(f"   ❌ ListBucket失败: {e}")
            return False
        
        # 测试2.2: GetObject权限（如果有文件的话）
        if result.files:
            test_file = result.files[0]
            print(f"\n   📥 测试GetObject权限 (文件: {test_file.key})...")
            try:
                file_data = bucket_adapter.download_file(test_file.key)
                print(f"   ✅ GetObject成功，下载了 {len(file_data)} 字节")
            except Exception as e:
                print(f"   ❌ GetObject失败: {e}")
        
        # 测试2.3: PutObject权限
        print(f"\n   📤 测试PutObject权限...")
        test_content = b"LightRek permission test file"
        test_key = "lightrek-test-file.txt"
        
        try:
            bucket_adapter.upload_file(test_key, test_content)
            print(f"   ✅ PutObject成功，上传了测试文件")
            
            # 清理测试文件
            try:
                bucket_adapter.delete_file(test_key)
                print(f"   🗑️ 测试文件已清理")
            except:
                print(f"   ⚠️ 测试文件清理失败，请手动删除: {test_key}")
                
        except Exception as e:
            print(f"   ❌ PutObject失败: {e}")
            
            # 分析错误类型
            error_str = str(e).lower()
            if "403" in error_str:
                print("   💡 建议: 检查用户是否有PutObject权限")
            elif "404" in error_str:
                print("   💡 建议: 检查存储桶是否存在或区域配置")
        
        return True
        
    else:
        print(f"\n❌ 存储桶 '{test_bucket}' 不在可访问列表中")
        print("💡 可能的原因:")
        print("   1. 存储桶名称错误")
        print("   2. 存储桶不存在")
        print("   3. 没有访问该存储桶的权限")
        
        # 建议使用其他存储桶进行测试
        if buckets:
            print(f"\n🔄 建议使用以下存储桶进行测试:")
            for bucket in buckets[:3]:
                print(f"   • {bucket}")
        
        return False

def test_alternative_buckets():
    """测试其他可用的存储桶"""
    print("\n🔄 测试其他可用存储桶")
    print("=" * 30)
    
    config = S3StorageConfig(
        storage_type=StorageType.S3,
        name="腾讯云COS测试",
        endpoint="https://cos.ap-shanghai.myqcloud.com",
        access_key="your-secret-id",
        secret_key="your-secret-key",
        bucket="",
        region="ap-shanghai"
    )
    
    adapter = S3StorageAdapter(config)
    
    try:
        buckets = adapter.list_buckets()
        
        for bucket in buckets:
            print(f"\n📦 测试存储桶: {bucket}")
            config.bucket = bucket
            test_adapter = S3StorageAdapter(config)
            
            try:
                result = test_adapter.list_files(max_keys=5)
                print(f"   ✅ 可访问，包含 {len(result.files)} 个文件")
                
                # 如果这个桶可以访问，建议使用它
                if len(result.files) > 0:
                    print(f"   💡 建议: 可以使用此存储桶进行同步测试")
                    return bucket
                    
            except Exception as e:
                print(f"   ❌ 无法访问: {str(e)[:50]}...")
        
    except Exception as e:
        print(f"❌ 获取存储桶列表失败: {e}")
    
    return None

if __name__ == "__main__":
    print("🚀 腾讯云COS权限诊断工具")
    print("⚠️ 注意: 请在运行前修改配置中的SecretId和SecretKey")
    print()
    
    # 权限测试
    success = test_bucket_permissions()
    
    if not success:
        # 测试其他存储桶
        alternative = test_alternative_buckets()
        
        if alternative:
            print(f"\n✅ 找到可用的存储桶: {alternative}")
            print("💡 建议: 在同步任务中使用这个存储桶")
    
    print("\n📋 权限检查清单:")
    print("   □ 确认SecretId和SecretKey正确")
    print("   □ 确认用户有ListAllMyBuckets权限")
    print("   □ 确认用户有目标存储桶的ListBucket权限")
    print("   □ 确认用户有目标存储桶的GetObject权限")
    print("   □ 确认用户有目标存储桶的PutObject权限")
    print("   □ 确认存储桶名称和区域配置正确")
