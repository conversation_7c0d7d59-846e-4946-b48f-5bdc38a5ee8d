#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
参数验证测试脚本
"""

import requests
import json

def test_validation():
    """测试参数验证功能"""
    base_url = "http://localhost:8001"
    
    print("🧪 测试参数验证功能")
    print("=" * 50)
    
    # 测试用例
    test_cases = [
        {
            "name": "空名称测试",
            "data": {
                "name": "",
                "storage_type": "s3",
                "endpoint": "https://s3.amazonaws.com",
                "access_key": "test",
                "secret_key": "test",
                "bucket": "test"
            },
            "should_fail": True
        },
        {
            "name": "缺少必需字段测试",
            "data": {
                "name": "测试存储",
                "storage_type": "s3"
                # 缺少endpoint, access_key等
            },
            "should_fail": True
        },
        {
            "name": "无效端口测试",
            "data": {
                "name": "SFTP测试",
                "storage_type": "sftp",
                "host": "example.com",
                "port": 70000,  # 超出范围
                "username": "test"
            },
            "should_fail": True
        },
        {
            "name": "有效配置测试",
            "data": {
                "name": "有效S3存储",
                "storage_type": "s3",
                "endpoint": "https://s3.amazonaws.com",
                "access_key": "test_key",
                "secret_key": "test_secret",
                "bucket": "test-bucket"
            },
            "should_fail": False
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🔧 测试 {i}: {test_case['name']}")
        
        try:
            response = requests.post(
                f"{base_url}/api/sources",
                json=test_case["data"],
                timeout=10
            )
            
            print(f"   HTTP状态: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"   响应: {result}")
                
                if test_case["should_fail"]:
                    if result.get("success") == False:
                        print("   ✅ 正确拒绝了无效配置")
                    else:
                        print("   ❌ 错误接受了无效配置")
                else:
                    if result.get("success") == True:
                        print("   ✅ 正确接受了有效配置")
                    else:
                        print("   ❌ 错误拒绝了有效配置")
            else:
                print(f"   ❌ 意外的HTTP状态码: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print("参数验证测试完成")

if __name__ == "__main__":
    test_validation()
