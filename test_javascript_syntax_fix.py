#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试JavaScript语法修复
"""

import logging
import re
import os

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def test_javascript_syntax():
    """测试JavaScript语法"""
    logger = setup_logging()
    
    try:
        # 读取JavaScript文件
        with open('web/app.js', 'r', encoding='utf-8') as f:
            js_content = f.read()
        
        logger.info("✅ JavaScript文件读取成功")
        
        # 检查是否有转义函数
        if 'function escapeJsString(' in js_content:
            logger.info("✅ 转义函数存在")
        else:
            logger.error("❌ 转义函数不存在")
            return False
        
        # 检查转义函数的使用
        escape_usage_patterns = [
            r"escapeJsString\(parentPath\)",
            r"escapeJsString\(file\.path\)",
            r"escapeJsString\(file\.name\)",
            r"escapeJsString\(path\)",
            r"escapeJsString\(part\)",
        ]
        
        for pattern in escape_usage_patterns:
            if re.search(pattern, js_content):
                logger.info(f"✅ 转义使用正确: {pattern}")
            else:
                logger.warning(f"⚠️ 转义使用可能缺失: {pattern}")
        
        # 检查可能的语法错误模式
        potential_errors = [
            (r"onclick=['\"].*\$\{[^}]*\}.*['\"]", "onclick属性中的模板字符串"),
            (r"onclick=['\"][^'\"]*'[^'\"]*'[^'\"]*['\"]", "onclick属性中的未转义单引号"),
            (r"value=['\"][^'\"]*'[^'\"]*'[^'\"]*['\"]", "value属性中的未转义单引号"),
        ]
        
        for pattern, description in potential_errors:
            matches = re.findall(pattern, js_content)
            if matches:
                logger.warning(f"⚠️ 发现潜在语法错误 ({description}): {len(matches)} 处")
                for i, match in enumerate(matches[:3]):  # 只显示前3个
                    logger.warning(f"  {i+1}: {match[:100]}...")
            else:
                logger.info(f"✅ 无语法错误: {description}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试JavaScript语法失败: {e}")
        return False

def test_escape_function():
    """测试转义函数"""
    logger = logging.getLogger(__name__)
    
    try:
        # 模拟转义函数
        def escapeJsString(s):
            if not s:
                return ''
            return s.replace('\\', '\\\\') \
                    .replace("'", "\\'") \
                    .replace('"', '\\"') \
                    .replace('\n', '\\n') \
                    .replace('\r', '\\r') \
                    .replace('\t', '\\t')
        
        # 测试用例
        test_cases = [
            ("normal_file.txt", "normal_file.txt"),
            ("file with spaces.txt", "file with spaces.txt"),
            ("file'with'quotes.txt", "file\\'with\\'quotes.txt"),
            ('file"with"double.txt', 'file\\"with\\"double.txt'),
            ("file\nwith\nnewlines.txt", "file\\nwith\\nnewlines.txt"),
            ("file\twith\ttabs.txt", "file\\twith\\ttabs.txt"),
            ("file\\with\\backslashes.txt", "file\\\\with\\\\backslashes.txt"),
            ("", ""),
            (None, ""),
        ]
        
        for i, (input_str, expected) in enumerate(test_cases):
            try:
                result = escapeJsString(input_str)
                if result == expected:
                    logger.info(f"✅ 测试用例 {i+1}: '{input_str}' -> '{result}'")
                else:
                    logger.error(f"❌ 测试用例 {i+1}: '{input_str}' -> '{result}' (期望: '{expected}')")
                    return False
            except Exception as e:
                logger.error(f"❌ 测试用例 {i+1} 异常: {e}")
                return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试转义函数失败: {e}")
        return False

def test_html_generation():
    """测试HTML生成"""
    logger = logging.getLogger(__name__)
    
    try:
        # 模拟有问题的文件名
        problematic_files = [
            {"name": "file'with'quotes.txt", "path": "folder/file'with'quotes.txt"},
            {"name": 'file"with"double.txt', "path": 'folder/file"with"double.txt'},
            {"name": "file\nwith\nnewlines.txt", "path": "folder/file\nwith\nnewlines.txt"},
            {"name": "normal_file.txt", "path": "folder/normal_file.txt"},
        ]
        
        def escapeJsString(s):
            if not s:
                return ''
            return s.replace('\\', '\\\\') \
                    .replace("'", "\\'") \
                    .replace('"', '\\"') \
                    .replace('\n', '\\n') \
                    .replace('\r', '\\r') \
                    .replace('\t', '\\t')
        
        for i, file_info in enumerate(problematic_files):
            try:
                # 模拟生成HTML
                html = f'''
                <div class="file-item" onclick="navigateToPath('{escapeJsString(file_info["path"])}')">
                    <div class="file-name">{escapeJsString(file_info["name"])}</div>
                    <input type="checkbox" value="{escapeJsString(file_info["path"])}">
                </div>
                '''
                
                # 检查生成的HTML是否有语法错误
                if "onclick=\"navigateToPath('" in html and "')" in html:
                    logger.info(f"✅ HTML生成正确 {i+1}: {file_info['name']}")
                else:
                    logger.error(f"❌ HTML生成错误 {i+1}: {file_info['name']}")
                    return False
                    
            except Exception as e:
                logger.error(f"❌ HTML生成异常 {i+1}: {e}")
                return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试HTML生成失败: {e}")
        return False

def test_file_existence():
    """测试文件存在性"""
    logger = logging.getLogger(__name__)
    
    try:
        required_files = [
            'web/app.js',
            'web/index.html',
            'web/style.css'
        ]
        
        for file_path in required_files:
            if os.path.exists(file_path):
                logger.info(f"✅ 文件存在: {file_path}")
            else:
                logger.error(f"❌ 文件不存在: {file_path}")
                return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试文件存在性失败: {e}")
        return False

def main():
    """主函数"""
    logger = setup_logging()
    
    logger.info("🧪 JavaScript语法修复测试")
    logger.info("=" * 60)
    
    tests = [
        ("文件存在性测试", test_file_existence),
        ("JavaScript语法测试", test_javascript_syntax),
        ("转义函数测试", test_escape_function),
        ("HTML生成测试", test_html_generation),
    ]
    
    passed_tests = 0
    
    for test_name, test_func in tests:
        logger.info(f"\n📋 执行测试: {test_name}")
        
        try:
            if test_func():
                logger.info(f"✅ {test_name} 通过")
                passed_tests += 1
            else:
                logger.error(f"❌ {test_name} 失败")
        except Exception as e:
            logger.error(f"❌ {test_name} 异常: {e}")
    
    logger.info(f"\n📊 测试结果: {passed_tests}/{len(tests)} 通过")
    
    if passed_tests == len(tests):
        logger.info("🎉 所有测试通过！JavaScript语法修复成功")
        logger.info("")
        logger.info("📋 修复内容:")
        logger.info("  ✅ 添加了JavaScript字符串转义函数")
        logger.info("  ✅ 修复了文件路径中特殊字符的处理")
        logger.info("  ✅ 修复了onclick事件中的语法错误")
        logger.info("  ✅ 修复了HTML属性中的引号冲突")
        logger.info("")
        logger.info("🚀 现在可以:")
        logger.info("  1. 正常浏览包含特殊字符的文件和文件夹")
        logger.info("  2. 点击文件夹进入子目录而不会出现JavaScript错误")
        logger.info("  3. 选择文件进行下载而不会出现语法错误")
        logger.info("  4. 使用面包屑导航而不会出现问题")
        
        return 0
    else:
        logger.error("❌ 部分测试失败，需要进一步修复")
        return 1

if __name__ == "__main__":
    exit(main())
