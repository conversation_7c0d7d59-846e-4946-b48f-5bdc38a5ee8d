#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Web界面页面功能
"""

import requests
import json

def test_web_pages():
    """测试Web界面的页面功能"""
    base_url = 'http://localhost:8003'
    
    print('=== 测试Web界面页面功能 ===')
    
    # 测试主页
    try:
        response = requests.get(f'{base_url}/')
        print(f'主页访问: {response.status_code == 200}')
        if response.status_code == 200:
            content = response.text
            # 检查关键页面元素是否存在
            has_optimization = 'id="optimization-config"' in content
            has_task_logs = 'id="task-logs"' in content  
            has_user_manual = 'id="user-manual"' in content
            has_nav_optimization = 'showOptimizationConfig()' in content
            has_nav_logs = 'showTaskLogs()' in content
            has_nav_manual = 'showUserManual()' in content
            
            print(f'性能优化页面元素: {has_optimization}')
            print(f'任务日志页面元素: {has_task_logs}')
            print(f'用户手册页面元素: {has_user_manual}')
            print(f'性能优化导航: {has_nav_optimization}')
            print(f'任务日志导航: {has_nav_logs}')
            print(f'用户手册导航: {has_nav_manual}')
            
            # 检查JavaScript函数是否存在
            has_load_optimization = 'function loadOptimizationConfig()' in content
            has_load_logs = 'function loadTaskLogs()' in content
            has_show_page = 'function showPage(' in content
            
            print(f'加载优化配置函数: {has_load_optimization}')
            print(f'加载任务日志函数: {has_load_logs}')
            print(f'显示页面函数: {has_show_page}')
            
    except Exception as e:
        print(f'主页访问失败: {e}')
    
    # 测试优化配置API
    try:
        response = requests.get(f'{base_url}/api/optimization-config')
        print(f'\\n优化配置API: {response.status_code == 200}')
        if response.status_code == 200:
            config = response.json()
            print(f'优化配置内容: {list(config.keys())}')
        else:
            print(f'优化配置API错误: {response.status_code} - {response.text[:200]}')
    except Exception as e:
        print(f'优化配置API失败: {e}')
    
    # 测试任务执行历史API
    try:
        response = requests.get(f'{base_url}/api/task-executions')
        print(f'\\n任务执行历史API: {response.status_code == 200}')
        if response.status_code == 200:
            executions = response.json()
            print(f'执行历史数量: {len(executions.get("executions", []))}')
        else:
            print(f'任务执行历史API错误: {response.status_code} - {response.text[:200]}')
    except Exception as e:
        print(f'任务执行历史API失败: {e}')
    
    # 测试用户手册页面
    try:
        response = requests.get(f'{base_url}/manual')
        print(f'\\n用户手册页面: {response.status_code == 200}')
        if response.status_code == 200:
            print(f'用户手册内容长度: {len(response.text)} 字符')
            # 检查手册内容
            content = response.text
            has_manual_content = '用户手册' in content and 'LightRek' in content
            print(f'用户手册内容完整: {has_manual_content}')
        else:
            print(f'用户手册页面错误: {response.status_code} - {response.text[:200]}')
    except Exception as e:
        print(f'用户手册页面失败: {e}')
    
    # 测试任务日志页面重定向
    try:
        response = requests.get(f'{base_url}/logs', allow_redirects=False)
        print(f'\\n任务日志页面重定向: {response.status_code == 302}')
        if response.status_code == 302:
            location = response.headers.get('Location', '')
            print(f'重定向到: {location}')
    except Exception as e:
        print(f'任务日志页面测试失败: {e}')
    
    # 测试统计信息API
    try:
        response = requests.get(f'{base_url}/api/statistics')
        print(f'\\n统计信息API: {response.status_code == 200}')
        if response.status_code == 200:
            stats = response.json()
            print(f'统计信息: {stats}')
    except Exception as e:
        print(f'统计信息API失败: {e}')
    
    # 测试POST优化配置
    try:
        test_config = {
            'max_workers': 10,
            'chunk_size_mb': 15,
            'enable_parallel_scan': True,
            'retry_times': 5
        }
        response = requests.post(f'{base_url}/api/optimization-config', 
                               json=test_config,
                               headers={'Content-Type': 'application/json'})
        print(f'\\n保存优化配置: {response.status_code == 200}')
        if response.status_code == 200:
            result = response.json()
            print(f'保存结果: {result}')
        else:
            print(f'保存优化配置错误: {response.status_code} - {response.text[:200]}')
    except Exception as e:
        print(f'保存优化配置失败: {e}')
    
    print('\\n页面功能测试完成')

if __name__ == '__main__':
    test_web_pages()
