"""
高效同步引擎 - 支持块级别同步、并行处理、断点续传
"""

import hashlib
import threading
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict, Any, Optional, Tuple
import logging
from dataclasses import dataclass
from storage_abstraction import StorageAdapter, FileMetadata
from database_manager import db_manager

# 全局SMB子进程信号量，限制并发数
_smb_subprocess_semaphore = threading.Semaphore(2)  # 最多2个并发SMB子进程


@dataclass
class SyncProgress:
    """同步进度信息"""
    total_files: int = 0
    completed_files: int = 0
    failed_files: int = 0
    skipped_files: int = 0
    total_bytes: int = 0
    transferred_bytes: int = 0
    current_file: str = ""
    speed_mbps: float = 0.0


class EfficientSyncEngine:
    """高效同步引擎"""
    
    def __init__(self, max_workers: int = 8, chunk_size: int = 8 * 1024 * 1024):
        self.max_workers = max_workers
        self.chunk_size = chunk_size  # 8MB chunks
        self.logger = logging.getLogger(__name__)
        self._stop_event = threading.Event()
        self._progress_lock = threading.Lock()

        # 确保日志配置正确
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)
        
    def sync_files(self, source_adapter: StorageAdapter, target_adapter: StorageAdapter,
                   files_to_sync: List[FileMetadata], task_id: str, execution_id: str,
                   progress_callback: Optional[callable] = None) -> SyncProgress:
        """高效同步文件列表"""
        
        progress = SyncProgress(total_files=len(files_to_sync))
        
        # 计算总字节数
        for file_meta in files_to_sync:
            try:
                size = int(file_meta.size) if file_meta.size else 0
                progress.total_bytes += size
            except (ValueError, TypeError):
                pass
        
        self.logger.info(f"开始高效同步: {progress.total_files} 个文件, 总大小: {progress.total_bytes / (1024*1024):.2f} MB")
        
        # 按文件大小分组，优化同步策略
        small_files = []  # < 1MB
        medium_files = []  # 1MB - 100MB
        large_files = []  # > 100MB
        
        for file_meta in files_to_sync:
            try:
                size = int(file_meta.size) if file_meta.size else 0
                if size < 1024 * 1024:  # 1MB
                    small_files.append(file_meta)
                elif size < 100 * 1024 * 1024:  # 100MB
                    medium_files.append(file_meta)
                else:
                    large_files.append(file_meta)
            except (ValueError, TypeError):
                small_files.append(file_meta)
        
        self.logger.info(f"文件分组: 小文件 {len(small_files)}, 中等文件 {len(medium_files)}, 大文件 {len(large_files)}")
        
        start_time = time.time()
        
        try:
            # 1. 并行同步小文件 (高并发)
            if small_files:
                self._sync_small_files_parallel(source_adapter, target_adapter, small_files, 
                                               progress, execution_id, progress_callback)
            
            # 2. 并行同步中等文件 (中等并发)
            if medium_files:
                self._sync_medium_files_parallel(source_adapter, target_adapter, medium_files,
                                                progress, execution_id, progress_callback)
            
            # 3. 串行同步大文件 (块级别传输)
            if large_files:
                self._sync_large_files_chunked(source_adapter, target_adapter, large_files,
                                              progress, execution_id, progress_callback)
            
        except Exception as e:
            self.logger.error(f"同步过程中出错: {e}")
            db_manager.add_task_log(execution_id, 'ERROR', f'同步失败: {e}')
        
        # 计算同步速度
        elapsed_time = time.time() - start_time
        if elapsed_time > 0:
            progress.speed_mbps = (progress.transferred_bytes / (1024 * 1024)) / elapsed_time
        
        self.logger.info(f"同步完成: {progress.completed_files}/{progress.total_files} 文件, "
                        f"速度: {progress.speed_mbps:.2f} MB/s")
        
        return progress
    
    def _sync_small_files_parallel(self, source_adapter: StorageAdapter, target_adapter: StorageAdapter,
                                  files: List[FileMetadata], progress: SyncProgress, execution_id: str,
                                  progress_callback: Optional[callable] = None):
        """并行同步小文件"""
        self.logger.info(f"开始并行同步 {len(files)} 个小文件")

        # 对于SFTP，降低并发数避免连接过载
        max_workers = 2 if 'SFTP' in source_adapter.__class__.__name__ else self.max_workers

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_file = {
                executor.submit(self._sync_single_file, source_adapter, target_adapter, file_meta, execution_id): file_meta
                for file_meta in files
            }
            
            for future in as_completed(future_to_file):
                if self._stop_event.is_set():
                    break
                    
                file_meta = future_to_file[future]
                try:
                    success, bytes_transferred = future.result()
                    with self._progress_lock:
                        if success:
                            progress.completed_files += 1
                            progress.transferred_bytes += bytes_transferred
                        else:
                            progress.failed_files += 1
                        
                        progress.current_file = file_meta.key
                        
                        if progress_callback:
                            progress_callback(progress)
                            
                except Exception as e:
                    self.logger.error(f"同步文件 {file_meta.key} 失败: {e}")
                    with self._progress_lock:
                        progress.failed_files += 1
    
    def _sync_medium_files_parallel(self, source_adapter: StorageAdapter, target_adapter: StorageAdapter,
                                   files: List[FileMetadata], progress: SyncProgress, execution_id: str,
                                   progress_callback: Optional[callable] = None):
        """并行同步中等文件"""
        self.logger.info(f"开始并行同步 {len(files)} 个中等文件")

        # 对于SFTP，使用更少的并发数
        if 'SFTP' in source_adapter.__class__.__name__:
            workers = 1  # SFTP串行处理中等文件
        else:
            workers = min(4, self.max_workers)
        
        with ThreadPoolExecutor(max_workers=workers) as executor:
            future_to_file = {
                executor.submit(self._sync_single_file, source_adapter, target_adapter, file_meta, execution_id): file_meta
                for file_meta in files
            }
            
            for future in as_completed(future_to_file):
                if self._stop_event.is_set():
                    break
                    
                file_meta = future_to_file[future]
                try:
                    success, bytes_transferred = future.result()
                    with self._progress_lock:
                        if success:
                            progress.completed_files += 1
                            progress.transferred_bytes += bytes_transferred
                        else:
                            progress.failed_files += 1
                        
                        progress.current_file = file_meta.key
                        
                        if progress_callback:
                            progress_callback(progress)
                            
                except Exception as e:
                    self.logger.error(f"同步文件 {file_meta.key} 失败: {e}")
                    with self._progress_lock:
                        progress.failed_files += 1
    
    def _sync_large_files_chunked(self, source_adapter: StorageAdapter, target_adapter: StorageAdapter,
                                 files: List[FileMetadata], progress: SyncProgress, execution_id: str,
                                 progress_callback: Optional[callable] = None):
        """块级别同步大文件"""
        self.logger.info(f"开始块级别同步 {len(files)} 个大文件")
        
        for file_meta in files:
            if self._stop_event.is_set():
                break
                
            try:
                success, bytes_transferred = self._sync_large_file_chunked(
                    source_adapter, target_adapter, file_meta, execution_id, progress_callback
                )
                
                with self._progress_lock:
                    if success:
                        progress.completed_files += 1
                        progress.transferred_bytes += bytes_transferred
                    else:
                        progress.failed_files += 1
                    
                    progress.current_file = file_meta.key
                    
                    if progress_callback:
                        progress_callback(progress)
                        
            except Exception as e:
                self.logger.error(f"同步大文件 {file_meta.key} 失败: {e}")
                with self._progress_lock:
                    progress.failed_files += 1
    
    def _sync_single_file(self, source_adapter: StorageAdapter, target_adapter: StorageAdapter,
                         file_meta: FileMetadata, execution_id: str) -> Tuple[bool, int]:
        """同步单个文件，带重试机制"""
        max_retries = 3
        file_data = None

        for attempt in range(max_retries):
            try:
                # 下载文件 - 对SMB存储使用子进程隔离下载
                self.logger.debug(f"开始下载文件: {file_meta.key} (尝试 {attempt + 1}/{max_retries})")

                if hasattr(source_adapter.config, 'storage_type') and source_adapter.config.storage_type.name == 'SMB':
                    # 为SMB存储使用子进程隔离下载避免状态污染
                    self.logger.info(f"使用SMB子进程下载: {file_meta.key}")
                    file_data = self._download_smb_file_with_subprocess(source_adapter.config, file_meta.key)
                else:
                    # 其他存储类型使用原有逻辑
                    self.logger.debug(f"使用标准下载: {file_meta.key}")
                    file_data = source_adapter.get_file(file_meta.key)

                if file_data is None:
                    if attempt < max_retries - 1:
                        self.logger.warning(f"下载失败，将重试: {file_meta.key} (尝试 {attempt + 1}/{max_retries})")
                        time.sleep(1)  # 等待1秒后重试
                        continue
                    else:
                        error_msg = f"下载失败，已达最大重试次数: {file_meta.key}"
                        self.logger.error(error_msg)
                        db_manager.add_task_log(execution_id, 'ERROR', error_msg)
                        return False, 0

                self.logger.debug(f"文件下载成功: {file_meta.key}, 大小: {len(file_data)} 字节")
                break  # 下载成功，跳出重试循环

            except Exception as e:
                if attempt < max_retries - 1:
                    self.logger.warning(f"下载异常，将重试: {file_meta.key} - {e}")
                    time.sleep(1)
                    continue
                else:
                    error_msg = f"下载异常，已达最大重试次数: {file_meta.key} - {e}"
                    self.logger.error(error_msg)
                    db_manager.add_task_log(execution_id, 'ERROR', error_msg)
                    return False, 0

        # 确保我们有数据才继续上传
        if file_data is None:
            self.logger.error(f"文件下载失败，无法继续: {file_meta.key}")
            return False, 0
            
        # 上传文件
        try:
            self.logger.debug(f"开始上传文件: {file_meta.key}")
            success = target_adapter.put_file(
                file_meta.key, file_data,
                file_meta.content_type or 'binary/octet-stream'
            )

            if success:
                bytes_transferred = len(file_data)
                self.logger.debug(f"文件上传成功: {file_meta.key}")

                # 更新文件哈希记录
                try:
                    file_hash = hashlib.md5(file_data).hexdigest()
                    db_manager.update_file_hash(
                        f"target_{target_adapter.__class__.__name__}",
                        file_meta.key,
                        len(file_data),
                        file_hash,
                        file_meta.last_modified.isoformat() if file_meta.last_modified else ""
                    )
                except Exception as e:
                    self.logger.warning(f"更新文件哈希失败: {file_meta.key} - {e}")

                return True, bytes_transferred
            else:
                error_msg = f"文件上传失败: {file_meta.key}"
                self.logger.warning(error_msg)
                db_manager.add_task_log(execution_id, 'WARNING', error_msg)
                return False, 0

        except Exception as e:
            error_msg = f"同步文件异常: {file_meta.key} - {e}"
            self.logger.error(error_msg)
            db_manager.add_task_log(execution_id, 'ERROR', error_msg)
            return False, 0
    
    def _sync_large_file_chunked(self, source_adapter: StorageAdapter, target_adapter: StorageAdapter,
                                file_meta: FileMetadata, execution_id: str, 
                                progress_callback: Optional[callable] = None) -> Tuple[bool, int]:
        """块级别同步大文件"""
        try:
            # 对于大文件，如果目标适配器支持分片上传，使用分片上传
            # 对SMB存储使用子进程隔离下载
            if hasattr(source_adapter.config, 'storage_type') and source_adapter.config.storage_type.name == 'SMB':
                self.logger.info(f"使用SMB子进程下载(块级别): {file_meta.key}")
                file_data = self._download_smb_file_with_subprocess(source_adapter.config, file_meta.key)
            else:
                file_data = source_adapter.get_file(file_meta.key)
            if file_data is None:
                return False, 0
            
            # 检查目标适配器是否支持分片上传
            if hasattr(target_adapter, 'put_file_chunked'):
                success = target_adapter.put_file_chunked(
                    file_meta.key, file_data, self.chunk_size,
                    file_meta.content_type or 'binary/octet-stream'
                )
            else:
                # 回退到普通上传
                success = target_adapter.put_file(
                    file_meta.key, file_data,
                    file_meta.content_type or 'binary/octet-stream'
                )
            
            if success:
                bytes_transferred = len(file_data)
                # 更新文件哈希记录
                file_hash = hashlib.md5(file_data).hexdigest()
                db_manager.update_file_hash(
                    f"target_{target_adapter.__class__.__name__}",
                    file_meta.key,
                    len(file_data),
                    file_hash,
                    file_meta.last_modified.isoformat() if file_meta.last_modified else ""
                )
                
                db_manager.add_task_log(execution_id, 'INFO', 
                                      f'大文件同步成功: {file_meta.key} ({bytes_transferred / (1024*1024):.2f} MB)')
                return True, bytes_transferred
            else:
                return False, 0
                
        except Exception as e:
            self.logger.error(f"块级别同步文件 {file_meta.key} 时出错: {e}")
            db_manager.add_task_log(execution_id, 'ERROR', f'大文件同步失败: {file_meta.key} - {e}')
            return False, 0
    
    def _download_smb_file_with_subprocess(self, smb_config, file_key):
        """使用子进程隔离下载SMB文件"""
        import subprocess
        import json
        import base64
        import os
        import tempfile

        # 使用信号量限制并发SMB子进程数
        with _smb_subprocess_semaphore:
            temp_file = None
            try:
                # 准备配置数据
                config_data = {
                    'hostname': smb_config.hostname,
                    'port': smb_config.port,
                    'username': smb_config.username,
                    'password': smb_config.password,
                    'domain': smb_config.domain or '',
                    'share_name': smb_config.share_name,
                    'root_path': smb_config.root_path or ''
                }

                # 创建临时文件
                with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False, encoding='utf-8') as f:
                    json.dump({
                        'config': config_data,
                        'file_key': file_key
                    }, f)
                    temp_file = f.name

                # 构建命令
                script_path = os.path.join(os.path.dirname(__file__), 'smb_downloader.py')
                cmd = ['python', script_path, temp_file]

                self.logger.info(f"SMB子进程命令: {cmd}")
                self.logger.info(f"SMB子进程工作目录: {os.path.dirname(__file__)}")

                # 执行子进程
                result = subprocess.run(
                    cmd,
                    capture_output=True,
                    text=True,
                    timeout=30,
                    cwd=os.path.dirname(__file__),
                    encoding='utf-8',
                    errors='ignore'
                )

                self.logger.info(f"SMB子进程返回码: {result.returncode}")
                self.logger.info(f"SMB子进程标准输出: {repr(result.stdout)}")
                self.logger.info(f"SMB子进程标准错误: {repr(result.stderr)}")

                if result.returncode == 0:
                    output = result.stdout.strip()
                    # 查找SUCCESS行
                    success_line = None
                    for line in output.split('\n'):
                        if line.startswith("SUCCESS:"):
                            success_line = line
                            break

                    if success_line:
                        # 解析成功结果：SUCCESS:size:output_file
                        parts = success_line.split(':', 2)
                        if len(parts) == 3:
                            size = int(parts[1])
                            output_file = parts[2]

                            # 从输出文件读取数据
                            try:
                                with open(output_file, 'rb') as f:
                                    data = f.read()
                                # 清理输出文件
                                os.unlink(output_file)
                                self.logger.debug(f"SMB子进程下载成功: {file_key} ({size} bytes)")
                                return data
                            except Exception as e:
                                self.logger.warning(f"SMB子进程读取输出文件失败 {file_key}: {e}")
                                return None
                        else:
                            self.logger.warning(f"SMB子进程输出格式错误: {success_line}")
                            return None
                    else:
                        # 查找FAILED或ERROR行
                        for line in output.split('\n'):
                            if line.startswith("FAILED:"):
                                self.logger.warning(f"SMB子进程下载失败: {file_key}")
                                return None
                            elif line.startswith("ERROR:"):
                                error_msg = line[6:]  # 移除"ERROR:"前缀
                                self.logger.warning(f"SMB子进程错误 {file_key}: {error_msg}")
                                return None

                        self.logger.warning(f"SMB子进程未知输出: {output}")
                        return None
                else:
                    self.logger.warning(f"SMB子进程失败 {file_key} (返回码: {result.returncode}): {result.stderr}")
                    return None

            except subprocess.TimeoutExpired:
                self.logger.warning(f"SMB子进程下载超时: {file_key}")
                return None
            except Exception as e:
                self.logger.warning(f"SMB子进程下载异常 {file_key}: {e}")
                return None
            finally:
                # 清理临时文件
                if temp_file and os.path.exists(temp_file):
                    try:
                        os.unlink(temp_file)
                    except:
                        pass

    def stop(self):
        """停止同步"""
        self._stop_event.set()
        self.logger.info("同步引擎停止信号已发送")
