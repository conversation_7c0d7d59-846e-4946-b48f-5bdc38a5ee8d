#!/usr/bin/env python3
"""
修复JavaScript变量与Python format()冲突的问题
将JavaScript模板字符串中的变量转义
"""

import re
import shutil
from pathlib import Path

def fix_javascript_variables(file_path):
    """修复JavaScript变量与Python format()冲突"""
    
    # 备份原文件
    backup_path = str(file_path) + '.js_fix_backup'
    shutil.copy2(str(file_path), backup_path)
    print(f"已备份原文件到: {backup_path}")
    
    # 读取文件内容
    with open(str(file_path), 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 需要转义的JavaScript变量模式
    js_patterns = [
        (r'\$\{sourceData\.([^}]+)\}', r'${{sourceData.\1}}'),
        (r'\$\{targetData\.([^}]+)\}', r'${{targetData.\1}}'),
        (r'\$\{taskData\.([^}]+)\}', r'${{taskData.\1}}'),
        (r'\$\{data\.([^}]+)\}', r'${{data.\1}}'),
        (r'\$\{result\.([^}]+)\}', r'${{result.\1}}'),
        (r'\$\{response\.([^}]+)\}', r'${{response.\1}}'),
        (r'\$\{config\.([^}]+)\}', r'${{config.\1}}'),
        (r'\$\{item\.([^}]+)\}', r'${{item.\1}}'),
        (r'\$\{node\.([^}]+)\}', r'${{node.\1}}'),
        (r'\$\{file\.([^}]+)\}', r'${{file.\1}}'),
        (r'\$\{exec\.([^}]+)\}', r'${{exec.\1}}'),
        (r'\$\{log\.([^}]+)\}', r'${{log.\1}}'),
        (r'\$\{bucket\}', r'${{bucket}}'),
        (r'\$\{path\}', r'${{path}}'),
        (r'\$\{id\}', r'${{id}}'),
        (r'\$\{executionId\}', r'${{executionId}}'),
        (r'\$\{title\}', r'${{title}}'),
        (r'\$\{message\}', r'${{message}}'),
        (r'\$\{type\}', r'${{type}}'),
        (r'\$\{preset\}', r'${{preset}}'),
        (r'\$\{count\}', r'${{count}}'),
        (r'\$\{percentage\}', r'${{percentage}}'),
        (r'\$\{height\}', r'${{height}}'),
        (r'\$\{date\}', r'${{date}}'),
        (r'\$\{targetInputId\}', r'${{targetInputId}}'),
        (r'\$\{error\}', r'${{error}}'),
        (r'\$\{logs\}', r'${{logs}}'),
        (r'\$\{buckets\}', r'${{buckets}}'),
        (r'\$\{bucketListHtml\}', r'${{bucketListHtml}}'),
    ]
    
    # 统计修复前的问题数量
    total_fixes = 0
    
    # 应用所有修复模式
    fixed_content = content
    for pattern, replacement in js_patterns:
        matches = re.findall(pattern, fixed_content)
        if matches:
            print(f"修复模式: {pattern} -> 找到 {len(matches)} 个匹配")
            total_fixes += len(matches)
            fixed_content = re.sub(pattern, replacement, fixed_content)
    
    # 还需要处理一些特殊的复杂表达式
    complex_patterns = [
        # 处理复杂的JavaScript表达式
        (r'\$\{([^}]*sourceData[^}]*)\}', r'${{\1}}'),
        (r'\$\{([^}]*targetData[^}]*)\}', r'${{\1}}'),
        (r'\$\{([^}]*taskData[^}]*)\}', r'${{\1}}'),
        (r'\$\{([^}]*\.length[^}]*)\}', r'${{\1}}'),
        (r'\$\{([^}]*\.map[^}]*)\}', r'${{\1}}'),
        (r'\$\{([^}]*\.join[^}]*)\}', r'${{\1}}'),
        (r'\$\{([^}]*\.slice[^}]*)\}', r'${{\1}}'),
        (r'\$\{([^}]*\?\s*[^}]*:\s*[^}]*)\}', r'${{\1}}'),  # 三元运算符
        (r'\$\{([^}]*===\s*[^}]*)\}', r'${{\1}}'),  # 比较运算符
        (r'\$\{([^}]*\|\|\s*[^}]*)\}', r'${{\1}}'),  # 逻辑或运算符
    ]
    
    for pattern, replacement in complex_patterns:
        matches = re.findall(pattern, fixed_content)
        if matches:
            print(f"修复复杂表达式: 找到 {len(matches)} 个匹配")
            total_fixes += len(matches)
            fixed_content = re.sub(pattern, replacement, fixed_content)
    
    # 写入修复后的内容
    with open(str(file_path), 'w', encoding='utf-8') as f:
        f.write(fixed_content)
    
    print(f"修复完成:")
    print(f"  - 总修复数量: {total_fixes}")
    
    return total_fixes

def main():
    """主函数"""
    file_path = Path("complete_web_interface.py")
    
    if not file_path.exists():
        print(f"❌ 文件不存在: {file_path}")
        return
    
    print("🔧 开始修复JavaScript变量与Python format()冲突...")
    print("=" * 60)
    
    try:
        fixed_count = fix_javascript_variables(file_path)
        
        print("\n" + "=" * 60)
        print("🎯 修复总结:")
        print(f"  - 文件: {file_path}")
        print(f"  - 修复数量: {fixed_count}")
        
        if fixed_count > 0:
            print("\n🎉 修复成功! JavaScript变量已正确转义")
            print("💡 请重启Web服务器并测试功能")
        else:
            print("\n💡 没有发现需要修复的JavaScript变量")
            
    except Exception as e:
        print(f"❌ 修复过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
