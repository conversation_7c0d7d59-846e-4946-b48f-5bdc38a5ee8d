#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试文件名编码问题
"""

import json
import tempfile

def test_filename_encoding():
    """测试文件名编码问题"""
    print("🧪 测试文件名编码问题")
    
    # 测试文件名
    test_files = ['TerSafe.dll', 'TP3Helper.exe', 'UnityPlayer.dll']
    
    for file_key in test_files:
        print(f"\n测试文件: {file_key}")
        print(f"  原始类型: {type(file_key)}")
        print(f"  原始编码: {repr(file_key)}")
        print(f"  UTF-8编码: {file_key.encode('utf-8')}")
        
        # 测试JSON序列化
        config_data = {
            'hostname': 'Jay<PERSON>',
            'port': 445,
            'username': 'smb',
            'password': 'smbsmb',
            'domain': 'WORKGROUP',
            'share_name': 'hlmj',
            'root_path': ''
        }
        
        # 创建临时文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False, encoding='utf-8') as f:
            json.dump({
                'config': config_data,
                'file_key': file_key
            }, f)
            temp_file = f.name
        
        # 读取并检查
        with open(temp_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        recovered_file_key = data['file_key']
        print(f"  恢复后类型: {type(recovered_file_key)}")
        print(f"  恢复后编码: {repr(recovered_file_key)}")
        print(f"  是否相等: {file_key == recovered_file_key}")
        
        # 清理
        import os
        os.unlink(temp_file)
        
        # 检查是否有编码问题
        if file_key != recovered_file_key:
            print(f"  ❌ 编码问题！")
        else:
            print(f"  ✅ 编码正常")

if __name__ == "__main__":
    test_filename_encoding()
