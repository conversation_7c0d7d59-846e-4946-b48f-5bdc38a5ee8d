#!/usr/bin/env python3
"""
测试SFTP配置功能
"""

import requests
import json

def test_sftp_config():
    """测试SFTP配置"""
    print("🧪 测试SFTP配置功能")
    print("=" * 50)
    
    # 测试数据 - 直接发送配置字段，不嵌套在config中
    sftp_config = {
        "name": "测试SFTP服务器",
        "description": "用于测试的SFTP服务器",
        "storage_type": "sftp",
        "hostname": "**************",
        "port": 22,
        "username": "root",
        "password": "rootreadbook2",
        "root_path": "/"
    }
    
    try:
        # 发送POST请求
        response = requests.post(
            'http://localhost:8001/api/sources',
            json=sftp_config,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        print(f"HTTP状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ SFTP配置添加成功")
            else:
                print(f"❌ SFTP配置添加失败: {result.get('message', '未知错误')}")
        else:
            print(f"❌ HTTP请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    
    # 测试获取配置
    print("\n🔍 测试获取存储配置...")
    try:
        response = requests.get('http://localhost:8001/api/sources', timeout=5)
        print(f"HTTP状态码: {response.status_code}")
        if response.status_code == 200:
            sources = response.json()
            print(f"当前数据源数量: {len(sources)}")
            for source_id, config in sources.items():
                print(f"  - {source_id}: {config.get('storage_type', 'unknown')}")
        
    except Exception as e:
        print(f"❌ 获取配置失败: {e}")

if __name__ == "__main__":
    test_sftp_config()
