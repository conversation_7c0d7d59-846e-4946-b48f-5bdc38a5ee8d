#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库管理器迁移脚本 - 解决并发访问问题
"""

import os
import shutil
import logging
from datetime import datetime

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def backup_original_database_manager():
    """备份原始数据库管理器"""
    logger = logging.getLogger(__name__)
    
    original_file = "database_manager.py"
    backup_file = f"database_manager_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.py"
    
    try:
        if os.path.exists(original_file):
            shutil.copy2(original_file, backup_file)
            logger.info(f"✅ 已备份原始数据库管理器: {backup_file}")
            return True
        else:
            logger.warning(f"⚠️ 原始数据库管理器文件不存在: {original_file}")
            return False
    except Exception as e:
        logger.error(f"❌ 备份失败: {e}")
        return False

def update_database_manager():
    """更新数据库管理器"""
    logger = logging.getLogger(__name__)
    
    try:
        # 读取改进的数据库管理器
        with open("improved_database_manager.py", "r", encoding="utf-8") as f:
            improved_content = f.read()
        
        # 替换类名和全局实例名
        updated_content = improved_content.replace(
            "class ImprovedDatabaseManager:",
            "class DatabaseManager:"
        ).replace(
            "improved_db_manager = ImprovedDatabaseManager()",
            "db_manager = DatabaseManager()"
        )
        
        # 写入到原始文件
        with open("database_manager.py", "w", encoding="utf-8") as f:
            f.write(updated_content)
        
        logger.info("✅ 数据库管理器更新成功")
        return True
        
    except Exception as e:
        logger.error(f"❌ 更新数据库管理器失败: {e}")
        return False

def test_new_database_manager():
    """测试新的数据库管理器"""
    logger = logging.getLogger(__name__)
    
    try:
        # 导入新的数据库管理器
        from database_manager import db_manager
        
        logger.info("✅ 新数据库管理器导入成功")
        
        # 测试基本操作
        test_storage_id = "test_storage"
        test_file_key = "test_file.txt"
        test_file_hash = "test_hash_123"
        
        # 测试更新文件哈希
        success = db_manager.update_file_hash(
            storage_id=test_storage_id,
            file_key=test_file_key,
            file_size=1024,
            file_hash=test_file_hash,
            last_modified=datetime.now().isoformat()
        )
        
        if success:
            logger.info("✅ 文件哈希更新测试成功")
            
            # 测试获取文件哈希
            hash_record = db_manager.get_file_hash(test_storage_id, test_file_key)
            if hash_record and hash_record['file_hash'] == test_file_hash:
                logger.info("✅ 文件哈希获取测试成功")
                return True
            else:
                logger.error("❌ 文件哈希获取测试失败")
                return False
        else:
            logger.error("❌ 文件哈希更新测试失败")
            return False
            
    except Exception as e:
        logger.error(f"❌ 测试新数据库管理器失败: {e}")
        return False

def update_imports_in_files():
    """更新其他文件中的导入"""
    logger = logging.getLogger(__name__)
    
    files_to_update = [
        "unified_task_manager.py",
        "static_web_server.py",
        "lightrek.py"
    ]
    
    updated_files = []
    
    for file_path in files_to_update:
        try:
            if not os.path.exists(file_path):
                logger.warning(f"⚠️ 文件不存在，跳过: {file_path}")
                continue
            
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()
            
            # 检查是否需要更新
            if "from database_manager import db_manager" in content:
                logger.info(f"✅ {file_path} 导入已正确")
                updated_files.append(file_path)
            elif "from improved_database_manager import improved_db_manager" in content:
                # 更新导入
                updated_content = content.replace(
                    "from improved_database_manager import improved_db_manager",
                    "from database_manager import db_manager"
                ).replace(
                    "improved_db_manager",
                    "db_manager"
                )
                
                with open(file_path, "w", encoding="utf-8") as f:
                    f.write(updated_content)
                
                logger.info(f"✅ 已更新 {file_path} 中的导入")
                updated_files.append(file_path)
            else:
                logger.info(f"ℹ️ {file_path} 无需更新导入")
                
        except Exception as e:
            logger.error(f"❌ 更新 {file_path} 失败: {e}")
    
    return updated_files

def create_concurrent_test_script():
    """创建并发测试脚本"""
    logger = logging.getLogger(__name__)
    
    test_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库并发访问测试
"""

import threading
import time
import logging
from datetime import datetime
import uuid

def setup_logging():
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(threadName)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def concurrent_hash_update_test(thread_id: int, num_operations: int = 100):
    """并发哈希更新测试"""
    logger = logging.getLogger(__name__)
    
    try:
        from database_manager import db_manager
        
        success_count = 0
        
        for i in range(num_operations):
            storage_id = f"test_storage_{thread_id}"
            file_key = f"test_file_{i}.txt"
            file_hash = f"hash_{thread_id}_{i}_{uuid.uuid4().hex[:8]}"
            
            success = db_manager.update_file_hash(
                storage_id=storage_id,
                file_key=file_key,
                file_size=1024 + i,
                file_hash=file_hash,
                last_modified=datetime.now().isoformat()
            )
            
            if success:
                success_count += 1
            else:
                logger.error(f"线程 {thread_id} 操作 {i} 失败")
            
            # 小延迟模拟真实场景
            time.sleep(0.01)
        
        logger.info(f"线程 {thread_id} 完成: {success_count}/{num_operations} 成功")
        return success_count
        
    except Exception as e:
        logger.error(f"线程 {thread_id} 异常: {e}")
        return 0

def main():
    """主函数"""
    logger = setup_logging()
    
    logger.info("🧪 数据库并发访问测试")
    logger.info("=" * 50)
    
    # 测试参数
    num_threads = 10
    operations_per_thread = 50
    
    logger.info(f"启动 {num_threads} 个线程，每个线程执行 {operations_per_thread} 次操作")
    
    # 创建线程
    threads = []
    results = {}
    
    start_time = time.time()
    
    for i in range(num_threads):
        thread = threading.Thread(
            target=lambda tid=i: results.update({tid: concurrent_hash_update_test(tid, operations_per_thread)}),
            name=f"TestThread-{i}"
        )
        threads.append(thread)
        thread.start()
    
    # 等待所有线程完成
    for thread in threads:
        thread.join()
    
    end_time = time.time()
    
    # 统计结果
    total_operations = num_threads * operations_per_thread
    total_success = sum(results.values())
    success_rate = (total_success / total_operations) * 100
    
    logger.info("=" * 50)
    logger.info(f"📊 测试结果:")
    logger.info(f"  总操作数: {total_operations}")
    logger.info(f"  成功操作: {total_success}")
    logger.info(f"  成功率: {success_rate:.1f}%")
    logger.info(f"  总耗时: {end_time - start_time:.2f}秒")
    logger.info(f"  平均速度: {total_operations / (end_time - start_time):.1f} 操作/秒")
    
    if success_rate >= 99.0:
        logger.info("🎉 并发测试通过！数据库锁定问题已解决")
        return 0
    else:
        logger.error("❌ 并发测试失败，仍存在数据库锁定问题")
        return 1

if __name__ == "__main__":
    exit(main())
'''
    
    try:
        with open("test_database_concurrency.py", "w", encoding="utf-8") as f:
            f.write(test_script)
        
        logger.info("✅ 已创建并发测试脚本: test_database_concurrency.py")
        return True
        
    except Exception as e:
        logger.error(f"❌ 创建并发测试脚本失败: {e}")
        return False

def main():
    """主函数"""
    logger = setup_logging()
    
    logger.info("🔧 LightRek 数据库管理器迁移工具")
    logger.info("解决 'database is locked' 并发访问问题")
    logger.info("=" * 60)
    
    steps = [
        ("备份原始数据库管理器", backup_original_database_manager),
        ("更新数据库管理器", update_database_manager),
        ("测试新数据库管理器", test_new_database_manager),
        ("更新其他文件中的导入", lambda: len(update_imports_in_files()) >= 0),
        ("创建并发测试脚本", create_concurrent_test_script),
    ]
    
    success_count = 0
    
    for step_name, step_func in steps:
        logger.info(f"\n📋 执行步骤: {step_name}")
        
        try:
            if step_func():
                logger.info(f"✅ {step_name} 完成")
                success_count += 1
            else:
                logger.error(f"❌ {step_name} 失败")
        except Exception as e:
            logger.error(f"❌ {step_name} 异常: {e}")
    
    logger.info(f"\n📊 迁移结果: {success_count}/{len(steps)} 步骤成功")
    
    if success_count == len(steps):
        logger.info("🎉 数据库管理器迁移完成！")
        logger.info("")
        logger.info("📋 改进特性:")
        logger.info("  ✅ 连接池 - 支持最多10个并发连接")
        logger.info("  ✅ WAL模式 - 提高并发读写性能")
        logger.info("  ✅ 重试机制 - 自动重试锁定操作")
        logger.info("  ✅ 批量操作 - 减少数据库访问次数")
        logger.info("  ✅ 优化配置 - 提高SQLite性能")
        logger.info("")
        logger.info("🧪 后续步骤:")
        logger.info("1. 运行 python test_database_concurrency.py 测试并发性能")
        logger.info("2. 重启 lightrek.py 应用新的数据库管理器")
        logger.info("3. 运行多个同步任务验证锁定问题已解决")
        
        return 0
    else:
        logger.error("❌ 迁移过程中出现错误，请检查日志")
        return 1

if __name__ == "__main__":
    exit(main())
