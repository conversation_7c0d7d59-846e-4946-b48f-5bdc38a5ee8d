#!/usr/bin/env python3
"""
Web界面Bug测试 - 验证卡片显示修复
测试不同存储类型的卡片是否正确显示相应参数
"""

import requests
import json
import time
from pathlib import Path

class WebInterfaceBugTester:
    """Web界面Bug测试器"""
    
    def __init__(self, base_url: str = "http://localhost:8001"):
        self.base_url = base_url
        
    def setup_test_storages(self):
        """设置测试存储配置"""
        print("🔧 设置测试存储配置...")
        
        # 不同类型的存储配置
        storage_configs = {
            "s3_test": {
                "storage_type": "s3",
                "name": "S3测试存储",
                "description": "AWS S3兼容存储测试",
                "access_key": "AKIAIOSFODNN7EXAMPLE",
                "secret_key": "wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY",
                "endpoint": "https://s3.amazonaws.com",
                "region": "us-east-1",
                "bucket": "test-bucket"
            },
            "sftp_test": {
                "storage_type": "sftp",
                "name": "SFTP测试存储",
                "description": "SFTP服务器连接测试",
                "hostname": "sftp.example.com",
                "port": 22,
                "username": "testuser",
                "password": "testpass",
                "root_path": "/home/<USER>/data"
            },
            "smb_test": {
                "storage_type": "smb",
                "name": "SMB测试存储",
                "description": "Windows网络共享测试",
                "hostname": "smb.example.com",
                "share_name": "shared_folder",
                "username": "domain\\testuser",
                "password": "testpass",
                "root_path": "/documents"
            },
            "ftp_test": {
                "storage_type": "ftp",
                "name": "FTP测试存储",
                "description": "FTP服务器连接测试",
                "hostname": "ftp.example.com",
                "port": 21,
                "username": "ftpuser",
                "password": "ftppass",
                "root_path": "/uploads"
            },
            "local_test": {
                "storage_type": "local",
                "name": "本地测试存储",
                "description": "本地文件系统测试",
                "root_path": str(Path("demo_data/source").absolute())
            }
        }
        
        # 添加数据源
        for storage_id, config in storage_configs.items():
            try:
                response = requests.post(
                    f"{self.base_url}/api/sources",
                    json=config,
                    timeout=10
                )
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get("success"):
                        print(f"✅ 添加 {config['name']} 成功")
                    else:
                        print(f"❌ 添加 {config['name']} 失败: {result.get('message')}")
                else:
                    print(f"❌ 添加 {config['name']} 失败: HTTP {response.status_code}")
                    
            except Exception as e:
                print(f"❌ 添加 {config['name']} 异常: {e}")
                
        # 添加目标存储（复用部分配置）
        target_configs = {
            "local_target": {
                "storage_type": "local",
                "name": "本地目标存储",
                "description": "本地文件系统目标",
                "root_path": str(Path("demo_data/target").absolute())
            }
        }
        
        for storage_id, config in target_configs.items():
            try:
                response = requests.post(
                    f"{self.base_url}/api/targets",
                    json=config,
                    timeout=10
                )
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get("success"):
                        print(f"✅ 添加目标 {config['name']} 成功")
                    else:
                        print(f"❌ 添加目标 {config['name']} 失败: {result.get('message')}")
                        
            except Exception as e:
                print(f"❌ 添加目标 {config['name']} 异常: {e}")
                
    def create_test_task(self):
        """创建测试任务"""
        print("\n📋 创建测试任务...")
        
        # 获取可用的数据源和目标
        try:
            sources_response = requests.get(f"{self.base_url}/api/sources")
            targets_response = requests.get(f"{self.base_url}/api/targets")
            
            if sources_response.status_code == 200 and targets_response.status_code == 200:
                sources = sources_response.json()
                targets = targets_response.json()
                
                if sources and targets:
                    source_id = list(sources.keys())[0]  # 使用第一个数据源
                    target_id = list(targets.keys())[0]  # 使用第一个目标
                    
                    task_config = {
                        "name": "多类型存储测试任务",
                        "description": "测试不同存储类型之间的同步",
                        "source_id": source_id,
                        "target_id": target_id,
                        "sync_mode": "incremental",
                        "schedule_type": "manual",
                        "enabled": True,
                        "prefix": ""
                    }
                    
                    response = requests.post(
                        f"{self.base_url}/api/tasks",
                        json=task_config,
                        timeout=10
                    )
                    
                    if response.status_code == 200:
                        result = response.json()
                        if result.get("success"):
                            print(f"✅ 任务创建成功: {task_config['name']}")
                        else:
                            print(f"❌ 任务创建失败: {result.get('message')}")
                    else:
                        print(f"❌ 任务创建失败: HTTP {response.status_code}")
                else:
                    print("❌ 没有可用的数据源或目标")
            else:
                print("❌ 无法获取数据源或目标列表")
                
        except Exception as e:
            print(f"❌ 创建任务异常: {e}")
            
    def verify_card_display(self):
        """验证卡片显示"""
        print("\n🔍 验证卡片显示修复...")
        
        try:
            # 获取所有配置
            sources_response = requests.get(f"{self.base_url}/api/sources")
            targets_response = requests.get(f"{self.base_url}/api/targets")
            tasks_response = requests.get(f"{self.base_url}/api/tasks")
            
            if all(r.status_code == 200 for r in [sources_response, targets_response, tasks_response]):
                sources = sources_response.json()
                targets = targets_response.json()
                tasks = tasks_response.json()
                
                print(f"📊 数据源数量: {len(sources)}")
                print(f"📊 目标数量: {len(targets)}")
                print(f"📊 任务数量: {len(tasks)}")
                
                # 验证数据源配置
                print("\n📂 数据源配置验证:")
                for source_id, source_config in sources.items():
                    storage_type = source_config.get('storage_type', 's3')
                    name = source_config.get('name', '未命名')
                    print(f"  - {name} ({storage_type})")
                    
                    # 检查必要字段
                    if storage_type == 's3':
                        required_fields = ['endpoint', 'bucket', 'region']
                    elif storage_type == 'sftp':
                        required_fields = ['hostname', 'port', 'username']
                    elif storage_type == 'smb':
                        required_fields = ['hostname', 'share_name', 'username']
                    elif storage_type == 'ftp':
                        required_fields = ['hostname', 'port', 'username']
                    elif storage_type == 'local':
                        required_fields = ['root_path']
                    else:
                        required_fields = []
                        
                    missing_fields = [field for field in required_fields if not source_config.get(field)]
                    if missing_fields:
                        print(f"    ⚠️ 缺少字段: {', '.join(missing_fields)}")
                    else:
                        print(f"    ✅ 配置完整")
                        
                # 验证目标配置
                print("\n🎯 目标配置验证:")
                for target_id, target_config in targets.items():
                    storage_type = target_config.get('storage_type', 's3')
                    name = target_config.get('name', '未命名')
                    print(f"  - {name} ({storage_type})")
                    
                # 验证任务配置
                print("\n📋 任务配置验证:")
                for task_id, task_config in tasks.items():
                    name = task_config.get('name', '未命名')
                    source_id = task_config.get('source_id', '')
                    target_id = task_config.get('target_id', '')
                    sync_mode = task_config.get('sync_mode', 'full')
                    schedule_type = task_config.get('schedule_type', 'manual')
                    
                    print(f"  - {name}")
                    print(f"    源: {source_id}")
                    print(f"    目标: {target_id}")
                    print(f"    模式: {sync_mode}")
                    print(f"    调度: {schedule_type}")
                    
                print("\n✅ 卡片显示验证完成")
                print("💡 请在浏览器中访问 http://localhost:8001 查看实际的卡片显示效果")
                print("💡 检查不同存储类型是否显示了正确的参数信息")
                
            else:
                print("❌ 无法获取配置信息")
                
        except Exception as e:
            print(f"❌ 验证过程异常: {e}")
            
    def run_bug_test(self):
        """运行Bug测试"""
        print("🐛 开始Web界面Bug测试")
        print("=" * 50)
        print("测试目标: 验证任务、来源和目标卡片根据不同类型显示不同参数")
        print("=" * 50)
        
        # 设置测试数据
        self.setup_test_storages()
        
        # 创建测试任务
        self.create_test_task()
        
        # 验证卡片显示
        self.verify_card_display()
        
        print("\n" + "=" * 50)
        print("🎯 测试完成")
        print("=" * 50)
        print("请在浏览器中验证以下内容:")
        print("1. 数据源卡片显示正确的存储类型标签")
        print("2. 不同存储类型显示相应的参数信息:")
        print("   - S3: 端点、存储桶、区域")
        print("   - SFTP: 主机、端口、用户、路径")
        print("   - SMB: 主机、共享、用户、路径")
        print("   - FTP: 主机、端口、用户、路径")
        print("   - 本地: 根路径")
        print("3. 任务卡片显示正确的同步模式和调度类型标签")

def main():
    """主函数"""
    tester = WebInterfaceBugTester()
    
    try:
        tester.run_bug_test()
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
