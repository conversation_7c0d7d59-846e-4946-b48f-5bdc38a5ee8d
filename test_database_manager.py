#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库管理器功能测试
"""

import os
import tempfile
import uuid
from datetime import datetime
from database_manager import DatabaseManager

def test_database_manager():
    """测试数据库管理器功能"""
    print('=== 数据库管理器测试 ===')

    # 创建临时数据库文件
    temp_db = tempfile.NamedTemporaryFile(suffix='.db', delete=False)
    temp_db.close()
    db_path = temp_db.name

    try:
        # 创建数据库管理器
        db_manager = DatabaseManager(db_path)
        print(f'使用临时数据库: {db_path}')

        print('\n--- 测试文件哈希管理 ---')
        # 测试文件哈希记录
        storage_id = 'test_storage'
        file_key = 'test/file.txt'
        file_size = 1024
        file_hash = 'abc123def456'
        last_modified = datetime.now().isoformat()

        # 更新文件哈希
        success = db_manager.update_file_hash(storage_id, file_key, file_size, file_hash, last_modified)
        print(f'更新文件哈希: {success}')

        # 获取文件哈希
        hash_record = db_manager.get_file_hash(storage_id, file_key)
        if hash_record:
            print(f'文件哈希记录: key={hash_record["file_key"]}, size={hash_record["file_size"]}, hash={hash_record["file_hash"]}')
        else:
            print('获取文件哈希失败')

        # 更新同一文件的哈希（测试REPLACE功能）
        new_hash = 'xyz789uvw012'
        new_modified = datetime.now().isoformat()
        success = db_manager.update_file_hash(storage_id, file_key, file_size + 100, new_hash, new_modified)
        print(f'更新文件哈希（第二次）: {success}')

        updated_record = db_manager.get_file_hash(storage_id, file_key)
        if updated_record:
            print(f'更新后哈希记录: size={updated_record["file_size"]}, hash={updated_record["file_hash"]}')

        print('\n--- 测试任务执行记录 ---')
        # 创建任务执行记录
        execution_id = str(uuid.uuid4())
        task_id = str(uuid.uuid4())
        task_name = '测试同步任务'
        source_id = 'test_source'
        target_id = 'test_target'

        success = db_manager.create_task_execution(execution_id, task_id, task_name, source_id, target_id)
        print(f'创建任务执行记录: {success}')

        # 更新任务执行记录
        update_data = {
            'files_total': 100,
            'files_processed': 50,
            'files_skipped': 10,
            'files_failed': 5,
            'bytes_total': 1024000,
            'bytes_transferred': 512000
        }
        success = db_manager.update_task_execution(execution_id, **update_data)
        print(f'更新任务执行记录: {success}')

        # 完成任务执行
        completion_data = {
            'status': 'completed',
            'end_time': datetime.now().isoformat(),
            'files_processed': 95,
            'bytes_transferred': 972800
        }
        success = db_manager.update_task_execution(execution_id, **completion_data)
        print(f'完成任务执行记录: {success}')

        print('\n--- 测试任务日志记录 ---')
        # 添加各种级别的日志
        log_messages = [
            ('INFO', '任务开始执行'),
            ('INFO', '正在扫描源文件...'),
            ('DEBUG', '找到100个文件'),
            ('INFO', '开始文件传输'),
            ('WARNING', '文件 test.txt 传输重试'),
            ('ERROR', '文件 error.txt 传输失败'),
            ('INFO', '任务执行完成')
        ]

        for level, message in log_messages:
            success = db_manager.add_task_log(execution_id, level, message)
            if not success:
                print(f'添加日志失败: {level} - {message}')

        print(f'添加了{len(log_messages)}条任务日志')

        print('\n--- 测试数据查询 ---')
        # 获取任务执行历史
        executions = db_manager.get_task_executions(task_id)
        print(f'任务执行历史数量: {len(executions)}')
        if executions:
            execution = executions[0]
            print(f'执行记录: name={execution["task_name"]}, status={execution["status"]}')
            print(f'  文件统计: total={execution["files_total"]}, processed={execution["files_processed"]}, failed={execution["files_failed"]}')
            print(f'  字节统计: total={execution["bytes_total"]}, transferred={execution["bytes_transferred"]}')

        # 获取最近执行记录
        recent_executions = db_manager.get_recent_executions(limit=5)
        print(f'最近执行记录数量: {len(recent_executions)}')

        # 获取执行日志
        logs = db_manager.get_execution_logs(execution_id)
        print(f'执行日志数量: {len(logs)}')
        if logs:
            print('最近的日志条目:')
            for log in logs[:3]:  # 显示前3条
                print(f'  [{log["level"]}] {log["timestamp"]}: {log["message"]}')

        print('\n--- 测试多任务场景 ---')
        # 创建多个任务执行记录
        task_ids = []
        execution_ids = []
        
        for i in range(3):
            tid = str(uuid.uuid4())
            eid = str(uuid.uuid4())
            task_ids.append(tid)
            execution_ids.append(eid)
            
            success = db_manager.create_task_execution(
                eid, tid, f'批量测试任务{i+1}', 'batch_source', 'batch_target'
            )
            
            # 添加一些日志
            db_manager.add_task_log(eid, 'INFO', f'批量任务{i+1}开始')
            db_manager.add_task_log(eid, 'INFO', f'批量任务{i+1}完成')
            
            # 更新状态
            db_manager.update_task_execution(eid, 
                status='completed',
                files_total=10 * (i + 1),
                files_processed=10 * (i + 1),
                bytes_total=1024 * (i + 1),
                bytes_transferred=1024 * (i + 1)
            )

        print(f'创建了{len(task_ids)}个批量测试任务')

        # 获取所有执行记录
        all_executions = db_manager.get_task_executions(limit=10)
        print(f'总执行记录数量: {len(all_executions)}')

        # 按任务ID查询
        for tid in task_ids[:2]:  # 测试前2个
            task_executions = db_manager.get_task_executions(tid)
            print(f'任务{tid[:8]}...的执行记录: {len(task_executions)}条')

        print('\n--- 测试错误处理 ---')
        # 测试无效的execution_id
        invalid_logs = db_manager.get_execution_logs('invalid_execution_id')
        print(f'无效execution_id的日志数量: {len(invalid_logs)}')

        # 测试获取不存在的文件哈希
        nonexistent_hash = db_manager.get_file_hash('nonexistent_storage', 'nonexistent_file')
        print(f'不存在的文件哈希: {nonexistent_hash is None}')

        print('\n--- 测试数据清理 ---')
        # 注意：这里不实际执行清理，因为我们的测试数据都是新的
        # 在实际应用中，可以调用 db_manager.cleanup_old_data(days=30)
        print('数据清理功能已实现（测试中跳过实际清理）')

        print('\n--- 测试并发安全性 ---')
        import threading
        import time
        
        def concurrent_hash_update(thread_id):
            """并发更新文件哈希的测试函数"""
            for i in range(5):
                storage_id = f'concurrent_storage_{thread_id}'
                file_key = f'file_{i}.txt'
                file_hash = f'hash_{thread_id}_{i}'
                
                success = db_manager.update_file_hash(
                    storage_id, file_key, 1024 + i, file_hash, datetime.now().isoformat()
                )
                time.sleep(0.01)  # 短暂延迟
        
        # 创建多个线程并发更新
        threads = []
        for i in range(3):
            thread = threading.Thread(target=concurrent_hash_update, args=(i,))
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        print('并发安全性测试完成')

        print('\n--- 测试数据统计 ---')
        # 简单的数据统计查询
        import sqlite3
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            # 统计文件哈希记录数
            cursor.execute('SELECT COUNT(*) FROM file_hashes')
            hash_count = cursor.fetchone()[0]
            print(f'文件哈希记录总数: {hash_count}')
            
            # 统计任务执行记录数
            cursor.execute('SELECT COUNT(*) FROM task_executions')
            execution_count = cursor.fetchone()[0]
            print(f'任务执行记录总数: {execution_count}')
            
            # 统计任务日志记录数
            cursor.execute('SELECT COUNT(*) FROM task_logs')
            log_count = cursor.fetchone()[0]
            print(f'任务日志记录总数: {log_count}')
            
            # 按日志级别统计
            cursor.execute('SELECT level, COUNT(*) FROM task_logs GROUP BY level')
            level_stats = cursor.fetchall()
            print('日志级别统计:')
            for level, count in level_stats:
                print(f'  {level}: {count}条')

    finally:
        # 清理临时数据库文件
        try:
            if os.path.exists(db_path):
                # 确保数据库连接已关闭
                del db_manager
                import time
                time.sleep(0.1)  # 短暂等待
                os.unlink(db_path)
                print(f'\n清理临时数据库文件: {db_path}')
        except Exception as e:
            print(f'\n清理临时数据库文件失败: {e}（这在Windows上很常见，不影响测试结果）')

    print('数据库管理器测试完成')

if __name__ == '__main__':
    test_database_manager()
