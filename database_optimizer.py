#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库优化器 - 定期清理和优化LightRek数据库
"""

import sqlite3
import os
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, Optional
import threading
import time

class DatabaseOptimizer:
    """数据库优化器"""
    
    def __init__(self, db_path: str = "lightrek_data.db"):
        self.db_path = db_path
        self.logger = logging.getLogger(__name__)
        self._stop_event = threading.Event()
        self._optimization_thread = None
        
    def get_database_stats(self) -> Dict[str, Any]:
        """获取数据库统计信息"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                stats = {}
                
                # 数据库文件大小
                if os.path.exists(self.db_path):
                    file_size = os.path.getsize(self.db_path)
                    stats['file_size_mb'] = round(file_size / (1024 * 1024), 2)
                
                # 各表记录数
                tables = ['file_hashes', 'task_executions', 'task_logs']
                for table in tables:
                    try:
                        cursor.execute(f"SELECT COUNT(*) FROM {table}")
                        count = cursor.fetchone()[0]
                        stats[f'{table}_count'] = count
                    except sqlite3.OperationalError:
                        stats[f'{table}_count'] = 0
                
                # file_hashes表详细统计
                try:
                    cursor.execute("""
                        SELECT 
                            storage_id,
                            COUNT(*) as count,
                            MIN(created_at) as oldest,
                            MAX(updated_at) as newest
                        FROM file_hashes 
                        GROUP BY storage_id 
                        ORDER BY count DESC
                    """)
                    file_hash_details = cursor.fetchall()
                    stats['file_hash_by_storage'] = [
                        {
                            'storage_id': row[0],
                            'count': row[1],
                            'oldest': row[2],
                            'newest': row[3]
                        }
                        for row in file_hash_details
                    ]
                except sqlite3.OperationalError:
                    stats['file_hash_by_storage'] = []
                
                # 数据库页面统计
                cursor.execute("PRAGMA page_count")
                page_count = cursor.fetchone()[0]
                cursor.execute("PRAGMA page_size")
                page_size = cursor.fetchone()[0]
                stats['total_pages'] = page_count
                stats['page_size'] = page_size
                stats['theoretical_size_mb'] = round((page_count * page_size) / (1024 * 1024), 2)
                
                return stats
                
        except Exception as e:
            self.logger.error(f"获取数据库统计失败: {e}")
            return {}
    
    def clean_old_file_hashes(self, days: int = 7) -> int:
        """清理旧的文件哈希记录"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 计算截止日期
                cutoff_date = (datetime.now() - timedelta(days=days)).isoformat()
                
                # 统计要删除的记录数
                cursor.execute("""
                    SELECT COUNT(*) FROM file_hashes 
                    WHERE updated_at < ?
                """, (cutoff_date,))
                count_to_delete = cursor.fetchone()[0]
                
                # 删除旧记录
                cursor.execute("""
                    DELETE FROM file_hashes 
                    WHERE updated_at < ?
                """, (cutoff_date,))
                
                conn.commit()
                
                self.logger.info(f"清理了 {count_to_delete} 条旧文件哈希记录（{days}天前）")
                return count_to_delete
                
        except Exception as e:
            self.logger.error(f"清理旧文件哈希失败: {e}")
            return 0
    
    def clean_duplicate_file_hashes(self) -> int:
        """清理重复的文件哈希记录（保留最新的）"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 查找重复记录
                cursor.execute("""
                    DELETE FROM file_hashes 
                    WHERE id NOT IN (
                        SELECT MAX(id) 
                        FROM file_hashes 
                        GROUP BY storage_id, file_key
                    )
                """)
                
                deleted_count = cursor.rowcount
                conn.commit()
                
                self.logger.info(f"清理了 {deleted_count} 条重复文件哈希记录")
                return deleted_count
                
        except Exception as e:
            self.logger.error(f"清理重复文件哈希失败: {e}")
            return 0
    
    def clean_orphaned_logs(self) -> int:
        """清理孤立的日志记录"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 清理没有对应执行记录的日志
                cursor.execute("""
                    DELETE FROM task_logs 
                    WHERE execution_id NOT IN (
                        SELECT id FROM task_executions
                    )
                """)
                
                deleted_count = cursor.rowcount
                conn.commit()
                
                self.logger.info(f"清理了 {deleted_count} 条孤立日志记录")
                return deleted_count
                
        except Exception as e:
            self.logger.error(f"清理孤立日志失败: {e}")
            return 0
    
    def clean_old_executions(self, days: int = 30) -> int:
        """清理旧的任务执行记录"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cutoff_date = (datetime.now() - timedelta(days=days)).isoformat()
                
                # 先清理对应的日志
                cursor.execute("""
                    DELETE FROM task_logs 
                    WHERE execution_id IN (
                        SELECT id FROM task_executions 
                        WHERE start_time < ?
                    )
                """, (cutoff_date,))
                logs_deleted = cursor.rowcount
                
                # 再清理执行记录
                cursor.execute("""
                    DELETE FROM task_executions 
                    WHERE start_time < ?
                """, (cutoff_date,))
                executions_deleted = cursor.rowcount
                
                conn.commit()
                
                self.logger.info(f"清理了 {executions_deleted} 条旧执行记录和 {logs_deleted} 条相关日志（{days}天前）")
                return executions_deleted + logs_deleted
                
        except Exception as e:
            self.logger.error(f"清理旧执行记录失败: {e}")
            return 0
    
    def vacuum_database(self) -> bool:
        """压缩数据库，回收空间"""
        try:
            # 获取压缩前的大小
            size_before = os.path.getsize(self.db_path) if os.path.exists(self.db_path) else 0
            
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("VACUUM")
                conn.commit()
            
            # 获取压缩后的大小
            size_after = os.path.getsize(self.db_path) if os.path.exists(self.db_path) else 0
            
            saved_mb = round((size_before - size_after) / (1024 * 1024), 2)
            self.logger.info(f"数据库压缩完成，节省空间: {saved_mb} MB")
            
            return True
            
        except Exception as e:
            self.logger.error(f"数据库压缩失败: {e}")
            return False
    
    def optimize_database(self, 
                         clean_file_hashes_days: int = 7,
                         clean_executions_days: int = 30,
                         vacuum: bool = True) -> Dict[str, Any]:
        """完整的数据库优化"""
        
        self.logger.info("开始数据库优化...")
        
        # 获取优化前的统计
        stats_before = self.get_database_stats()
        
        results = {
            'start_time': datetime.now().isoformat(),
            'stats_before': stats_before,
            'operations': {}
        }
        
        # 1. 清理重复文件哈希
        results['operations']['duplicate_hashes_cleaned'] = self.clean_duplicate_file_hashes()
        
        # 2. 清理旧文件哈希
        results['operations']['old_hashes_cleaned'] = self.clean_old_file_hashes(clean_file_hashes_days)
        
        # 3. 清理孤立日志
        results['operations']['orphaned_logs_cleaned'] = self.clean_orphaned_logs()
        
        # 4. 清理旧执行记录
        results['operations']['old_executions_cleaned'] = self.clean_old_executions(clean_executions_days)
        
        # 5. 压缩数据库
        if vacuum:
            results['operations']['vacuum_success'] = self.vacuum_database()
        
        # 获取优化后的统计
        stats_after = self.get_database_stats()
        results['stats_after'] = stats_after
        results['end_time'] = datetime.now().isoformat()
        
        # 计算节省的空间
        if 'file_size_mb' in stats_before and 'file_size_mb' in stats_after:
            saved_mb = stats_before['file_size_mb'] - stats_after['file_size_mb']
            results['space_saved_mb'] = round(saved_mb, 2)
        
        self.logger.info(f"数据库优化完成，节省空间: {results.get('space_saved_mb', 0)} MB")
        
        return results
    
    def start_auto_optimization(self, interval_hours: int = 24):
        """启动自动优化（每24小时执行一次）"""
        if self._optimization_thread and self._optimization_thread.is_alive():
            self.logger.warning("自动优化已在运行")
            return
        
        self._stop_event.clear()
        self._optimization_thread = threading.Thread(
            target=self._auto_optimization_loop,
            args=(interval_hours,),
            daemon=True
        )
        self._optimization_thread.start()
        
        self.logger.info(f"启动自动数据库优化，间隔: {interval_hours} 小时")
    
    def stop_auto_optimization(self):
        """停止自动优化"""
        if self._optimization_thread:
            self._stop_event.set()
            self._optimization_thread.join(timeout=5)
            self.logger.info("自动数据库优化已停止")
    
    def _auto_optimization_loop(self, interval_hours: int):
        """自动优化循环"""
        while not self._stop_event.is_set():
            try:
                # 执行优化
                self.optimize_database()
                
                # 等待下次执行
                for _ in range(interval_hours * 3600):  # 转换为秒
                    if self._stop_event.is_set():
                        break
                    time.sleep(1)
                    
            except Exception as e:
                self.logger.error(f"自动优化过程中出错: {e}")
                # 出错后等待1小时再重试
                for _ in range(3600):
                    if self._stop_event.is_set():
                        break
                    time.sleep(1)


def main():
    """命令行工具"""
    import argparse
    
    parser = argparse.ArgumentParser(description="LightRek 数据库优化工具")
    parser.add_argument("--db", default="lightrek_data.db", help="数据库文件路径")
    parser.add_argument("--stats", action="store_true", help="显示数据库统计信息")
    parser.add_argument("--optimize", action="store_true", help="执行完整优化")
    parser.add_argument("--clean-hashes", type=int, metavar="DAYS", help="清理N天前的文件哈希")
    parser.add_argument("--clean-executions", type=int, metavar="DAYS", help="清理N天前的执行记录")
    parser.add_argument("--vacuum", action="store_true", help="压缩数据库")
    
    args = parser.parse_args()
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    optimizer = DatabaseOptimizer(args.db)
    
    if args.stats:
        stats = optimizer.get_database_stats()
        print("\n📊 数据库统计信息:")
        print(f"文件大小: {stats.get('file_size_mb', 0)} MB")
        print(f"文件哈希记录: {stats.get('file_hashes_count', 0):,}")
        print(f"任务执行记录: {stats.get('task_executions_count', 0):,}")
        print(f"任务日志记录: {stats.get('task_logs_count', 0):,}")
        
        if 'file_hash_by_storage' in stats:
            print("\n📁 按存储分组的文件哈希:")
            for item in stats['file_hash_by_storage']:
                print(f"  {item['storage_id']}: {item['count']:,} 条记录")
    
    if args.optimize:
        results = optimizer.optimize_database()
        print(f"\n✅ 优化完成，节省空间: {results.get('space_saved_mb', 0)} MB")
    
    if args.clean_hashes:
        count = optimizer.clean_old_file_hashes(args.clean_hashes)
        print(f"✅ 清理了 {count} 条旧文件哈希记录")
    
    if args.clean_executions:
        count = optimizer.clean_old_executions(args.clean_executions)
        print(f"✅ 清理了 {count} 条旧执行记录")
    
    if args.vacuum:
        success = optimizer.vacuum_database()
        print(f"✅ 数据库压缩{'成功' if success else '失败'}")


if __name__ == "__main__":
    main()
