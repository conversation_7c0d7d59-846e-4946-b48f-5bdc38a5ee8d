#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试任务创建和运行修复
"""

import requests
import json
import time

def test_task_creation_fix():
    """测试任务创建和运行修复"""
    base_url = 'http://localhost:8000'
    
    print('🔧 测试任务创建和运行修复')
    print('=' * 50)
    
    # 1. 检查服务器连接
    print('\n1️⃣ 检查服务器连接')
    try:
        response = requests.get(f'{base_url}/')
        if response.status_code == 200:
            print('✅ 服务器连接正常')
        else:
            print(f'❌ 服务器连接失败: {response.status_code}')
            return False
    except Exception as e:
        print(f'❌ 连接异常: {e}')
        print('请确保 lightrek.py 正在运行: python lightrek.py --port 8000')
        return False
    
    # 2. 获取现有任务并测试运行
    print('\n2️⃣ 获取现有任务')
    try:
        response = requests.get(f'{base_url}/api/tasks')
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                tasks = data.get('tasks', {})
                print(f'✅ 获取任务列表成功 ({len(tasks)} 个任务)')
                
                if tasks:
                    # 测试运行第一个任务
                    first_task_id = list(tasks.keys())[0]
                    first_task = tasks[first_task_id]
                    print(f'📋 测试任务: {first_task.get("name", "未命名")} (ID: {first_task_id[:8]}...)')
                    
                    print('\n   🚀 尝试运行任务')
                    try:
                        response = requests.post(f'{base_url}/api/tasks/{first_task_id}/run',
                                               headers={'Content-Type': 'application/json'})
                        
                        if response.status_code == 200:
                            result = response.json()
                            if result.get('success'):
                                print(f'   ✅ 任务启动成功: {result.get("message")}')
                                return True
                            else:
                                print(f'   ❌ 任务启动失败: {result.get("message")}')
                                return False
                        else:
                            print(f'   ❌ API请求失败: {response.status_code}')
                            return False
                    except Exception as e:
                        print(f'   ❌ 运行任务异常: {e}')
                        return False
                else:
                    print('   ℹ️ 没有现有任务，将创建新任务进行测试')
                    return test_create_new_task(base_url)
            else:
                print(f'❌ 获取任务列表失败: {data.get("message")}')
                return False
        else:
            print(f'❌ 任务列表API失败: {response.status_code}')
            return False
    except Exception as e:
        print(f'❌ 获取任务列表异常: {e}')
        return False

def test_create_new_task(base_url):
    """创建新任务并测试"""
    print('\n3️⃣ 创建新任务并测试')
    
    # 创建测试数据源
    print('\n   📁 创建测试数据源')
    test_source = {
        'name': '测试数据源',
        'type': 'local',
        'path': 'C:\\temp\\source_test'
    }
    
    try:
        response = requests.post(f'{base_url}/api/sources', 
                               json=test_source,
                               headers={'Content-Type': 'application/json'})
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                source_id = data.get('source_id')
                print(f'   ✅ 数据源创建成功: {source_id[:8]}...')
            else:
                print(f'   ❌ 数据源创建失败: {data.get("message")}')
                return False
        else:
            print(f'   ❌ 数据源API失败: {response.status_code}')
            return False
    except Exception as e:
        print(f'   ❌ 数据源创建异常: {e}')
        return False
    
    # 创建测试目标存储
    print('\n   🎯 创建测试目标存储')
    test_target = {
        'name': '测试目标存储',
        'type': 'local',
        'path': 'C:\\temp\\target_test'
    }
    
    try:
        response = requests.post(f'{base_url}/api/targets', 
                               json=test_target,
                               headers={'Content-Type': 'application/json'})
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                target_id = data.get('target_id')
                print(f'   ✅ 目标存储创建成功: {target_id[:8]}...')
            else:
                print(f'   ❌ 目标存储创建失败: {data.get("message")}')
                return False
        else:
            print(f'   ❌ 目标存储API失败: {response.status_code}')
            return False
    except Exception as e:
        print(f'   ❌ 目标存储创建异常: {e}')
        return False
    
    # 创建测试任务
    print('\n   📋 创建测试任务')
    test_task = {
        'name': '修复测试任务',
        'description': '用于测试任务创建和运行修复的任务',
        'source_id': source_id,
        'target_id': target_id,
        'sync_mode': 'incremental',
        'max_workers': 2,
        'retry_times': 1
    }
    
    try:
        response = requests.post(f'{base_url}/api/tasks', 
                               json=test_task,
                               headers={'Content-Type': 'application/json'})
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                task_id = data.get('task_id')
                print(f'   ✅ 任务创建成功: {task_id[:8]}...')
            else:
                print(f'   ❌ 任务创建失败: {data.get("message")}')
                return False
        else:
            print(f'   ❌ 任务创建API失败: {response.status_code}')
            return False
    except Exception as e:
        print(f'   ❌ 任务创建异常: {e}')
        return False
    
    # 运行测试任务
    print('\n   🚀 运行测试任务')
    try:
        response = requests.post(f'{base_url}/api/tasks/{task_id}/run',
                               headers={'Content-Type': 'application/json'})
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f'   ✅ 任务启动成功: {result.get("message")}')
                return True
            else:
                print(f'   ❌ 任务启动失败: {result.get("message")}')
                return False
        else:
            print(f'   ❌ 任务启动API失败: {response.status_code}')
            return False
    except Exception as e:
        print(f'   ❌ 任务启动异常: {e}')
        return False

def test_task_status():
    """测试任务状态查询"""
    base_url = 'http://localhost:8000'
    
    print('\n4️⃣ 测试任务状态查询')
    try:
        response = requests.get(f'{base_url}/api/task-status')
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                status = data.get('status', {})
                print(f'✅ 任务状态查询成功 ({len(status)} 个任务状态)')
                
                if status:
                    for task_id, task_status in list(status.items())[:3]:  # 显示前3个
                        print(f'   📊 任务 {task_id[:8]}...: {task_status.get("status", "未知")}')
                else:
                    print('   ℹ️ 当前没有运行中的任务')
                return True
            else:
                print(f'❌ 任务状态查询失败: {data.get("message")}')
                return False
        else:
            print(f'❌ 任务状态API失败: {response.status_code}')
            return False
    except Exception as e:
        print(f'❌ 任务状态查询异常: {e}')
        return False

def main():
    """主函数"""
    print('🧪 LightRek 任务创建和运行修复测试')
    print('=' * 60)
    print('测试目标: 修复 UnifiedSyncTask 创建问题')
    print('=' * 60)
    
    success_count = 0
    total_tests = 3
    
    # 测试1: 任务创建和运行
    if test_task_creation_fix():
        success_count += 1
    
    # 测试2: 任务状态查询
    if test_task_status():
        success_count += 1
    
    # 等待一下再检查状态
    print('\n⏳ 等待2秒后再次检查任务状态...')
    time.sleep(2)
    
    # 测试3: 再次检查任务状态
    if test_task_status():
        success_count += 1
    
    # 总结
    print('\n' + '=' * 60)
    print(f'📊 测试结果: {success_count}/{total_tests} 通过')
    
    if success_count == total_tests:
        print('🎉 任务创建和运行修复成功!')
        print('✅ UnifiedSyncTask 对象创建正常')
        print('✅ 任务启动功能正常')
        print('✅ 任务状态查询正常')
    elif success_count > 0:
        print('⚠️ 部分功能正常，部分需要进一步检查')
    else:
        print('❌ 修复未完全成功，需要进一步调试')
    
    print('\n💡 如果仍有问题，请查看服务器控制台输出获取详细错误信息')

if __name__ == '__main__':
    main()
