#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模拟生产环境的SMB同步测试
"""

import smb_storage_adapter
from storage_abstraction import StorageFactory, SMBStorageConfig, StorageType

def simulate_sync_task():
    """模拟同步任务的完整流程"""
    print("🧪 模拟生产环境的SMB同步任务")
    
    config = SMBStorageConfig(
        storage_type=StorageType.SMB,
        hostname='Jay<PERSON>',
        port=445,
        username='smb',
        password='smbsmb',
        domain='WORKGROUP',
        share_name='hlmj',
        root_path=''
    )
    
    # 创建源适配器（模拟同步任务管理器的行为）
    source_adapter = StorageFactory.create_adapter(config)
    
    print("📋 模拟同步任务流程:")
    
    # 1. 扫描源文件（这会调用 list_files）
    print("1. 扫描源文件...")
    try:
        source_files = []
        result = source_adapter.list_files("", max_keys=10)
        source_files.extend(result.files)
        print(f"   ✅ 成功: 找到 {len(source_files)} 个文件")
        
        if len(source_files) < 3:
            print("   ❌ 文件数量不足")
            return False
            
    except Exception as e:
        print(f"   ❌ 失败: {e}")
        return False
    
    # 2. 模拟同步过程：逐个下载文件
    print("\n2. 开始同步文件...")
    success_count = 0
    total_files = min(5, len(source_files))  # 只测试前5个文件
    
    for i, file_meta in enumerate(source_files[:total_files]):
        print(f"   同步 {i+1}/{total_files}: {file_meta.key}")
        
        try:
            # 模拟同步任务管理器的调用
            file_data = source_adapter.get_file(file_meta.key)
            
            if file_data is None:
                print(f"     ❌ 下载失败: 返回None")
            else:
                print(f"     ✅ 下载成功: {len(file_data)} bytes")
                success_count += 1
                
                # 验证数据完整性
                if len(file_data) == file_meta.size:
                    print(f"     ✅ 大小匹配: {len(file_data)} == {file_meta.size}")
                else:
                    print(f"     ⚠️ 大小不匹配: {len(file_data)} != {file_meta.size}")
                
        except Exception as e:
            print(f"     ❌ 下载异常: {e}")
    
    print(f"\n📊 同步结果: {success_count}/{total_files} 成功")
    
    # 3. 再次扫描验证连接状态
    print("\n3. 验证连接状态...")
    try:
        result2 = source_adapter.list_files("", max_keys=3)
        print(f"   ✅ 成功: 找到 {len(result2.files)} 个文件")
    except Exception as e:
        print(f"   ❌ 失败: {e}")
        return False
    
    # 评估结果
    success_rate = success_count / total_files
    
    print(f"\n🎯 总体结果:")
    print(f"   成功率: {success_count}/{total_files} ({success_rate:.1%})")
    
    if success_rate >= 0.8:
        print("   ✅ 生产环境模拟测试通过")
        return True
    else:
        print("   ❌ 生产环境模拟测试失败")
        return False

def simulate_concurrent_sync():
    """模拟并发同步任务"""
    print("\n🧪 模拟并发同步任务")
    
    config = SMBStorageConfig(
        storage_type=StorageType.SMB,
        hostname='Jayce',
        port=445,
        username='smb',
        password='smbsmb',
        domain='WORKGROUP',
        share_name='hlmj',
        root_path=''
    )
    
    import threading
    import time
    
    # 测试文件
    test_files = ['398.xml', '398_MonitorData.ini', 'BugCrashReporter.exe']
    results = {}
    
    def sync_worker(filename, thread_id):
        """同步工作线程"""
        print(f"   线程{thread_id} 开始同步: {filename}")
        try:
            # 每个线程创建独立的适配器（模拟多任务并发）
            adapter = StorageFactory.create_adapter(config)
            
            # 先扫描（模拟任务开始时的扫描）
            result = adapter.list_files("", max_keys=5)
            
            # 然后下载
            data = adapter.get_file(filename)
            
            if data:
                results[thread_id] = {'success': True, 'size': len(data), 'file': filename}
                print(f"   线程{thread_id} ✅ 成功: {len(data)} bytes")
            else:
                results[thread_id] = {'success': False, 'error': 'None returned', 'file': filename}
                print(f"   线程{thread_id} ❌ 失败: 返回None")
                
        except Exception as e:
            results[thread_id] = {'success': False, 'error': str(e), 'file': filename}
            print(f"   线程{thread_id} ❌ 异常: {e}")
    
    # 启动并发同步
    threads = []
    start_time = time.time()
    
    for i, filename in enumerate(test_files):
        thread = threading.Thread(target=sync_worker, args=(filename, i+1))
        threads.append(thread)
        thread.start()
    
    # 等待所有线程完成
    for thread in threads:
        thread.join()
    
    end_time = time.time()
    
    # 分析结果
    successful = sum(1 for r in results.values() if r['success'])
    print(f"\n📊 并发同步结果:")
    print(f"   成功率: {successful}/{len(test_files)}")
    print(f"   总耗时: {end_time - start_time:.2f}s")
    
    return successful >= len(test_files) * 0.8

def main():
    """主函数"""
    print("🧪 生产环境SMB同步模拟测试")
    print("=" * 60)
    
    tests = [
        ("生产环境同步模拟", simulate_sync_task),
        ("并发同步模拟", simulate_concurrent_sync),
    ]
    
    passed_tests = 0
    
    for test_name, test_func in tests:
        print(f"\n📋 执行测试: {test_name}")
        
        try:
            if test_func():
                print(f"✅ {test_name} 通过")
                passed_tests += 1
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
            import traceback
            traceback.print_exc()
    
    print(f"\n📊 测试结果: {passed_tests}/{len(tests)} 通过")
    
    if passed_tests >= len(tests):
        print("🎉 生产环境SMB同步模拟测试完全通过！")
        print("")
        print("🚀 现在可以重新运行同步任务，应该能够:")
        print("  ✅ 成功扫描源文件")
        print("  ✅ 稳定下载所有文件")
        print("  ✅ 避免连接状态污染")
        print("  ✅ 消除 STATUS_OBJECT_NAME_INVALID 错误")
        print("  ✅ 支持并发同步任务")
        
        return 0
    else:
        print("❌ 生产环境SMB同步模拟测试仍有问题")
        return 1

if __name__ == "__main__":
    exit(main())
