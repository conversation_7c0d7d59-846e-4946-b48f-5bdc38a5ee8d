#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动增强版Web服务器
包含完整的同步任务和性能优化功能
"""

import sys
import time
import signal
from web_server import WebServer

def signal_handler(sig, frame):
    """信号处理器"""
    print('\n🛑 收到停止信号，正在关闭服务器...')
    sys.exit(0)

def main():
    """主函数"""
    print('🚀 LightRek 增强版Web服务器')
    print('=' * 50)
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    
    try:
        # 导入管理器类
        from unified_config_manager import UnifiedConfigManager
        from unified_task_manager import UnifiedTaskManager
        from database_manager import DatabaseManager
        
        print('⚙️ 初始化管理器...')
        
        # 创建管理器实例
        config_manager = UnifiedConfigManager()
        task_manager = UnifiedTaskManager(config_manager)
        
        # 数据库管理器是可选的
        try:
            db_manager = DatabaseManager()
            print('✅ 数据库管理器初始化成功')
        except Exception as e:
            print(f'⚠️ 数据库管理器初始化失败: {e}')
            db_manager = None
        
        # 创建增强版Web服务器
        port = 8009  # 使用新端口避免冲突
        web_server = WebServer(config_manager, task_manager, db_manager, port)
        
        print(f'🌐 启动增强版Web服务器 (端口: {port})...')
        
        # 启动增强版服务器
        if web_server.start_enhanced():
            print(f'✅ 增强版Web服务器启动成功!')
            print(f'🌐 访问地址: http://localhost:{port}')
            print('')
            print('📋 可用的API端点:')
            print('   GET  /api/statistics        - 获取统计信息')
            print('   GET  /api/sources           - 获取数据源列表')
            print('   POST /api/sources           - 添加数据源')
            print('   GET  /api/targets           - 获取目标存储列表')
            print('   POST /api/targets           - 添加目标存储')
            print('   GET  /api/tasks             - 获取任务列表')
            print('   POST /api/tasks             - 创建同步任务')
            print('   POST /api/tasks/{id}/run    - 运行任务')
            print('   POST /api/tasks/{id}/stop   - 停止任务')
            print('   GET  /api/task-status       - 获取任务状态')
            print('   GET  /api/task-executions   - 获取执行记录')
            print('   GET  /api/optimization-config - 获取优化配置')
            print('   POST /api/optimization-config - 保存优化配置')
            print('   POST /api/test-connection   - 测试连接')
            print('')
            print('🔧 增强功能:')
            print('   ✅ 完整的同步任务管理')
            print('   ✅ 性能优化配置')
            print('   ✅ 实时任务状态监控')
            print('   ✅ 执行记录和日志')
            print('   ✅ 连接测试功能')
            print('   ✅ 统计信息仪表盘')
            print('')
            print('按 Ctrl+C 停止服务器')
            
            # 保持服务器运行
            try:
                while web_server.is_running():
                    time.sleep(1)
            except KeyboardInterrupt:
                print('\n🛑 收到停止信号')
                web_server.stop()
                print('✅ 服务器已停止')
        else:
            print('❌ 增强版Web服务器启动失败')
            return 1
            
    except ImportError as e:
        print(f'❌ 导入模块失败: {e}')
        print('请确保所有依赖模块都已正确安装')
        print('需要的模块:')
        print('  - unified_config_manager.py')
        print('  - unified_task_manager.py')
        print('  - database_manager.py (可选)')
        return 1
    except Exception as e:
        print(f'❌ 启动过程中出错: {e}')
        return 1
    
    return 0

if __name__ == '__main__':
    sys.exit(main())
