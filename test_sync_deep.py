#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
同步功能深度测试
"""

import os
import tempfile
import shutil
from datetime import datetime
from storage_abstraction import LocalStorageConfig, FileMetadata
from local_storage_adapter import LocalStorageAdapter
from efficient_sync_engine import EfficientSyncEngine, SyncProgress
from compressed_transfer import CompressedTransferEngine, CompressionConfig

def create_test_files(directory, file_count=10, size_range=(1024, 10*1024)):
    """创建测试文件"""
    os.makedirs(directory, exist_ok=True)
    created_files = []
    
    for i in range(file_count):
        filename = f"test_file_{i:03d}.txt"
        filepath = os.path.join(directory, filename)
        
        # 创建不同大小的文件
        import random
        size = random.randint(*size_range)
        content = f"Test file {i}\n" + "x" * (size - len(f"Test file {i}\n"))
        
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(content)
        
        created_files.append(filename)
    
    return created_files

def test_sync_functionality():
    """测试同步功能"""
    print('=== 同步功能深度测试 ===')
    
    # 创建临时目录
    temp_base = tempfile.mkdtemp(prefix="lightrek_sync_test_")
    source_dir = os.path.join(temp_base, "source")
    target_dir = os.path.join(temp_base, "target")
    
    try:
        print(f'测试目录: {temp_base}')
        
        print('\n--- 准备测试数据 ---')
        # 创建测试文件
        small_files = create_test_files(os.path.join(source_dir, "small"), 20, (100, 1024))  # 小文件
        medium_files = create_test_files(os.path.join(source_dir, "medium"), 10, (10*1024, 100*1024))  # 中等文件
        large_files = create_test_files(os.path.join(source_dir, "large"), 3, (1024*1024, 5*1024*1024))  # 大文件
        
        print(f'创建测试文件: 小文件 {len(small_files)}, 中等文件 {len(medium_files)}, 大文件 {len(large_files)}')
        
        # 创建目标目录
        os.makedirs(target_dir, exist_ok=True)

        # 创建存储适配器
        source_config = LocalStorageConfig(
            name='测试源存储',
            description='同步测试源',
            root_path=source_dir
        )
        source_adapter = LocalStorageAdapter(source_config)

        target_config = LocalStorageConfig(
            name='测试目标存储',
            description='同步测试目标',
            root_path=target_dir
        )
        target_adapter = LocalStorageAdapter(target_config)
        
        print('\n--- 测试存储适配器连接 ---')
        source_ok, source_msg = source_adapter.test_connection()
        target_ok, target_msg = target_adapter.test_connection()
        print(f'源存储连接: {source_ok}, {source_msg}')
        print(f'目标存储连接: {target_ok}, {target_msg}')
        
        print('\n--- 测试文件列表功能 ---')
        # 列出源文件
        source_result = source_adapter.list_files()
        print(f'源文件数量: {len(source_result.files)}')
        
        # 显示文件分类统计
        small_count = sum(1 for f in source_result.files if f.size < 10*1024)
        medium_count = sum(1 for f in source_result.files if 10*1024 <= f.size < 1024*1024)
        large_count = sum(1 for f in source_result.files if f.size >= 1024*1024)
        print(f'文件分类: 小文件 {small_count}, 中等文件 {medium_count}, 大文件 {large_count}')
        
        print('\n--- 测试高效同步引擎 ---')
        sync_engine = EfficientSyncEngine(max_workers=4, chunk_size=1024*1024)
        
        # 准备同步文件列表
        files_to_sync = source_result.files
        
        # 进度回调函数
        def progress_callback(progress: SyncProgress):
            percent = (progress.completed_files / progress.total_files) * 100 if progress.total_files > 0 else 0
            print(f'同步进度: {progress.completed_files}/{progress.total_files} ({percent:.1f}%) - 当前: {progress.current_file}')
        
        # 执行同步
        print(f'开始同步 {len(files_to_sync)} 个文件...')
        sync_result = sync_engine.sync_files(
            source_adapter, target_adapter, files_to_sync,
            'test_task_id', 'test_execution_id', progress_callback
        )
        
        print(f'同步结果:')
        print(f'  总文件: {sync_result.total_files}')
        print(f'  成功: {sync_result.completed_files}')
        print(f'  失败: {sync_result.failed_files}')
        print(f'  总字节: {sync_result.total_bytes:,}')
        print(f'  传输字节: {sync_result.transferred_bytes:,}')
        print(f'  速度: {sync_result.speed_mbps:.2f} MB/s')
        
        print('\n--- 验证同步结果 ---')
        # 检查目标文件
        target_result = target_adapter.list_files()
        print(f'目标文件数量: {len(target_result.files)}')
        
        # 验证文件完整性
        verification_success = 0
        verification_failed = 0
        
        for source_file in source_result.files:
            source_data = source_adapter.get_file(source_file.key)
            target_data = target_adapter.get_file(source_file.key)
            
            if source_data and target_data and source_data == target_data:
                verification_success += 1
            else:
                verification_failed += 1
                print(f'文件验证失败: {source_file.key}')
        
        print(f'文件完整性验证: 成功 {verification_success}, 失败 {verification_failed}')
        
        print('\n--- 测试增量同步 ---')
        # 修改一些源文件
        modified_files = []
        for i in range(3):
            filename = f"small/test_file_{i:03d}.txt"
            filepath = os.path.join(source_dir, filename)
            if os.path.exists(filepath):
                with open(filepath, 'a', encoding='utf-8') as f:
                    f.write(f"\nModified at {datetime.now()}")
                modified_files.append(filename)
        
        print(f'修改了 {len(modified_files)} 个文件')
        
        # 重新扫描源文件
        source_result_updated = source_adapter.list_files()
        
        # 模拟增量同步逻辑（比较文件修改时间）
        files_to_sync_incremental = []
        target_file_map = {f.key: f for f in target_result.files}
        
        for source_file in source_result_updated.files:
            target_file = target_file_map.get(source_file.key)
            if not target_file or source_file.last_modified > target_file.last_modified:
                files_to_sync_incremental.append(source_file)
        
        print(f'增量同步需要处理 {len(files_to_sync_incremental)} 个文件')
        
        if files_to_sync_incremental:
            incremental_result = sync_engine.sync_files(
                source_adapter, target_adapter, files_to_sync_incremental,
                'incremental_task_id', 'incremental_execution_id'
            )
            print(f'增量同步结果: 成功 {incremental_result.completed_files}, 失败 {incremental_result.failed_files}')
        
        print('\n--- 测试压缩传输功能 ---')
        # 创建压缩传输引擎
        compression_engine = CompressedTransferEngine()
        
        # 测试是否应该使用压缩
        should_compress = compression_engine.should_use_compression(source_adapter, len(source_result.files))
        print(f'是否建议使用压缩传输: {should_compress}')
        
        # 创建压缩配置
        compression_config = CompressionConfig(
            enabled=True,
            format="zip",
            max_archive_size=50*1024*1024,  # 50MB
            compression_level=6,
            selected_paths=["small/", "medium/"],  # 选择部分路径进行压缩
            exclude_patterns=["*.tmp", "temp/*"]
        )
        
        # 测试压缩传输（注意：这里只是测试配置，实际压缩传输需要网络存储）
        print(f'压缩配置: 格式={compression_config.format}, 最大大小={compression_config.max_archive_size//1024//1024}MB')
        
        print('\n--- 测试文件过滤功能 ---')
        # 测试文件过滤
        def test_file_filter(files, include_patterns=None, exclude_patterns=None):
            import fnmatch
            filtered_files = []
            
            for file_meta in files:
                # 检查排除模式
                if exclude_patterns:
                    excluded = False
                    for pattern in exclude_patterns:
                        if fnmatch.fnmatch(file_meta.key, pattern):
                            excluded = True
                            break
                    if excluded:
                        continue
                
                # 检查包含模式
                if include_patterns:
                    included = False
                    for pattern in include_patterns:
                        if fnmatch.fnmatch(file_meta.key, pattern):
                            included = True
                            break
                    if not included:
                        continue
                
                filtered_files.append(file_meta)
            
            return filtered_files
        
        # 测试不同的过滤条件
        txt_files = test_file_filter(source_result.files, include_patterns=["*.txt"])
        small_files_filtered = test_file_filter(source_result.files, include_patterns=["small/*"])
        non_large_files = test_file_filter(source_result.files, exclude_patterns=["large/*"])
        
        print(f'过滤结果:')
        print(f'  .txt文件: {len(txt_files)}')
        print(f'  small目录文件: {len(small_files_filtered)}')
        print(f'  非large目录文件: {len(non_large_files)}')
        
        print('\n--- 测试并发性能 ---')
        # 测试不同并发数的性能
        for workers in [1, 2, 4]:
            print(f'测试 {workers} 个并发线程...')
            test_engine = EfficientSyncEngine(max_workers=workers)
            
            # 清空目标目录
            if os.path.exists(target_dir):
                shutil.rmtree(target_dir)
            os.makedirs(target_dir)
            
            import time
            start_time = time.time()
            
            # 只同步小文件进行性能测试
            small_files_only = [f for f in source_result.files if f.key.startswith('small/')]
            
            result = test_engine.sync_files(
                source_adapter, target_adapter, small_files_only[:10],  # 只测试前10个文件
                f'perf_test_{workers}', f'perf_execution_{workers}'
            )
            
            elapsed = time.time() - start_time
            print(f'  {workers} 线程: {elapsed:.2f}秒, 速度: {result.speed_mbps:.2f} MB/s')
        
        print('\n--- 测试错误处理 ---')
        # 创建一个会失败的适配器（指向不存在的路径）
        try:
            error_config = LocalStorageConfig(
                name='错误测试存储',
                root_path='C:/nonexistent/path/that/does/not/exist'
            )
            error_adapter = LocalStorageAdapter(error_config)
            print('错误适配器创建失败（应该抛出异常）')
        except Exception as e:
            print(f'错误适配器创建异常（预期）: {e}')

        # 测试文件不存在的情况
        try:
            nonexistent_data = source_adapter.get_file('nonexistent_file.txt')
            print(f'获取不存在文件: {nonexistent_data is None}')
        except Exception as e:
            print(f'获取不存在文件异常: {e}')

        # 测试删除不存在的文件
        try:
            delete_result = source_adapter.delete_file('nonexistent_file.txt')
            print(f'删除不存在文件结果: {delete_result}')
        except Exception as e:
            print(f'删除不存在文件异常: {e}')
        
    finally:
        # 清理测试目录
        if os.path.exists(temp_base):
            try:
                shutil.rmtree(temp_base)
                print(f'\n清理测试目录: {temp_base}')
            except Exception as e:
                print(f'\n清理测试目录失败: {e}')
    
    print('同步功能深度测试完成')

if __name__ == '__main__':
    test_sync_functionality()
