#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复complete_web_interface.py中的JavaScript模板字符串问题
"""

import re

def fix_template_strings():
    """修复JavaScript模板字符串中的三重大括号问题"""
    
    # 读取文件
    with open('complete_web_interface.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 修复模式：将 ${{{variable}}} 替换为 ${variable}
    # 但要小心不要影响Python的字符串格式化
    
    # 查找所有的 ${{{...}}} 模式
    pattern = r'\$\{\{\{([^}]+)\}\}\}'
    
    def replace_func(match):
        inner_content = match.group(1)
        return f'${{{inner_content}}}'
    
    # 执行替换
    fixed_content = re.sub(pattern, replace_func, content)
    
    # 写回文件
    with open('complete_web_interface.py', 'w', encoding='utf-8') as f:
        f.write(fixed_content)
    
    print("✅ JavaScript模板字符串修复完成")

if __name__ == "__main__":
    fix_template_strings()
