#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高性能SFTP存储适配器
基于AsyncSSH和最佳实践优化，提供企业级SFTP同步性能
"""

import asyncio
import asyncssh
import logging
import time
from typing import List, Optional, Dict, Any, AsyncGenerator
from dataclasses import dataclass
from pathlib import Path
import hashlib
import os
from concurrent.futures import ThreadPoolExecutor
import aiofiles

# 延迟导入避免循环依赖
from typing import TYPE_CHECKING
if TYPE_CHECKING:
    from storage_abstraction import StorageAdapter, FileMetadata, StorageType, HighPerformanceSFTPStorageConfig

# 配置日志
logger = logging.getLogger(__name__)

@dataclass
class HighPerformanceSFTPConfig:
    """高性能SFTP配置"""
    hostname: str
    port: int = 22
    username: str = ""
    password: str = ""
    private_key_path: str = ""
    private_key_passphrase: str = ""
    
    # 性能优化配置
    max_connections: int = 8  # 连接池大小
    max_concurrent_transfers: int = 16  # 最大并发传输数
    chunk_size: int = 64 * 1024  # 64KB 分块大小
    prefetch_size: int = 1024 * 1024  # 1MB 预取大小
    
    # 连接优化
    keepalive_interval: int = 30  # 保活间隔
    connect_timeout: int = 30  # 连接超时
    
    # 重试配置
    max_retries: int = 3
    retry_delay: float = 1.0
    
    # 压缩配置
    compression: bool = True
    compression_level: int = 6
    
    # 缓存配置
    enable_stat_cache: bool = True
    stat_cache_ttl: int = 300  # 5分钟


class ConnectionPool:
    """SFTP连接池"""
    
    def __init__(self, config: HighPerformanceSFTPConfig):
        self.config = config
        self.connections: List[asyncssh.SSHClientConnection] = []
        self.available_connections: asyncio.Queue = asyncio.Queue()
        self.lock = asyncio.Lock()
        self.closed = False
        
    async def initialize(self):
        """初始化连接池"""
        logger.info(f"初始化SFTP连接池，大小: {self.config.max_connections}")
        
        for i in range(self.config.max_connections):
            try:
                conn = await self._create_connection()
                self.connections.append(conn)
                await self.available_connections.put(conn)
                logger.debug(f"创建连接 {i+1}/{self.config.max_connections}")
            except Exception as e:
                logger.error(f"创建连接失败: {e}")
                raise
    
    async def _create_connection(self) -> asyncssh.SSHClientConnection:
        """创建单个连接"""
        connect_kwargs = {
            'host': self.config.hostname,
            'port': self.config.port,
            'username': self.config.username,
            'connect_timeout': self.config.connect_timeout,
            'keepalive_interval': self.config.keepalive_interval,
            'compression_algs': ['<EMAIL>', 'zlib'] if self.config.compression else None,
        }
        
        # 认证配置
        if self.config.private_key_path:
            connect_kwargs['client_keys'] = [self.config.private_key_path]
            if self.config.private_key_passphrase:
                connect_kwargs['passphrase'] = self.config.private_key_passphrase
        elif self.config.password:
            connect_kwargs['password'] = self.config.password
        
        return await asyncssh.connect(**connect_kwargs)
    
    async def get_connection(self) -> asyncssh.SSHClientConnection:
        """获取连接"""
        if self.closed:
            raise RuntimeError("连接池已关闭")
        
        try:
            # 等待可用连接，超时30秒
            conn = await asyncio.wait_for(
                self.available_connections.get(), 
                timeout=30.0
            )
            
            # 检查连接是否有效
            if conn.is_closing():
                logger.warning("检测到无效连接，重新创建")
                conn = await self._create_connection()
            
            return conn
        except asyncio.TimeoutError:
            raise RuntimeError("获取连接超时")
    
    async def return_connection(self, conn: asyncssh.SSHClientConnection):
        """归还连接"""
        if not self.closed and not conn.is_closing():
            await self.available_connections.put(conn)
    
    async def close(self):
        """关闭连接池"""
        self.closed = True
        
        # 关闭所有连接
        for conn in self.connections:
            if not conn.is_closing():
                conn.close()
                await conn.wait_closed()
        
        self.connections.clear()
        logger.info("SFTP连接池已关闭")


class HighPerformanceSFTPAdapter:
    """高性能SFTP存储适配器"""

    def __init__(self, config):
        # 运行时导入避免循环依赖
        from storage_abstraction import HighPerformanceSFTPStorageConfig

        # 兼容两种配置类型
        if isinstance(config, HighPerformanceSFTPStorageConfig):
            # 从标准配置转换为内部配置
            self.config = HighPerformanceSFTPConfig(
                hostname=config.hostname,
                port=config.port,
                username=config.username,
                password=config.password or "",
                private_key_path=config.private_key_path or "",
                private_key_passphrase=config.private_key_passphrase or "",
                max_connections=config.max_connections,
                max_concurrent_transfers=config.max_concurrent_transfers,
                chunk_size=config.chunk_size,
                prefetch_size=config.prefetch_size,
                keepalive_interval=config.keepalive_interval,
                connect_timeout=config.connect_timeout,
                max_retries=config.max_retries,
                retry_delay=config.retry_delay,
                compression=config.compression,
                compression_level=config.compression_level,
                enable_stat_cache=config.enable_stat_cache,
                stat_cache_ttl=config.stat_cache_ttl
            )
            self.root_path = config.root_path
        elif isinstance(config, HighPerformanceSFTPConfig):
            self.config = config
            self.root_path = "/"
        else:
            raise ValueError(f"不支持的配置类型: {type(config)}")
        self.connection_pool: Optional[ConnectionPool] = None
        self.stat_cache: Dict[str, tuple] = {}  # 路径 -> (stat_result, timestamp)
        self.semaphore = asyncio.Semaphore(config.max_concurrent_transfers)
        self.executor = ThreadPoolExecutor(max_workers=4)
        
    async def connect(self) -> bool:
        """连接到SFTP服务器"""
        try:
            self.connection_pool = ConnectionPool(self.config)
            await self.connection_pool.initialize()
            logger.info(f"成功连接到SFTP服务器: {self.config.hostname}:{self.config.port}")
            return True
        except Exception as e:
            logger.error(f"连接SFTP服务器失败: {e}")
            return False
    
    async def disconnect(self):
        """断开连接"""
        if self.connection_pool:
            await self.connection_pool.close()
            self.connection_pool = None
        
        self.executor.shutdown(wait=True)
        logger.info("SFTP连接已断开")
    
    async def _get_sftp_client(self):
        """获取SFTP客户端"""
        if not self.connection_pool:
            raise RuntimeError("未连接到SFTP服务器")
        
        conn = await self.connection_pool.get_connection()
        sftp = await conn.start_sftp_client()
        return conn, sftp
    
    async def _return_connection(self, conn):
        """归还连接"""
        if self.connection_pool:
            await self.connection_pool.return_connection(conn)
    
    def _get_cached_stat(self, path: str) -> Optional[Any]:
        """获取缓存的stat结果"""
        if not self.config.enable_stat_cache:
            return None
        
        if path in self.stat_cache:
            stat_result, timestamp = self.stat_cache[path]
            if time.time() - timestamp < self.config.stat_cache_ttl:
                return stat_result
            else:
                del self.stat_cache[path]
        
        return None
    
    def _cache_stat(self, path: str, stat_result: Any):
        """缓存stat结果"""
        if self.config.enable_stat_cache:
            self.stat_cache[path] = (stat_result, time.time())
    
    async def list_files(self, prefix: str = ""):
        """列出文件（高性能版本）"""
        from storage_abstraction import FileMetadata

        conn, sftp = await self._get_sftp_client()
        
        try:
            async def scan_directory(dir_path: str):
                """递归扫描目录"""
                try:
                    # 使用批量操作提高性能
                    entries = await sftp.listdir_attr(dir_path)
                    
                    for entry in entries:
                        full_path = f"{dir_path}/{entry.filename}".replace("//", "/")
                        
                        if entry.st_mode and entry.st_mode & 0o040000:  # 目录
                            async for file_meta in scan_directory(full_path):
                                yield file_meta
                        else:  # 文件
                            # 构建相对路径
                            relative_path = full_path
                            if prefix and full_path.startswith(prefix):
                                relative_path = full_path[len(prefix):].lstrip("/")
                            
                            yield FileMetadata(
                                key=relative_path,
                                size=entry.st_size or 0,
                                last_modified=entry.st_mtime or 0,
                                etag=None  # SFTP不支持ETag
                            )
                
                except Exception as e:
                    logger.error(f"扫描目录失败 {dir_path}: {e}")
            
            # 开始扫描
            start_path = prefix if prefix else "."
            async for file_meta in scan_directory(start_path):
                yield file_meta
        
        finally:
            await self._return_connection(conn)
    
    async def file_exists(self, key: str) -> bool:
        """检查文件是否存在"""
        # 先检查缓存
        cached_stat = self._get_cached_stat(key)
        if cached_stat is not None:
            return True
        
        conn, sftp = await self._get_sftp_client()
        
        try:
            stat_result = await sftp.stat(key)
            self._cache_stat(key, stat_result)
            return True
        except (FileNotFoundError, asyncssh.SFTPError):
            return False
        except Exception as e:
            logger.error(f"检查文件存在性失败 {key}: {e}")
            return False
        finally:
            await self._return_connection(conn)
    
    async def get_file_metadata(self, key: str):
        """获取文件元数据"""
        from storage_abstraction import FileMetadata
        # 先检查缓存
        cached_stat = self._get_cached_stat(key)
        if cached_stat:
            return FileMetadata(
                key=key,
                size=cached_stat.st_size,
                last_modified=cached_stat.st_mtime,
                etag=None
            )
        
        conn, sftp = await self._get_sftp_client()
        
        try:
            stat_result = await sftp.stat(key)
            self._cache_stat(key, stat_result)
            
            return FileMetadata(
                key=key,
                size=stat_result.st_size,
                last_modified=stat_result.st_mtime,
                etag=None
            )
        except (FileNotFoundError, asyncssh.SFTPError):
            return None
        except Exception as e:
            logger.error(f"获取文件元数据失败 {key}: {e}")
            return None
        finally:
            await self._return_connection(conn)
    
    async def upload_file(self, local_path: str, key: str, 
                         progress_callback=None) -> bool:
        """上传文件（高性能版本）"""
        async with self.semaphore:  # 限制并发数
            conn, sftp = await self._get_sftp_client()
            
            try:
                # 确保目标目录存在
                target_dir = os.path.dirname(key)
                if target_dir:
                    await self._ensure_directory_exists(sftp, target_dir)
                
                # 获取文件大小
                file_size = os.path.getsize(local_path)
                
                # 使用高性能传输
                await self._upload_with_progress(
                    sftp, local_path, key, file_size, progress_callback
                )
                
                # 清除缓存
                if key in self.stat_cache:
                    del self.stat_cache[key]
                
                logger.debug(f"文件上传成功: {local_path} -> {key}")
                return True
                
            except Exception as e:
                logger.error(f"文件上传失败 {local_path} -> {key}: {e}")
                return False
            finally:
                await self._return_connection(conn)
    
    async def _upload_with_progress(self, sftp, local_path: str, remote_path: str,
                                   file_size: int, progress_callback=None):
        """带进度的高性能上传"""
        async with aiofiles.open(local_path, 'rb') as local_file:
            async with sftp.open(remote_path, 'wb') as remote_file:
                # 设置缓冲区大小
                await remote_file.set_pipelined(True)
                
                bytes_transferred = 0
                
                while True:
                    chunk = await local_file.read(self.config.chunk_size)
                    if not chunk:
                        break
                    
                    await remote_file.write(chunk)
                    bytes_transferred += len(chunk)
                    
                    if progress_callback:
                        progress = (bytes_transferred / file_size) * 100
                        progress_callback(progress, bytes_transferred, file_size)
    
    async def _ensure_directory_exists(self, sftp, dir_path: str):
        """确保目录存在"""
        if not dir_path or dir_path == ".":
            return
        
        try:
            await sftp.stat(dir_path)
        except (FileNotFoundError, asyncssh.SFTPError):
            # 目录不存在，递归创建
            parent_dir = os.path.dirname(dir_path)
            if parent_dir and parent_dir != dir_path:
                await self._ensure_directory_exists(sftp, parent_dir)
            
            try:
                await sftp.mkdir(dir_path)
                logger.debug(f"创建目录: {dir_path}")
            except Exception as e:
                logger.warning(f"创建目录失败 {dir_path}: {e}")
    
    async def download_file(self, key: str, local_path: str,
                           progress_callback=None) -> bool:
        """下载文件（高性能版本）"""
        async with self.semaphore:  # 限制并发数
            conn, sftp = await self._get_sftp_client()
            
            try:
                # 确保本地目录存在
                local_dir = os.path.dirname(local_path)
                if local_dir:
                    os.makedirs(local_dir, exist_ok=True)
                
                # 获取远程文件大小
                stat_result = await sftp.stat(key)
                file_size = stat_result.st_size
                
                # 使用高性能传输
                await self._download_with_progress(
                    sftp, key, local_path, file_size, progress_callback
                )
                
                logger.debug(f"文件下载成功: {key} -> {local_path}")
                return True
                
            except Exception as e:
                logger.error(f"文件下载失败 {key} -> {local_path}: {e}")
                return False
            finally:
                await self._return_connection(conn)
    
    async def _download_with_progress(self, sftp, remote_path: str, local_path: str,
                                     file_size: int, progress_callback=None):
        """带进度的高性能下载"""
        async with sftp.open(remote_path, 'rb') as remote_file:
            # 设置预取以提高性能
            await remote_file.set_pipelined(True)
            
            async with aiofiles.open(local_path, 'wb') as local_file:
                bytes_transferred = 0
                
                while bytes_transferred < file_size:
                    chunk = await remote_file.read(self.config.chunk_size)
                    if not chunk:
                        break
                    
                    await local_file.write(chunk)
                    bytes_transferred += len(chunk)
                    
                    if progress_callback:
                        progress = (bytes_transferred / file_size) * 100
                        progress_callback(progress, bytes_transferred, file_size)
    
    async def delete_file(self, key: str) -> bool:
        """删除文件"""
        conn, sftp = await self._get_sftp_client()
        
        try:
            await sftp.remove(key)
            
            # 清除缓存
            if key in self.stat_cache:
                del self.stat_cache[key]
            
            logger.debug(f"文件删除成功: {key}")
            return True
            
        except Exception as e:
            logger.error(f"文件删除失败 {key}: {e}")
            return False
        finally:
            await self._return_connection(conn)
    
    async def batch_upload(self, file_pairs: List[tuple],
                          progress_callback=None) -> Dict[str, bool]:
        """批量上传文件（高性能版本）"""
        results = {}
        total_files = len(file_pairs)
        completed_files = 0

        # 创建信号量限制并发数
        semaphore = asyncio.Semaphore(self.config.max_concurrent_transfers)

        async def upload_single_file(local_path: str, remote_key: str):
            """上传单个文件"""
            nonlocal completed_files

            async with semaphore:
                try:
                    success = await self.upload_file(local_path, remote_key)
                    results[remote_key] = success

                    completed_files += 1
                    if progress_callback:
                        progress = (completed_files / total_files) * 100
                        progress_callback(progress, completed_files, total_files)

                    return success
                except Exception as e:
                    logger.error(f"批量上传文件失败 {local_path}: {e}")
                    results[remote_key] = False
                    return False

        # 并发执行所有上传任务
        tasks = [
            upload_single_file(local_path, remote_key)
            for local_path, remote_key in file_pairs
        ]

        await asyncio.gather(*tasks, return_exceptions=True)

        success_count = sum(1 for success in results.values() if success)
        logger.info(f"批量上传完成: {success_count}/{total_files} 成功")

        return results

    async def batch_download(self, file_pairs: List[tuple],
                           progress_callback=None) -> Dict[str, bool]:
        """批量下载文件（高性能版本）"""
        results = {}
        total_files = len(file_pairs)
        completed_files = 0

        # 创建信号量限制并发数
        semaphore = asyncio.Semaphore(self.config.max_concurrent_transfers)

        async def download_single_file(remote_key: str, local_path: str):
            """下载单个文件"""
            nonlocal completed_files

            async with semaphore:
                try:
                    success = await self.download_file(remote_key, local_path)
                    results[remote_key] = success

                    completed_files += 1
                    if progress_callback:
                        progress = (completed_files / total_files) * 100
                        progress_callback(progress, completed_files, total_files)

                    return success
                except Exception as e:
                    logger.error(f"批量下载文件失败 {remote_key}: {e}")
                    results[remote_key] = False
                    return False

        # 并发执行所有下载任务
        tasks = [
            download_single_file(remote_key, local_path)
            for remote_key, local_path in file_pairs
        ]

        await asyncio.gather(*tasks, return_exceptions=True)

        success_count = sum(1 for success in results.values() if success)
        logger.info(f"批量下载完成: {success_count}/{total_files} 成功")

        return results

    async def get_connection_stats(self) -> Dict[str, Any]:
        """获取连接统计信息"""
        if not self.connection_pool:
            return {}

        return {
            'total_connections': len(self.connection_pool.connections),
            'available_connections': self.connection_pool.available_connections.qsize(),
            'cache_entries': len(self.stat_cache),
            'max_concurrent_transfers': self.config.max_concurrent_transfers,
            'compression_enabled': self.config.compression
        }

    def get_storage_type(self):
        """获取存储类型"""
        from storage_abstraction import StorageType
        return StorageType.SFTP_HIGH_PERFORMANCE

    # 同步接口方法（兼容现有系统）
    def connect_sync(self) -> bool:
        """同步连接方法"""
        import asyncio
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            return loop.run_until_complete(self.connect())
        except Exception as e:
            logger.error(f"同步连接失败: {e}")
            return False
        finally:
            try:
                loop.close()
            except:
                pass

    def disconnect_sync(self):
        """同步断开连接方法"""
        import asyncio
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(self.disconnect())
        except Exception as e:
            logger.error(f"同步断开连接失败: {e}")
        finally:
            try:
                loop.close()
            except:
                pass

    def list_files_sync(self, prefix: str = ""):
        """同步列出文件方法"""
        import asyncio
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            files = []
            async def collect_files():
                async for file_meta in self.list_files(prefix):
                    files.append(file_meta)

            loop.run_until_complete(collect_files())
            return files
        except Exception as e:
            logger.error(f"同步列出文件失败: {e}")
            return []
        finally:
            try:
                loop.close()
            except:
                pass

    def upload_file_sync(self, local_path: str, key: str, progress_callback=None) -> bool:
        """同步上传文件方法"""
        import asyncio
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            return loop.run_until_complete(self.upload_file(local_path, key, progress_callback))
        except Exception as e:
            logger.error(f"同步上传文件失败: {e}")
            return False
        finally:
            try:
                loop.close()
            except:
                pass

    def download_file_sync(self, key: str, local_path: str, progress_callback=None) -> bool:
        """同步下载文件方法"""
        import asyncio
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            return loop.run_until_complete(self.download_file(key, local_path, progress_callback))
        except Exception as e:
            logger.error(f"同步下载文件失败: {e}")
            return False
        finally:
            try:
                loop.close()
            except:
                pass

    def file_exists_sync(self, key: str) -> bool:
        """同步检查文件是否存在方法"""
        import asyncio
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            return loop.run_until_complete(self.file_exists(key))
        except Exception as e:
            logger.error(f"同步检查文件存在失败: {e}")
            return False
        finally:
            try:
                loop.close()
            except:
                pass

    def get_file_metadata_sync(self, key: str):
        """同步获取文件元数据方法"""
        import asyncio
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            return loop.run_until_complete(self.get_file_metadata(key))
        except Exception as e:
            logger.error(f"同步获取文件元数据失败: {e}")
            return None
        finally:
            try:
                loop.close()
            except:
                pass

    def delete_file_sync(self, key: str) -> bool:
        """同步删除文件方法"""
        import asyncio
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            return loop.run_until_complete(self.delete_file(key))
        except Exception as e:
            logger.error(f"同步删除文件失败: {e}")
            return False
        finally:
            try:
                loop.close()
            except:
                pass


class SFTPPerformanceOptimizer:
    """SFTP性能优化器"""

    @staticmethod
    def create_optimized_config(hostname: str, username: str, password: str = "",
                               private_key_path: str = "",
                               performance_profile: str = "balanced") -> HighPerformanceSFTPConfig:
        """创建优化的SFTP配置"""

        profiles = {
            "high_performance": {
                "max_connections": 16,
                "max_concurrent_transfers": 32,
                "chunk_size": 128 * 1024,  # 128KB
                "prefetch_size": 2 * 1024 * 1024,  # 2MB
                "compression": False,  # 高性能模式关闭压缩
                "stat_cache_ttl": 600  # 10分钟
            },
            "balanced": {
                "max_connections": 8,
                "max_concurrent_transfers": 16,
                "chunk_size": 64 * 1024,  # 64KB
                "prefetch_size": 1024 * 1024,  # 1MB
                "compression": True,
                "stat_cache_ttl": 300  # 5分钟
            },
            "conservative": {
                "max_connections": 4,
                "max_concurrent_transfers": 8,
                "chunk_size": 32 * 1024,  # 32KB
                "prefetch_size": 512 * 1024,  # 512KB
                "compression": True,
                "stat_cache_ttl": 180  # 3分钟
            }
        }

        profile_config = profiles.get(performance_profile, profiles["balanced"])

        return HighPerformanceSFTPConfig(
            hostname=hostname,
            username=username,
            password=password,
            private_key_path=private_key_path,
            **profile_config
        )

    @staticmethod
    def auto_tune_config(config: HighPerformanceSFTPConfig,
                        network_latency_ms: float = 50,
                        bandwidth_mbps: float = 100) -> HighPerformanceSFTPConfig:
        """根据网络条件自动调优配置"""

        # 根据延迟调整连接数
        if network_latency_ms > 200:  # 高延迟
            config.max_connections = min(16, config.max_connections * 2)
            config.max_concurrent_transfers = min(32, config.max_concurrent_transfers * 2)
        elif network_latency_ms < 20:  # 低延迟
            config.max_connections = max(4, config.max_connections // 2)

        # 根据带宽调整分块大小
        if bandwidth_mbps > 1000:  # 高带宽
            config.chunk_size = 256 * 1024  # 256KB
            config.prefetch_size = 4 * 1024 * 1024  # 4MB
        elif bandwidth_mbps < 10:  # 低带宽
            config.chunk_size = 16 * 1024  # 16KB
            config.prefetch_size = 256 * 1024  # 256KB
            config.compression = True  # 低带宽启用压缩

        return config
