#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高性能SFTP存储适配器
基于AsyncSSH和最佳实践优化，提供企业级SFTP同步性能
"""

import asyncio
import asyncssh
import logging
import time
from typing import List, Optional, Dict, Any, AsyncGenerator
from dataclasses import dataclass
from pathlib import Path
import hashlib
import os
from concurrent.futures import ThreadPoolExecutor
import aiofiles
import stat
import posixpath
from datetime import datetime
from io import BytesIO

# 配置日志
logger = logging.getLogger(__name__)

@dataclass
class HighPerformanceSFTPConfig:
    """高性能SFTP配置"""
    hostname: str
    port: int = 22
    username: str = ""
    password: str = ""
    private_key_path: str = ""
    private_key_passphrase: str = ""
    root_path: str = "/"  # 根路径
    name: str = ""  # 存储名称
    description: str = ""  # 存储描述
    
    # 性能优化配置
    max_connections: int = 8  # 连接池大小
    max_concurrent_transfers: int = 16  # 最大并发传输数
    chunk_size: int = 64 * 1024  # 64KB 分块大小
    prefetch_size: int = 1024 * 1024  # 1MB 预取大小
    
    # 连接优化
    keepalive_interval: int = 30  # 保活间隔
    connect_timeout: int = 30  # 连接超时
    
    # 重试配置
    max_retries: int = 3
    retry_delay: float = 1.0
    
    # 压缩配置
    compression: bool = True
    compression_level: int = 6
    
    # 缓存配置
    enable_stat_cache: bool = True
    stat_cache_ttl: int = 300  # 5分钟


class ConnectionPool:
    """SFTP连接池"""
    
    def __init__(self, config: HighPerformanceSFTPConfig):
        self.config = config
        self.connections: List[asyncssh.SSHClientConnection] = []
        self.available_connections: asyncio.Queue = asyncio.Queue()
        self.lock = asyncio.Lock()
        self.closed = False
        
    async def initialize(self):
        """初始化连接池"""
        logger.info(f"初始化SFTP连接池，大小: {self.config.max_connections}")
        
        for i in range(self.config.max_connections):
            try:
                conn = await self._create_connection()
                self.connections.append(conn)
                await self.available_connections.put(conn)
                logger.debug(f"创建连接 {i+1}/{self.config.max_connections}")
            except Exception as e:
                logger.error(f"创建连接失败: {e}")
                raise
    
    async def _create_connection(self) -> asyncssh.SSHClientConnection:
        """创建单个连接"""
        connect_kwargs = {
            'host': self.config.hostname,
            'port': self.config.port,
            'username': self.config.username,
            'connect_timeout': self.config.connect_timeout,
            'keepalive_interval': self.config.keepalive_interval,
            'compression_algs': ['<EMAIL>', 'zlib'] if self.config.compression else None,
            'known_hosts': None,  # 跳过主机密钥验证（测试环境）
        }
        
        # 认证配置
        if self.config.private_key_path:
            connect_kwargs['client_keys'] = [self.config.private_key_path]
            if self.config.private_key_passphrase:
                connect_kwargs['passphrase'] = self.config.private_key_passphrase
        elif self.config.password:
            connect_kwargs['password'] = self.config.password
        
        return await asyncssh.connect(**connect_kwargs)
    
    async def get_connection(self) -> asyncssh.SSHClientConnection:
        """获取连接"""
        if self.closed:
            raise RuntimeError("连接池已关闭")
        
        try:
            # 等待可用连接，超时30秒
            conn = await asyncio.wait_for(
                self.available_connections.get(), 
                timeout=30.0
            )
            
            # 检查连接是否有效
            try:
                if hasattr(conn, 'is_closing') and conn.is_closing():
                    logger.warning("检测到无效连接，重新创建")
                    conn = await self._create_connection()
                elif hasattr(conn, '_closing') and conn._closing:
                    logger.warning("检测到无效连接，重新创建")
                    conn = await self._create_connection()
            except Exception:
                # 如果检查连接状态失败，重新创建连接
                logger.warning("连接状态检查失败，重新创建")
                conn = await self._create_connection()
            
            return conn
        except asyncio.TimeoutError:
            raise RuntimeError("获取连接超时")
    
    async def return_connection(self, conn: asyncssh.SSHClientConnection):
        """归还连接"""
        try:
            is_closing = False
            if hasattr(conn, 'is_closing'):
                is_closing = conn.is_closing()
            elif hasattr(conn, '_closing'):
                is_closing = conn._closing

            if not self.closed and not is_closing:
                await self.available_connections.put(conn)
        except Exception:
            # 如果检查连接状态失败，不归还连接
            pass
    
    async def close(self):
        """关闭连接池"""
        self.closed = True
        
        # 关闭所有连接
        for conn in self.connections:
            try:
                is_closing = False
                if hasattr(conn, 'is_closing'):
                    is_closing = conn.is_closing()
                elif hasattr(conn, '_closing'):
                    is_closing = conn._closing

                if not is_closing:
                    conn.close()
                    await conn.wait_closed()
            except Exception as e:
                logger.warning(f"关闭连接时出错: {e}")
        
        self.connections.clear()
        logger.info("SFTP连接池已关闭")


class HighPerformanceSFTPCore:
    """高性能SFTP存储适配器核心实现"""

    def __init__(self, config):
        # 运行时导入避免循环依赖
        from storage_abstraction import HighPerformanceSFTPStorageConfig

        # 兼容两种配置类型
        if isinstance(config, HighPerformanceSFTPStorageConfig):
            # 从标准配置转换为内部配置
            self.config = HighPerformanceSFTPConfig(
                hostname=config.hostname,
                port=config.port,
                username=config.username,
                password=config.password or "",
                private_key_path=config.private_key_path or "",
                private_key_passphrase=config.private_key_passphrase or "",
                root_path=getattr(config, 'root_path', '/'),
                name=getattr(config, 'name', ''),
                description=getattr(config, 'description', ''),
                max_connections=config.max_connections,
                max_concurrent_transfers=config.max_concurrent_transfers,
                chunk_size=config.chunk_size,
                prefetch_size=config.prefetch_size,
                keepalive_interval=config.keepalive_interval,
                connect_timeout=config.connect_timeout,
                max_retries=config.max_retries,
                retry_delay=config.retry_delay,
                compression=config.compression,
                compression_level=config.compression_level,
                enable_stat_cache=config.enable_stat_cache,
                stat_cache_ttl=config.stat_cache_ttl
            )
            self.root_path = getattr(config, 'root_path', '/')
        elif isinstance(config, HighPerformanceSFTPConfig):
            self.config = config
            self.root_path = getattr(config, 'root_path', '/')
        else:
            raise ValueError(f"不支持的配置类型: {type(config)}")
        self.connection_pool: Optional[ConnectionPool] = None
        self.stat_cache: Dict[str, tuple] = {}  # 路径 -> (stat_result, timestamp)
        self.semaphore = asyncio.Semaphore(config.max_concurrent_transfers)
        self.executor = ThreadPoolExecutor(max_workers=4)
        
    async def connect(self) -> bool:
        """连接到SFTP服务器"""
        try:
            self.connection_pool = ConnectionPool(self.config)
            await self.connection_pool.initialize()
            logger.info(f"成功连接到SFTP服务器: {self.config.hostname}:{self.config.port}")
            return True
        except Exception as e:
            logger.error(f"连接SFTP服务器失败: {e}")
            return False
    
    async def disconnect(self):
        """断开连接"""
        if self.connection_pool:
            await self.connection_pool.close()
            self.connection_pool = None
        
        self.executor.shutdown(wait=True)
        logger.info("SFTP连接已断开")
    
    async def _get_sftp_client(self):
        """获取SFTP客户端"""
        if not self.connection_pool:
            raise RuntimeError("未连接到SFTP服务器")
        
        conn = await self.connection_pool.get_connection()
        sftp = await conn.start_sftp_client()
        return conn, sftp
    
    async def _return_connection(self, conn):
        """归还连接"""
        if self.connection_pool:
            await self.connection_pool.return_connection(conn)
    
    def _get_cached_stat(self, path: str) -> Optional[Any]:
        """获取缓存的stat结果"""
        if not self.config.enable_stat_cache:
            return None
        
        if path in self.stat_cache:
            stat_result, timestamp = self.stat_cache[path]
            if time.time() - timestamp < self.config.stat_cache_ttl:
                return stat_result
            else:
                del self.stat_cache[path]
        
        return None
    
    def _cache_stat(self, path: str, stat_result: Any):
        """缓存stat结果"""
        if self.config.enable_stat_cache:
            self.stat_cache[path] = (stat_result, time.time())
    
    async def list_files(self, prefix: str = ""):
        """列出文件（防止无限递归版本）"""
        from storage_abstraction import FileMetadata
        from datetime import datetime
        import posixpath
        import asyncio

        conn, sftp = await self._get_sftp_client()

        try:
            # 用于跟踪已访问的路径，防止循环引用
            visited_paths = set()
            file_count = 0
            max_files = 1000  # 限制文件数量

            async def scan_directory(dir_path: str, depth: int = 0):
                """递归扫描目录，带循环检测和深度限制"""
                nonlocal file_count

                # 限制递归深度
                if depth > 2:
                    logger.debug(f"达到最大递归深度，跳过: {dir_path}")
                    return

                # 限制文件数量
                if file_count >= max_files:
                    logger.debug(f"达到最大文件数量限制: {max_files}")
                    return

                # 规范化路径
                normalized_path = posixpath.normpath(dir_path)

                # 检查是否已访问过此路径（防止循环引用）
                if normalized_path in visited_paths:
                    logger.debug(f"检测到循环引用，跳过: {normalized_path}")
                    return

                # 标记为已访问
                visited_paths.add(normalized_path)

                try:
                    # 设置超时，避免长时间阻塞
                    entries = await asyncio.wait_for(sftp.readdir(dir_path), timeout=30.0)

                    for entry in entries:
                        if file_count >= max_files:
                            break

                        # AsyncSSH的SFTPName对象结构
                        filename = entry.filename

                        # 跳过特殊目录
                        if filename in ['.', '..']:
                            continue

                        full_path = posixpath.join(dir_path, filename)

                        # 获取文件属性
                        try:
                            attrs = await asyncio.wait_for(sftp.stat(full_path), timeout=10.0)

                            # 检查文件类型
                            is_directory = False
                            is_regular_file = False

                            if hasattr(attrs, 'type'):
                                # 使用type属性
                                is_directory = (attrs.type == 2)  # FILEXFER_TYPE_DIRECTORY
                                is_regular_file = (attrs.type == 1)  # FILEXFER_TYPE_REGULAR
                            elif hasattr(attrs, 'permissions') and attrs.permissions:
                                # 使用permissions检查文件类型
                                import stat
                                is_directory = stat.S_ISDIR(attrs.permissions)
                                is_regular_file = stat.S_ISREG(attrs.permissions)

                            if is_directory and depth < 2:
                                # 递归扫描子目录（限制深度）
                                async for file_meta in scan_directory(full_path, depth + 1):
                                    yield file_meta

                            elif is_regular_file:
                                # 构建相对路径
                                relative_path = full_path
                                if self.root_path and full_path.startswith(self.root_path):
                                    relative_path = full_path[len(self.root_path):].lstrip("/")

                                # 检查前缀匹配
                                if not prefix or relative_path.startswith(prefix):
                                    # 转换时间戳
                                    last_modified = datetime.now()
                                    if hasattr(attrs, 'mtime') and attrs.mtime:
                                        last_modified = datetime.fromtimestamp(attrs.mtime)

                                    yield FileMetadata(
                                        key=relative_path,
                                        size=getattr(attrs, 'size', 0),
                                        last_modified=last_modified,
                                        etag=None,  # SFTP不支持ETag
                                        content_type='application/octet-stream'
                                    )
                                    file_count += 1

                        except asyncio.TimeoutError:
                            logger.warning(f"获取文件属性超时: {full_path}")
                            continue
                        except Exception as e:
                            logger.debug(f"获取文件属性失败 {full_path}: {e}")
                            continue

                except asyncio.TimeoutError:
                    logger.warning(f"扫描目录超时: {dir_path}")
                except Exception as e:
                    logger.error(f"扫描目录失败 {dir_path}: {e}")
                finally:
                    # 从已访问集合中移除，允许其他路径访问
                    visited_paths.discard(normalized_path)

            # 开始扫描 - 正确处理root_path和prefix的组合
            if prefix:
                # 如果有前缀，拼接到root_path
                start_path = posixpath.join(self.root_path or "/", prefix).replace('//', '/')
            else:
                # 如果没有前缀，使用root_path
                start_path = self.root_path or "."

            logger.debug(f"开始扫描路径: {start_path} (root_path={self.root_path}, prefix={prefix})")
            async for file_meta in scan_directory(start_path):
                yield file_meta
        
        finally:
            await self._return_connection(conn)
    
    async def file_exists(self, key: str) -> bool:
        """检查文件是否存在"""
        # 先检查缓存
        cached_stat = self._get_cached_stat(key)
        if cached_stat is not None:
            return True
        
        conn, sftp = await self._get_sftp_client()
        
        try:
            stat_result = await sftp.stat(key)
            self._cache_stat(key, stat_result)
            return True
        except (FileNotFoundError, asyncssh.SFTPError):
            return False
        except Exception as e:
            logger.error(f"检查文件存在性失败 {key}: {e}")
            return False
        finally:
            await self._return_connection(conn)
    
    async def get_file_metadata(self, key: str):
        """获取文件元数据"""
        from storage_abstraction import FileMetadata
        # 先检查缓存
        cached_stat = self._get_cached_stat(key)
        if cached_stat:
            return FileMetadata(
                key=key,
                size=cached_stat.st_size,
                last_modified=cached_stat.st_mtime,
                etag=None
            )
        
        conn, sftp = await self._get_sftp_client()
        
        try:
            stat_result = await sftp.stat(key)
            self._cache_stat(key, stat_result)
            
            return FileMetadata(
                key=key,
                size=stat_result.st_size,
                last_modified=stat_result.st_mtime,
                etag=None
            )
        except (FileNotFoundError, asyncssh.SFTPError):
            return None
        except Exception as e:
            logger.error(f"获取文件元数据失败 {key}: {e}")
            return None
        finally:
            await self._return_connection(conn)
    
    async def upload_file(self, local_path: str, key: str, 
                         progress_callback=None) -> bool:
        """上传文件（高性能版本）"""
        async with self.semaphore:  # 限制并发数
            conn, sftp = await self._get_sftp_client()
            
            try:
                # 确保目标目录存在
                target_dir = os.path.dirname(key)
                if target_dir:
                    await self._ensure_directory_exists(sftp, target_dir)
                
                # 获取文件大小
                file_size = os.path.getsize(local_path)
                
                # 使用高性能传输
                await self._upload_with_progress(
                    sftp, local_path, key, file_size, progress_callback
                )
                
                # 清除缓存
                if key in self.stat_cache:
                    del self.stat_cache[key]
                
                logger.debug(f"文件上传成功: {local_path} -> {key}")
                return True
                
            except Exception as e:
                logger.error(f"文件上传失败 {local_path} -> {key}: {e}")
                return False
            finally:
                await self._return_connection(conn)
    
    async def _upload_with_progress(self, sftp, local_path: str, remote_path: str,
                                   file_size: int, progress_callback=None):
        """带进度的高性能上传"""
        async with aiofiles.open(local_path, 'rb') as local_file:
            async with sftp.open(remote_path, 'wb') as remote_file:
                # 设置缓冲区大小
                await remote_file.set_pipelined(True)
                
                bytes_transferred = 0
                
                while True:
                    chunk = await local_file.read(self.config.chunk_size)
                    if not chunk:
                        break
                    
                    await remote_file.write(chunk)
                    bytes_transferred += len(chunk)
                    
                    if progress_callback:
                        progress = (bytes_transferred / file_size) * 100
                        progress_callback(progress, bytes_transferred, file_size)
    
    async def _ensure_directory_exists(self, sftp, dir_path: str):
        """确保目录存在"""
        if not dir_path or dir_path == ".":
            return
        
        try:
            await sftp.stat(dir_path)
        except (FileNotFoundError, asyncssh.SFTPError):
            # 目录不存在，递归创建
            parent_dir = os.path.dirname(dir_path)
            if parent_dir and parent_dir != dir_path:
                await self._ensure_directory_exists(sftp, parent_dir)
            
            try:
                await sftp.mkdir(dir_path)
                logger.debug(f"创建目录: {dir_path}")
            except Exception as e:
                logger.warning(f"创建目录失败 {dir_path}: {e}")
    
    async def download_file(self, key: str, local_path: str,
                           progress_callback=None) -> bool:
        """下载文件（高性能版本）"""
        async with self.semaphore:  # 限制并发数
            conn, sftp = await self._get_sftp_client()
            
            try:
                # 确保本地目录存在
                local_dir = os.path.dirname(local_path)
                if local_dir:
                    os.makedirs(local_dir, exist_ok=True)
                
                # 获取远程文件大小
                stat_result = await sftp.stat(key)
                file_size = stat_result.st_size
                
                # 使用高性能传输
                await self._download_with_progress(
                    sftp, key, local_path, file_size, progress_callback
                )
                
                logger.debug(f"文件下载成功: {key} -> {local_path}")
                return True
                
            except Exception as e:
                logger.error(f"文件下载失败 {key} -> {local_path}: {e}")
                return False
            finally:
                await self._return_connection(conn)
    
    async def _download_with_progress(self, sftp, remote_path: str, local_path: str,
                                     file_size: int, progress_callback=None):
        """带进度的高性能下载"""
        async with sftp.open(remote_path, 'rb') as remote_file:
            # 设置预取以提高性能
            await remote_file.set_pipelined(True)
            
            async with aiofiles.open(local_path, 'wb') as local_file:
                bytes_transferred = 0
                
                while bytes_transferred < file_size:
                    chunk = await remote_file.read(self.config.chunk_size)
                    if not chunk:
                        break
                    
                    await local_file.write(chunk)
                    bytes_transferred += len(chunk)
                    
                    if progress_callback:
                        progress = (bytes_transferred / file_size) * 100
                        progress_callback(progress, bytes_transferred, file_size)
    
    async def delete_file(self, key: str) -> bool:
        """删除文件"""
        conn, sftp = await self._get_sftp_client()
        
        try:
            await sftp.remove(key)
            
            # 清除缓存
            if key in self.stat_cache:
                del self.stat_cache[key]
            
            logger.debug(f"文件删除成功: {key}")
            return True
            
        except Exception as e:
            logger.error(f"文件删除失败 {key}: {e}")
            return False
        finally:
            await self._return_connection(conn)
    
    async def batch_upload(self, file_pairs: List[tuple],
                          progress_callback=None) -> Dict[str, bool]:
        """批量上传文件（高性能版本）"""
        results = {}
        total_files = len(file_pairs)
        completed_files = 0

        # 创建信号量限制并发数
        semaphore = asyncio.Semaphore(self.config.max_concurrent_transfers)

        async def upload_single_file(local_path: str, remote_key: str):
            """上传单个文件"""
            nonlocal completed_files

            async with semaphore:
                try:
                    success = await self.upload_file(local_path, remote_key)
                    results[remote_key] = success

                    completed_files += 1
                    if progress_callback:
                        progress = (completed_files / total_files) * 100
                        progress_callback(progress, completed_files, total_files)

                    return success
                except Exception as e:
                    logger.error(f"批量上传文件失败 {local_path}: {e}")
                    results[remote_key] = False
                    return False

        # 并发执行所有上传任务
        tasks = [
            upload_single_file(local_path, remote_key)
            for local_path, remote_key in file_pairs
        ]

        await asyncio.gather(*tasks, return_exceptions=True)

        success_count = sum(1 for success in results.values() if success)
        logger.info(f"批量上传完成: {success_count}/{total_files} 成功")

        return results

    async def batch_download(self, file_pairs: List[tuple],
                           progress_callback=None) -> Dict[str, bool]:
        """批量下载文件（高性能版本）"""
        results = {}
        total_files = len(file_pairs)
        completed_files = 0

        # 创建信号量限制并发数
        semaphore = asyncio.Semaphore(self.config.max_concurrent_transfers)

        async def download_single_file(remote_key: str, local_path: str):
            """下载单个文件"""
            nonlocal completed_files

            async with semaphore:
                try:
                    success = await self.download_file(remote_key, local_path)
                    results[remote_key] = success

                    completed_files += 1
                    if progress_callback:
                        progress = (completed_files / total_files) * 100
                        progress_callback(progress, completed_files, total_files)

                    return success
                except Exception as e:
                    logger.error(f"批量下载文件失败 {remote_key}: {e}")
                    results[remote_key] = False
                    return False

        # 并发执行所有下载任务
        tasks = [
            download_single_file(remote_key, local_path)
            for remote_key, local_path in file_pairs
        ]

        await asyncio.gather(*tasks, return_exceptions=True)

        success_count = sum(1 for success in results.values() if success)
        logger.info(f"批量下载完成: {success_count}/{total_files} 成功")

        return results

    async def get_connection_stats(self) -> Dict[str, Any]:
        """获取连接统计信息"""
        if not self.connection_pool:
            return {}

        return {
            'total_connections': len(self.connection_pool.connections),
            'available_connections': self.connection_pool.available_connections.qsize(),
            'cache_entries': len(self.stat_cache),
            'max_concurrent_transfers': self.config.max_concurrent_transfers,
            'compression_enabled': self.config.compression
        }

    def get_storage_type(self):
        """获取存储类型"""
        from storage_abstraction import StorageType
        return StorageType.SFTP_HIGH_PERFORMANCE

    # 同步接口方法（兼容现有系统）
    def connect_sync(self) -> bool:
        """同步连接方法"""
        import asyncio
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            return loop.run_until_complete(self.connect())
        except Exception as e:
            logger.error(f"同步连接失败: {e}")
            return False
        finally:
            try:
                loop.close()
            except:
                pass

    def disconnect_sync(self):
        """同步断开连接方法"""
        import asyncio
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(self.disconnect())
        except Exception as e:
            logger.error(f"同步断开连接失败: {e}")
        finally:
            try:
                loop.close()
            except:
                pass

    def list_files_sync(self, prefix: str = ""):
        """同步列出文件方法"""
        import asyncio
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            files = []
            async def collect_files():
                async for file_meta in self.list_files(prefix):
                    files.append(file_meta)

            loop.run_until_complete(collect_files())
            return files
        except Exception as e:
            logger.error(f"同步列出文件失败: {e}")
            return []
        finally:
            try:
                loop.close()
            except:
                pass

    def upload_file_sync(self, local_path: str, key: str, progress_callback=None) -> bool:
        """同步上传文件方法"""
        import asyncio
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            return loop.run_until_complete(self.upload_file(local_path, key, progress_callback))
        except Exception as e:
            logger.error(f"同步上传文件失败: {e}")
            return False
        finally:
            try:
                loop.close()
            except:
                pass

    def download_file_sync(self, key: str, local_path: str, progress_callback=None) -> bool:
        """同步下载文件方法"""
        import asyncio
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            return loop.run_until_complete(self.download_file(key, local_path, progress_callback))
        except Exception as e:
            logger.error(f"同步下载文件失败: {e}")
            return False
        finally:
            try:
                loop.close()
            except:
                pass

    def file_exists_sync(self, key: str) -> bool:
        """同步检查文件是否存在方法"""
        import asyncio
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            return loop.run_until_complete(self.file_exists(key))
        except Exception as e:
            logger.error(f"同步检查文件存在失败: {e}")
            return False
        finally:
            try:
                loop.close()
            except:
                pass

    def get_file_metadata_sync(self, key: str):
        """同步获取文件元数据方法"""
        import asyncio
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            return loop.run_until_complete(self.get_file_metadata(key))
        except Exception as e:
            logger.error(f"同步获取文件元数据失败: {e}")
            return None
        finally:
            try:
                loop.close()
            except:
                pass

    def delete_file_sync(self, key: str) -> bool:
        """同步删除文件方法"""
        import asyncio
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            return loop.run_until_complete(self.delete_file(key))
        except Exception as e:
            logger.error(f"同步删除文件失败: {e}")
            return False
        finally:
            try:
                loop.close()
            except:
                pass


class SFTPPerformanceOptimizer:
    """SFTP性能优化器"""

    @staticmethod
    def create_optimized_config(hostname: str, username: str, password: str = "",
                               private_key_path: str = "",
                               performance_profile: str = "balanced") -> HighPerformanceSFTPConfig:
        """创建优化的SFTP配置"""

        profiles = {
            "high_performance": {
                "max_connections": 16,
                "max_concurrent_transfers": 32,
                "chunk_size": 128 * 1024,  # 128KB
                "prefetch_size": 2 * 1024 * 1024,  # 2MB
                "compression": False,  # 高性能模式关闭压缩
                "stat_cache_ttl": 600  # 10分钟
            },
            "balanced": {
                "max_connections": 8,
                "max_concurrent_transfers": 16,
                "chunk_size": 64 * 1024,  # 64KB
                "prefetch_size": 1024 * 1024,  # 1MB
                "compression": True,
                "stat_cache_ttl": 300  # 5分钟
            },
            "conservative": {
                "max_connections": 4,
                "max_concurrent_transfers": 8,
                "chunk_size": 32 * 1024,  # 32KB
                "prefetch_size": 512 * 1024,  # 512KB
                "compression": True,
                "stat_cache_ttl": 180  # 3分钟
            }
        }

        profile_config = profiles.get(performance_profile, profiles["balanced"])

        return HighPerformanceSFTPConfig(
            hostname=hostname,
            username=username,
            password=password,
            private_key_path=private_key_path,
            **profile_config
        )

    @staticmethod
    def auto_tune_config(config: HighPerformanceSFTPConfig,
                        network_latency_ms: float = 50,
                        bandwidth_mbps: float = 100) -> HighPerformanceSFTPConfig:
        """根据网络条件自动调优配置"""

        # 根据延迟调整连接数
        if network_latency_ms > 200:  # 高延迟
            config.max_connections = min(16, config.max_connections * 2)
            config.max_concurrent_transfers = min(32, config.max_concurrent_transfers * 2)
        elif network_latency_ms < 20:  # 低延迟
            config.max_connections = max(4, config.max_connections // 2)

        # 根据带宽调整分块大小
        if bandwidth_mbps > 1000:  # 高带宽
            config.chunk_size = 256 * 1024  # 256KB
            config.prefetch_size = 4 * 1024 * 1024  # 4MB
        elif bandwidth_mbps < 10:  # 低带宽
            config.chunk_size = 16 * 1024  # 16KB
            config.prefetch_size = 256 * 1024  # 256KB
            config.compression = True  # 低带宽启用压缩

        return config


# StorageAdapter兼容包装器
class HighPerformanceSFTPAdapter:
    """高性能SFTP存储适配器 - StorageAdapter兼容版本"""

    def __init__(self, config):
        self.config = config
        self.core_adapter = None
        self.loop = None
        self.executor = ThreadPoolExecutor(max_workers=4)

    def _ensure_loop(self):
        """确保事件循环存在"""
        try:
            self.loop = asyncio.get_event_loop()
        except RuntimeError:
            self.loop = asyncio.new_event_loop()
            asyncio.set_event_loop(self.loop)

    def _run_async(self, coro):
        """运行异步函数"""
        try:
            # 尝试获取当前运行的事件循环
            current_loop = asyncio.get_running_loop()
            # 如果已经在事件循环中，使用新的线程和新的事件循环
            import concurrent.futures
            import threading

            def run_in_thread():
                new_loop = asyncio.new_event_loop()
                asyncio.set_event_loop(new_loop)
                try:
                    return new_loop.run_until_complete(coro)
                finally:
                    new_loop.close()

            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(run_in_thread)
                return future.result(timeout=300)  # 5分钟超时

        except RuntimeError:
            # 没有运行的事件循环，创建新的
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                return loop.run_until_complete(coro)
            finally:
                loop.close()

    async def _get_core_adapter(self):
        """获取核心适配器实例"""
        if self.core_adapter is None:
            # 转换配置
            core_config = HighPerformanceSFTPConfig(
                hostname=self.config.hostname,
                port=self.config.port,
                username=self.config.username,
                password=self.config.password,
                private_key_path=self.config.private_key_path or "",
                private_key_passphrase=self.config.private_key_passphrase or "",
                root_path=getattr(self.config, 'root_path', '/'),
                name=getattr(self.config, 'name', ''),
                description=getattr(self.config, 'description', ''),
                max_connections=getattr(self.config, 'max_connections', 4),
                max_concurrent_transfers=getattr(self.config, 'max_concurrent_transfers', 8),
                chunk_size=getattr(self.config, 'chunk_size', 64 * 1024),
                prefetch_size=getattr(self.config, 'prefetch_size', 1024 * 1024),
                keepalive_interval=getattr(self.config, 'keepalive_interval', 30),
                connect_timeout=getattr(self.config, 'connect_timeout', 30),
                max_retries=getattr(self.config, 'max_retries', 3),
                retry_delay=getattr(self.config, 'retry_delay', 1.0),
                compression=getattr(self.config, 'compression', True),
                compression_level=getattr(self.config, 'compression_level', 6),
                enable_stat_cache=getattr(self.config, 'enable_stat_cache', True),
                stat_cache_ttl=getattr(self.config, 'stat_cache_ttl', 300)
            )

            self.core_adapter = HighPerformanceSFTPCore(core_config)
            await self.core_adapter.connect()

        return self.core_adapter

    def test_connection(self) -> tuple[bool, str]:
        """测试连接"""
        try:
            async def _test():
                adapter = await self._get_core_adapter()
                stats = await adapter.get_connection_stats()
                return True, f"SFTP连接成功，根目录: {self.config.root_path}, 连接池: {stats.get('active_connections', 0)}"

            return self._run_async(_test())
        except Exception as e:
            return False, f"SFTP连接失败: {str(e)}"

    def list_files(self, prefix: str = "", max_keys: int = 1000,
                   continuation_token: Optional[str] = None):
        """列出文件 - 简化版本，避免事件循环问题"""
        try:
            # 直接使用核心适配器的同步方法
            if hasattr(self, 'core_adapter') and self.core_adapter:
                import asyncio
                import concurrent.futures
                import threading

                async def _list_async():
                    files = []
                    count = 0

                    # 获取核心适配器
                    if not self.core_adapter:
                        core_config = HighPerformanceSFTPConfig(
                            hostname=self.config.hostname,
                            port=self.config.port,
                            username=self.config.username,
                            password=self.config.password,
                            private_key_path=self.config.private_key_path or "",
                            private_key_passphrase=self.config.private_key_passphrase or "",
                            root_path=getattr(self.config, 'root_path', '/'),
                            name=getattr(self.config, 'name', ''),
                            description=getattr(self.config, 'description', ''),
                        )
                        self.core_adapter = HighPerformanceSFTPCore(core_config)
                        await self.core_adapter.connect()

                    # 使用核心适配器列出文件
                    async for file_meta in self.core_adapter.list_files(prefix):
                        if count >= max_keys:
                            break

                        # file_meta已经是FileMetadata对象，直接使用
                        # 调整key路径，移除root_path前缀
                        adjusted_key = file_meta.key
                        if adjusted_key.startswith(self.config.root_path):
                            adjusted_key = adjusted_key[len(self.config.root_path):].lstrip('/')

                        from storage_abstraction import FileMetadata
                        files.append(FileMetadata(
                            key=adjusted_key,
                            size=file_meta.size,
                            last_modified=file_meta.last_modified,
                            etag=file_meta.etag,
                            content_type='application/octet-stream'
                        ))
                        count += 1

                    from storage_abstraction import ListResult
                    return ListResult(
                        files=files,
                        is_truncated=count >= max_keys,
                        next_token=None
                    )

                def run_list():
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    try:
                        return loop.run_until_complete(_list_async())
                    finally:
                        loop.close()

                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(run_list)
                    return future.result(timeout=60)  # 1分钟超时
            else:
                # 如果没有核心适配器，返回空结果
                from storage_abstraction import ListResult
                return ListResult(files=[], is_truncated=False, next_token=None)

        except Exception as e:
            raise Exception(f"列出文件失败: {str(e)}")

    def get_file(self, key: str) -> Optional[bytes]:
        """下载文件 - 简化版本，避免事件循环问题"""
        try:
            # 直接使用核心适配器的同步方法
            if hasattr(self, 'core_adapter') and self.core_adapter:
                # 使用已有的核心适配器
                file_path = posixpath.join(self.config.root_path, key).replace('//', '/')

                # 创建新的事件循环来处理下载
                import asyncio
                import tempfile

                async def _download():
                    # 获取SFTP客户端
                    conn, sftp = await self.core_adapter._get_sftp_client()
                    try:
                        # 直接读取文件内容
                        async with sftp.open(file_path, 'rb') as remote_file:
                            return await remote_file.read()
                    finally:
                        await self.core_adapter._return_connection(conn)

                # 在新线程中运行异步操作
                import concurrent.futures
                import threading

                def run_download():
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    try:
                        return loop.run_until_complete(_download())
                    finally:
                        loop.close()

                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(run_download)
                    return future.result(timeout=60)  # 1分钟超时
            else:
                logger.error(f"核心适配器未初始化")
                return None

        except Exception as e:
            logger.error(f"下载文件失败 {key}: {e}")
            return None

    def put_file(self, key: str, data: bytes,
                 content_type: str = 'binary/octet-stream',
                 metadata: Optional[Dict[str, str]] = None) -> bool:
        """上传文件"""
        try:
            async def _put():
                adapter = await self._get_core_adapter()
                file_path = posixpath.join(self.config.root_path, key).replace('//', '/')

                # 使用临时文件上传
                import tempfile
                with tempfile.NamedTemporaryFile() as tmp_file:
                    tmp_file.write(data)
                    tmp_file.flush()
                    return await adapter.upload_file(tmp_file.name, file_path)

            return self._run_async(_put())
        except Exception as e:
            logger.error(f"上传文件失败 {key}: {e}")
            return False

    def delete_file(self, key: str) -> bool:
        """删除文件"""
        try:
            async def _delete():
                adapter = await self._get_core_adapter()
                file_path = posixpath.join(self.config.root_path, key).replace('//', '/')
                return await adapter.delete_file(file_path)

            return self._run_async(_delete())
        except Exception as e:
            logger.error(f"删除文件失败 {key}: {e}")
            return False

    def file_exists(self, key: str) -> bool:
        """检查文件是否存在"""
        try:
            async def _exists():
                adapter = await self._get_core_adapter()
                file_path = posixpath.join(self.config.root_path, key).replace('//', '/')
                return await adapter.file_exists(file_path)

            return self._run_async(_exists())
        except Exception as e:
            logger.error(f"检查文件存在失败 {key}: {e}")
            return False

    def get_file_metadata(self, key: str):
        """获取文件元数据"""
        try:
            async def _metadata():
                adapter = await self._get_core_adapter()
                file_path = posixpath.join(self.config.root_path, key).replace('//', '/')

                # 获取文件统计信息
                conn, sftp = await adapter._get_sftp_client()
                try:
                    stat_result = await sftp.stat(file_path)

                    from storage_abstraction import FileMetadata
                    return FileMetadata(
                        key=key,
                        size=stat_result.st_size,
                        last_modified=datetime.fromtimestamp(stat_result.st_mtime),
                        etag=str(stat_result.st_mtime),
                        content_type='application/octet-stream'
                    )
                finally:
                    await adapter._return_connection(conn)

            return self._run_async(_metadata())
        except Exception as e:
            logger.error(f"获取文件元数据失败 {key}: {e}")
            return None

    def close(self):
        """关闭连接"""
        try:
            async def _close():
                if self.core_adapter:
                    await self.core_adapter.close()
                    self.core_adapter = None

            if self.core_adapter:
                self._run_async(_close())
        except Exception as e:
            logger.error(f"关闭连接失败: {e}")
        finally:
            if self.executor:
                self.executor.shutdown(wait=False)



