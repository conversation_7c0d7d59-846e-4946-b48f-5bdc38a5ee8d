#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SFTP适配器迁移脚本
将现有的SFTP配置迁移到高性能版本
"""

import json
import logging
from unified_config_manager import UnifiedConfigManager
from high_performance_sftp_adapter import SFTPPerformanceOptimizer

def migrate_sftp_configs():
    """迁移SFTP配置"""
    logger = logging.getLogger(__name__)
    
    try:
        config_manager = UnifiedConfigManager()
        
        # 获取所有数据源
        sources = config_manager.get_all_sources()
        targets = config_manager.get_all_targets()
        
        migrated_count = 0
        
        # 迁移数据源
        for source_id, source_config in sources.items():
            if source_config.get('type') == 'sftp':
                logger.info(f"迁移数据源: {source_config.get('name', source_id)}")
                
                # 创建高性能配置
                hp_config = SFTPPerformanceOptimizer.create_optimized_config(
                    hostname=source_config.get('hostname', ''),
                    username=source_config.get('username', ''),
                    password=source_config.get('password', ''),
                    private_key_path=source_config.get('private_key_path', ''),
                    performance_profile='balanced'
                )
                
                # 更新配置类型
                source_config['type'] = 'sftp_hp'
                source_config['hp_config'] = hp_config.__dict__
                
                config_manager.update_source(source_id, source_config)
                migrated_count += 1
        
        # 迁移目标存储
        for target_id, target_config in targets.items():
            if target_config.get('type') == 'sftp':
                logger.info(f"迁移目标存储: {target_config.get('name', target_id)}")
                
                # 创建高性能配置
                hp_config = SFTPPerformanceOptimizer.create_optimized_config(
                    hostname=target_config.get('hostname', ''),
                    username=target_config.get('username', ''),
                    password=target_config.get('password', ''),
                    private_key_path=target_config.get('private_key_path', ''),
                    performance_profile='balanced'
                )
                
                # 更新配置类型
                target_config['type'] = 'sftp_hp'
                target_config['hp_config'] = hp_config.__dict__
                
                config_manager.update_target(target_id, target_config)
                migrated_count += 1
        
        logger.info(f"✅ 迁移完成，共迁移 {migrated_count} 个SFTP配置")
        return True
        
    except Exception as e:
        logger.error(f"迁移失败: {e}")
        return False

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    migrate_sftp_configs()
