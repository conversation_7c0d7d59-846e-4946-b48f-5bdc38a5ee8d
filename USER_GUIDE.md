# S3 Sync Tool 用户指南

欢迎使用S3 Sync Tool！这是一个功能强大的S3数据同步工具，支持在任意两个S3兼容的对象存储服务之间进行数据同步。

## 📖 目录

1. [快速开始](#快速开始)
2. [安装说明](#安装说明)
3. [图形界面使用](#图形界面使用)
4. [命令行使用](#命令行使用)
5. [配置文件](#配置文件)
6. [高级功能](#高级功能)
7. [故障排除](#故障排除)
8. [最佳实践](#最佳实践)

## 🚀 快速开始

### 第一次使用

1. **下载程序**：从发布页面下载适合您操作系统的可执行文件
2. **解压文件**：将下载的压缩包解压到任意目录
3. **运行程序**：双击可执行文件或在终端中运行

### 选择运行模式

程序提供三种运行方式：

- **交互模式**：运行主程序，选择GUI或CLI
- **图形界面**：直接运行GUI版本，适合日常使用
- **命令行**：直接运行CLI版本，适合自动化脚本

## 💾 安装说明

### Windows

1. 下载 `s3sync-v1.0.0-windows-amd64.zip`
2. 解压到任意目录（如 `C:\Tools\S3Sync\`）
3. 双击 `s3sync.exe` 启动程序

### macOS

1. 下载 `s3sync-v1.0.0-darwin-x86_64.tar.gz`
2. 解压：`tar -xzf s3sync-v1.0.0-darwin-x86_64.tar.gz`
3. 运行：`./s3sync`

**注意**：首次运行可能需要在"系统偏好设置 > 安全性与隐私"中允许运行。

### Linux

1. 下载 `s3sync-v1.0.0-linux-x86_64.tar.gz`
2. 解压：`tar -xzf s3sync-v1.0.0-linux-x86_64.tar.gz`
3. 添加执行权限：`chmod +x s3sync`
4. 运行：`./s3sync`

## 🖥️ 图形界面使用

### 启动GUI

```bash
# Windows
s3sync-gui.exe

# macOS/Linux
./s3sync-gui
```

### 界面说明

#### 1. 源S3配置区域
- **Access Key**：源S3的访问密钥
- **Secret Key**：源S3的秘密密钥
- **Endpoint URL**：自定义端点（可选）
- **Region**：S3区域
- **桶名称**：选择源桶

#### 2. 目标S3配置区域
- 配置项与源S3相同
- 用于设置同步目标

#### 3. 同步选项
- **对象前缀**：只同步指定前缀的对象
- **最大并发数**：控制同步速度和资源使用
- **删除多余对象**：是否删除目标桶中源桶没有的对象

#### 4. 操作按钮
- **测试连接**：验证S3配置是否正确
- **刷新桶列表**：获取可用的桶列表
- **预览同步**：查看将要执行的操作
- **开始同步**：执行实际同步
- **加载配置**：从文件加载配置
- **保存配置**：将当前配置保存到文件

### 使用步骤

1. **配置源S3**
   - 填写源S3的连接信息
   - 点击"测试连接"验证配置
   - 点击"刷新桶列表"获取桶列表
   - 选择源桶

2. **配置目标S3**
   - 填写目标S3的连接信息
   - 测试连接并选择目标桶

3. **设置同步选项**
   - 根据需要设置前缀过滤
   - 调整并发数（建议5-10）
   - 谨慎选择是否删除多余对象

4. **预览和执行**
   - 点击"预览同步"查看操作详情
   - 确认无误后点击"开始同步"

## 💻 命令行使用

### 启动CLI

```bash
# Windows
s3sync-cli.exe --help

# macOS/Linux
./s3sync-cli --help
```

### 基本同步命令

```bash
s3sync-cli sync \
  --source-access-key "AKIA..." \
  --source-secret-key "secret..." \
  --source-bucket "my-source-bucket" \
  --target-access-key "AKIA..." \
  --target-secret-key "secret..." \
  --target-bucket "my-target-bucket"
```

### 高级选项

```bash
s3sync-cli sync \
  --source-access-key "AKIA..." \
  --source-secret-key "secret..." \
  --source-endpoint "https://s3.amazonaws.com" \
  --source-region "us-west-2" \
  --source-bucket "my-source-bucket" \
  --target-access-key "AKIA..." \
  --target-secret-key "secret..." \
  --target-endpoint "https://oss-cn-beijing.aliyuncs.com" \
  --target-region "cn-beijing" \
  --target-bucket "my-target-bucket" \
  --prefix "documents/" \
  --delete-extra \
  --max-workers 10 \
  --dry-run
```

### 参数说明

| 参数 | 说明 | 必需 |
|------|------|------|
| `--source-access-key` | 源S3访问密钥 | ✅ |
| `--source-secret-key` | 源S3秘密密钥 | ✅ |
| `--source-bucket` | 源桶名称 | ✅ |
| `--target-access-key` | 目标S3访问密钥 | ✅ |
| `--target-secret-key` | 目标S3秘密密钥 | ✅ |
| `--target-bucket` | 目标桶名称 | ✅ |
| `--source-endpoint` | 源S3端点URL | ❌ |
| `--source-region` | 源S3区域 | ❌ |
| `--target-endpoint` | 目标S3端点URL | ❌ |
| `--target-region` | 目标S3区域 | ❌ |
| `--prefix` | 对象前缀过滤 | ❌ |
| `--delete-extra` | 删除多余对象 | ❌ |
| `--max-workers` | 最大并发数 | ❌ |
| `--dry-run` | 预览模式 | ❌ |

### 其他命令

```bash
# 测试S3连接
s3sync-cli test-connection \
  --access-key "AKIA..." \
  --secret-key "secret..." \
  --endpoint "https://s3.amazonaws.com"

# 列出S3桶
s3sync-cli list-buckets \
  --access-key "AKIA..." \
  --secret-key "secret..."

# 创建配置文件模板
s3sync-cli create-config

# 使用配置文件同步
s3sync-cli sync-from-config --config-file my-config.yaml
```

## ⚙️ 配置文件

### 创建配置文件

```bash
s3sync-cli create-config
```

这将创建一个 `s3_config.yaml` 模板文件。

### 配置文件格式

```yaml
# S3同步配置文件
source:
  access_key: "AKIA..."
  secret_key: "secret..."
  endpoint_url: "https://s3.amazonaws.com"  # 可选
  region: "us-east-1"
  bucket: "my-source-bucket"

target:
  access_key: "AKIA..."
  secret_key: "secret..."
  endpoint_url: "https://oss-cn-beijing.aliyuncs.com"  # 可选
  region: "cn-beijing"
  bucket: "my-target-bucket"

sync:
  prefix: "documents/"  # 可选，只同步此前缀的对象
  delete_extra: false   # 是否删除目标桶中多余的对象
  max_workers: 5        # 最大并发数
```

### 使用配置文件

```bash
# 命令行
s3sync-cli sync-from-config --config-file my-config.yaml

# GUI中点击"加载配置"按钮选择配置文件
```

## 🔧 高级功能

### 前缀过滤

只同步特定前缀的对象：

```bash
# 只同步 documents/ 目录下的文件
--prefix "documents/"

# 只同步 2023/ 开头的文件
--prefix "2023/"
```

### 删除模式

启用删除模式会删除目标桶中源桶没有的对象：

```bash
# 启用删除模式（谨慎使用）
--delete-extra
```

**⚠️ 警告**：删除模式会永久删除文件，请谨慎使用！

### 并发控制

调整并发数以优化性能：

```bash
# 低并发（适合网络较慢的环境）
--max-workers 2

# 高并发（适合高带宽环境）
--max-workers 20
```

### 预览模式

在实际同步前预览操作：

```bash
# 预览模式，不执行实际同步
--dry-run
```

## 🔌 支持的S3服务

本工具支持所有兼容S3 API的对象存储服务：

### AWS S3
```yaml
endpoint_url: null  # 使用默认端点
region: "us-east-1"
```

### 阿里云OSS
```yaml
endpoint_url: "https://oss-cn-beijing.aliyuncs.com"
region: "cn-beijing"
```

### 腾讯云COS
```yaml
endpoint_url: "https://cos.ap-beijing.myqcloud.com"
region: "ap-beijing"
```

### 华为云OBS
```yaml
endpoint_url: "https://obs.cn-north-4.myhuaweicloud.com"
region: "cn-north-4"
```

### MinIO
```yaml
endpoint_url: "http://localhost:9000"
region: "us-east-1"
```

## 🐛 故障排除

### 常见错误

#### 1. 连接超时
```
错误: Connection timeout
解决: 检查网络连接和endpoint URL
```

#### 2. 权限错误
```
错误: Access Denied
解决: 确认Access Key和Secret Key正确，且有足够权限
```

#### 3. 桶不存在
```
错误: NoSuchBucket
解决: 确认桶名称正确，且在指定区域
```

#### 4. 区域错误
```
错误: AuthorizationHeaderMalformed
解决: 确认region设置正确
```

### 权限要求

确保S3凭证具有以下权限：

**源桶权限**：
- `s3:ListBucket`
- `s3:GetObject`

**目标桶权限**：
- `s3:ListBucket`
- `s3:GetObject`
- `s3:PutObject`
- `s3:DeleteObject`（仅删除模式需要）

### 日志查看

程序会在 `logs/` 目录下生成详细日志：

```bash
# 查看最新日志
tail -f logs/s3_sync_*.log

# 搜索错误信息
grep "ERROR" logs/s3_sync_*.log
```

## 📋 最佳实践

### 1. 安全建议

- **使用IAM用户**：不要使用根账户的Access Key
- **最小权限原则**：只授予必要的权限
- **定期轮换密钥**：定期更换Access Key
- **配置文件安全**：保护配置文件，避免泄露凭证

### 2. 性能优化

- **合理设置并发数**：根据网络带宽调整
- **使用前缀过滤**：减少不必要的对象扫描
- **分批同步**：对于大量文件，考虑分批处理

### 3. 数据安全

- **备份重要数据**：同步前备份重要数据
- **测试小规模**：先在小范围测试
- **使用预览模式**：确认操作无误再执行
- **监控同步过程**：关注日志和错误信息

### 4. 网络优化

- **选择合适的区域**：选择地理位置较近的区域
- **避免跨区域传输**：尽量在同一区域内操作
- **使用专线连接**：对于大量数据传输，考虑专线

### 5. 成本控制

- **了解计费规则**：了解各云服务商的计费方式
- **优化传输策略**：避免重复传输
- **使用生命周期策略**：自动管理对象存储类别

## 📞 获取帮助

### 文档资源

- **README.md**：项目概述和快速开始
- **BUILD_GUIDE.md**：编译构建指南
- **USER_GUIDE.md**：本用户指南

### 命令行帮助

```bash
# 查看主程序帮助
s3sync --help

# 查看CLI帮助
s3sync-cli --help

# 查看特定命令帮助
s3sync-cli sync --help
```

### 社区支持

- 提交Issue报告问题
- 参与讨论和改进建议
- 贡献代码和文档

## 🔄 更新说明

### 检查更新

定期检查是否有新版本发布，新版本通常包含：

- 性能改进
- 错误修复
- 新功能添加
- 安全更新

### 升级步骤

1. 备份当前配置文件
2. 下载新版本
3. 替换可执行文件
4. 测试基本功能
5. 恢复配置文件

---

**感谢使用S3 Sync Tool！** 如果您觉得这个工具有用，请考虑给项目点个星⭐ 