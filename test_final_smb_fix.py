#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试最终的SMB修复方案
"""

import smb_storage_adapter
from storage_abstraction import StorageFactory, SMBStorageConfig, StorageType
import threading
import queue

def download_smb_file_with_thread_isolation(smb_config, file_key):
    """使用线程隔离下载SMB文件"""
    result_queue = queue.Queue()
    
    def isolated_download_worker():
        """隔离的下载工作线程"""
        try:
            # 在独立线程中创建全新的适配器
            fresh_adapter = StorageFactory.create_adapter(smb_config)
            
            # 下载文件
            data = fresh_adapter.get_file(file_key)
            result_queue.put(('success', data))
            
        except Exception as e:
            result_queue.put(('error', str(e)))
    
    # 启动隔离的工作线程
    worker_thread = threading.Thread(target=isolated_download_worker)
    worker_thread.start()
    
    # 等待结果
    worker_thread.join(timeout=30)  # 30秒超时
    
    if worker_thread.is_alive():
        print(f"SMB文件下载超时: {file_key}")
        return None
    
    try:
        result_type, result_data = result_queue.get_nowait()
        if result_type == 'success':
            return result_data
        else:
            print(f"SMB文件下载失败 {file_key}: {result_data}")
            return None
    except queue.Empty:
        print(f"SMB文件下载无结果: {file_key}")
        return None

def test_final_smb_fix():
    """测试最终的SMB修复方案"""
    print("🧪 测试最终的SMB修复方案")
    
    # 创建SMB配置
    smb_config = SMBStorageConfig(
        storage_type=StorageType.SMB,
        hostname='Jayce',
        port=445,
        username='smb',
        password='smbsmb',
        domain='WORKGROUP',
        share_name='hlmj',
        root_path=''
    )
    
    print("📋 模拟任务管理器同步流程:")
    
    try:
        # 创建源适配器
        source_adapter = StorageFactory.create_adapter(smb_config)
        
        # 1. 扫描源文件
        print("1. 扫描源文件...")
        source_files = []
        result = source_adapter.list_files("", max_keys=5)
        source_files.extend(result.files)
        print(f"   ✅ 成功: 找到 {len(source_files)} 个文件")
        
        if len(source_files) < 3:
            print("   ❌ 文件数量不足")
            return False
        
        # 2. 使用线程隔离下载文件
        print("\n2. 使用线程隔离下载文件...")
        success_count = 0
        total_files = min(3, len(source_files))
        
        for i, file_meta in enumerate(source_files[:total_files]):
            print(f"   同步 {i+1}/{total_files}: {file_meta.key}")
            
            try:
                # 使用线程隔离下载
                file_data = download_smb_file_with_thread_isolation(smb_config, file_meta.key)
                
                if file_data is None:
                    print(f"     ❌ 下载失败: 返回None")
                else:
                    print(f"     ✅ 下载成功: {len(file_data)} bytes")
                    success_count += 1
                    
                    # 验证数据完整性
                    if len(file_data) == file_meta.size:
                        print(f"     ✅ 大小匹配: {len(file_data)} == {file_meta.size}")
                    else:
                        print(f"     ⚠️ 大小不匹配: {len(file_data)} != {file_meta.size}")
                
            except Exception as e:
                print(f"     ❌ 下载异常: {e}")
        
        print(f"\n📊 同步结果: {success_count}/{total_files} 成功")
        
        # 评估结果
        success_rate = success_count / total_files
        
        print(f"\n🎯 总体结果:")
        print(f"   成功率: {success_count}/{total_files} ({success_rate:.1%})")
        
        if success_rate >= 0.8:
            print("   ✅ 最终SMB修复方案成功")
            return True
        else:
            print("   ❌ 最终SMB修复方案失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🧪 最终SMB修复方案测试")
    print("=" * 50)
    
    try:
        if test_final_smb_fix():
            print("🎉 最终SMB修复方案测试通过！")
            print("")
            print("🚀 现在可以重新运行同步任务，应该能够:")
            print("  ✅ 成功扫描SMB源文件")
            print("  ✅ 稳定下载所有SMB文件")
            print("  ✅ 避免连接状态污染")
            print("  ✅ 消除 STATUS_OBJECT_NAME_INVALID 错误")
            print("  ✅ 每个文件使用独立的线程下载")
            print("")
            print("📋 修复内容:")
            print("  1. 在任务管理器中为SMB存储使用线程隔离下载")
            print("  2. 每个文件下载在独立线程中创建新的适配器实例")
            print("  3. 完全避免适配器实例间的状态污染")
            
            return 0
        else:
            print("❌ 最终SMB修复方案测试失败")
            return 1
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit(main())
