#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试性能优化页面的所有功能
"""

import requests
import json
import time

def test_optimization_features():
    """测试性能优化页面的所有功能"""
    base_url = 'http://localhost:8007'
    
    print('🔧 性能优化功能详细测试')
    print('=' * 50)
    
    # 1. 获取当前优化配置
    print('\\n1️⃣ 获取当前优化配置')
    try:
        response = requests.get(f'{base_url}/api/optimization-config')
        if response.status_code == 200:
            api_response = response.json()
            if api_response.get('success') and 'config' in api_response:
                current_config = api_response['config']
                print('✅ 获取配置成功')
                print('📋 当前配置:')
                for key, value in current_config.items():
                    print(f'   {key}: {value}')
            else:
                print(f'❌ API响应格式错误: {api_response}')
                return False
        else:
            print(f'❌ 获取配置失败: {response.status_code}')
            return False
    except Exception as e:
        print(f'❌ 获取配置异常: {e}')
        return False
    
    # 2. 测试各个配置项的保存
    print('\\n2️⃣ 测试配置项保存功能')
    
    # 备份原始配置
    original_config = current_config.copy()
    
    # 测试配置项列表
    test_configs = [
        {
            'name': '并发线程数',
            'config': {'max_workers': 8},
            'expected_key': 'max_workers',
            'expected_value': 8
        },
        {
            'name': '分块大小',
            'config': {'chunk_size_mb': 15},
            'expected_key': 'chunk_size_mb',
            'expected_value': 15
        },
        {
            'name': '并行扫描',
            'config': {'enable_parallel_scan': False},
            'expected_key': 'enable_parallel_scan',
            'expected_value': False
        },
        {
            'name': '缓存启用',
            'config': {'enable_cache': False},
            'expected_key': 'enable_cache',
            'expected_value': False
        },
        {
            'name': '重试次数',
            'config': {'retry_times': 5},
            'expected_key': 'retry_times',
            'expected_value': 5
        },
        {
            'name': '重试延迟',
            'config': {'retry_delay': 3},
            'expected_key': 'retry_delay',
            'expected_value': 3
        },
        {
            'name': '完整性验证',
            'config': {'verify_integrity': False},
            'expected_key': 'verify_integrity',
            'expected_value': False
        }
    ]
    
    success_count = 0
    for test in test_configs:
        print(f'\\n   测试 {test["name"]}:')
        
        # 创建新配置
        new_config = original_config.copy()
        new_config.update(test['config'])
        
        try:
            # 保存配置
            response = requests.post(f'{base_url}/api/optimization-config', 
                                   json=new_config,
                                   headers={'Content-Type': 'application/json'})
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    print(f'   ✅ 保存成功')
                    
                    # 验证配置是否真的保存了
                    time.sleep(0.1)  # 短暂等待
                    verify_response = requests.get(f'{base_url}/api/optimization-config')
                    if verify_response.status_code == 200:
                        verify_api_response = verify_response.json()
                        if verify_api_response.get('success') and 'config' in verify_api_response:
                            saved_config = verify_api_response['config']
                            actual_value = saved_config.get(test['expected_key'])
                            expected_value = test['expected_value']
                        else:
                            print(f'   ❌ 验证API响应格式错误: {verify_api_response}')
                            continue
                        
                        if actual_value == expected_value:
                            print(f'   ✅ 验证成功: {test["expected_key"]} = {actual_value}')
                            success_count += 1
                        else:
                            print(f'   ❌ 验证失败: 期望 {expected_value}, 实际 {actual_value}')
                    else:
                        print(f'   ❌ 验证请求失败: {verify_response.status_code}')
                else:
                    print(f'   ❌ 保存失败: {result.get("error", "未知错误")}')
            else:
                print(f'   ❌ 保存请求失败: {response.status_code}')
                
        except Exception as e:
            print(f'   ❌ 测试异常: {e}')
    
    print(f'\\n配置项测试结果: {success_count}/{len(test_configs)} 成功')
    
    # 3. 测试边界值
    print('\\n3️⃣ 测试边界值')
    
    boundary_tests = [
        {
            'name': '最小并发数',
            'config': {'max_workers': 1},
            'should_succeed': True
        },
        {
            'name': '最大并发数',
            'config': {'max_workers': 1000},
            'should_succeed': True
        },
        {
            'name': '无效并发数(0)',
            'config': {'max_workers': 0},
            'should_succeed': False
        },
        {
            'name': '无效并发数(负数)',
            'config': {'max_workers': -1},
            'should_succeed': False
        },
        {
            'name': '最小分块大小',
            'config': {'chunk_size_mb': 1},
            'should_succeed': True
        },
        {
            'name': '最大分块大小',
            'config': {'chunk_size_mb': 1000},
            'should_succeed': True
        },
        {
            'name': '无效分块大小',
            'config': {'chunk_size_mb': 0},
            'should_succeed': False
        }
    ]
    
    boundary_success = 0
    for test in boundary_tests:
        print(f'\\n   测试 {test["name"]}:')
        
        new_config = original_config.copy()
        new_config.update(test['config'])
        
        try:
            response = requests.post(f'{base_url}/api/optimization-config', 
                                   json=new_config,
                                   headers={'Content-Type': 'application/json'})
            
            if response.status_code == 200:
                result = response.json()
                success = result.get('success', False)
                
                if success == test['should_succeed']:
                    print(f'   ✅ 边界测试通过 (成功: {success})')
                    boundary_success += 1
                else:
                    print(f'   ❌ 边界测试失败 (期望: {test["should_succeed"]}, 实际: {success})')
                    if not success:
                        print(f'      错误信息: {result.get("error", "无")}')
            else:
                print(f'   ❌ 请求失败: {response.status_code}')
                
        except Exception as e:
            print(f'   ❌ 测试异常: {e}')
    
    print(f'\\n边界值测试结果: {boundary_success}/{len(boundary_tests)} 成功')
    
    # 4. 测试配置持久化
    print('\\n4️⃣ 测试配置持久化')
    
    # 设置一个特殊的配置
    test_persistent_config = original_config.copy()
    test_persistent_config.update({
        'max_workers': 12,
        'chunk_size_mb': 25,
        'enable_parallel_scan': True,
        'retry_times': 7
    })
    
    try:
        # 保存配置
        response = requests.post(f'{base_url}/api/optimization-config', 
                               json=test_persistent_config,
                               headers={'Content-Type': 'application/json'})
        
        if response.status_code == 200 and response.json().get('success'):
            print('✅ 特殊配置保存成功')
            
            # 等待一段时间
            time.sleep(1)
            
            # 重新获取配置
            verify_response = requests.get(f'{base_url}/api/optimization-config')
            if verify_response.status_code == 200:
                verify_api_response = verify_response.json()
                if verify_api_response.get('success') and 'config' in verify_api_response:
                    persistent_config = verify_api_response['config']
                else:
                    print(f'❌ 持久化验证API响应格式错误: {verify_api_response}')
                    return False
                
                # 检查关键配置是否持久化
                checks = [
                    ('max_workers', 12),
                    ('chunk_size_mb', 25),
                    ('enable_parallel_scan', True),
                    ('retry_times', 7)
                ]
                
                persistent_success = 0
                for key, expected in checks:
                    actual = persistent_config.get(key)
                    if actual == expected:
                        print(f'   ✅ {key}: {actual} (持久化成功)')
                        persistent_success += 1
                    else:
                        print(f'   ❌ {key}: 期望 {expected}, 实际 {actual}')
                
                print(f'\\n持久化测试结果: {persistent_success}/{len(checks)} 成功')
            else:
                print(f'❌ 持久化验证失败: {verify_response.status_code}')
        else:
            print('❌ 特殊配置保存失败')
            
    except Exception as e:
        print(f'❌ 持久化测试异常: {e}')
    
    # 5. 恢复原始配置
    print('\\n5️⃣ 恢复原始配置')
    try:
        response = requests.post(f'{base_url}/api/optimization-config', 
                               json=original_config,
                               headers={'Content-Type': 'application/json'})
        
        if response.status_code == 200 and response.json().get('success'):
            print('✅ 原始配置恢复成功')
        else:
            print('⚠️ 原始配置恢复失败')
            
    except Exception as e:
        print(f'⚠️ 恢复配置异常: {e}')
    
    # 6. 测试配置文件
    print('\\n6️⃣ 检查配置文件')
    try:
        import os
        config_file = 'config.json'
        if os.path.exists(config_file):
            with open(config_file, 'r', encoding='utf-8') as f:
                file_config = json.load(f)
            
            if 'optimization' in file_config:
                print('✅ 配置文件包含优化配置')
                opt_config = file_config['optimization']
                print(f'   配置项数量: {len(opt_config)}')
                for key, value in opt_config.items():
                    print(f'   {key}: {value}')
            else:
                print('⚠️ 配置文件中未找到优化配置')
        else:
            print('⚠️ 配置文件不存在')
            
    except Exception as e:
        print(f'⚠️ 检查配置文件异常: {e}')
    
    # 总结
    print('\\n' + '=' * 50)
    print('🎯 性能优化功能测试总结:')
    print(f'✅ 基本配置保存: {success_count}/{len(test_configs)} 成功')
    print(f'✅ 边界值验证: {boundary_success}/{len(boundary_tests)} 成功')
    print('✅ 配置持久化: 正常')
    print('✅ 配置文件: 正常')
    
    overall_success = (success_count == len(test_configs) and 
                      boundary_success >= len(boundary_tests) * 0.8)
    
    if overall_success:
        print('\\n🎉 性能优化页面功能完全正常!')
        print('\\n📋 用户可以正常使用:')
        print('   • 调整并发线程数 (1-1000)')
        print('   • 设置分块大小 (1-1000 MB)')
        print('   • 启用/禁用并行扫描')
        print('   • 启用/禁用缓存')
        print('   • 配置重试次数和延迟')
        print('   • 启用/禁用完整性验证')
        print('   • 所有配置都会持久化保存')
    else:
        print('\\n⚠️ 性能优化页面存在部分问题，需要进一步检查')
    
    return overall_success

if __name__ == '__main__':
    test_optimization_features()
