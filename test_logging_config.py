#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试日志配置
"""

import logging
import sys

def test_logging():
    """测试日志配置"""
    print("🧪 测试日志配置")
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    # 测试不同模块的日志
    loggers = [
        'unified_task_manager',
        'efficient_sync_engine',
        'database_manager'
    ]
    
    for logger_name in loggers:
        logger = logging.getLogger(logger_name)
        logger.info(f"测试 {logger_name} 日志输出")
        print(f"  {logger_name} 日志级别: {logger.level}")
        print(f"  {logger_name} 处理器数量: {len(logger.handlers)}")
    
    # 测试EfficientSyncEngine
    try:
        from efficient_sync_engine import EfficientSyncEngine
        engine = EfficientSyncEngine()
        engine.logger.info("测试 EfficientSyncEngine 日志输出")
        print("✅ EfficientSyncEngine 日志测试成功")
    except Exception as e:
        print(f"❌ EfficientSyncEngine 日志测试失败: {e}")

if __name__ == "__main__":
    test_logging()
