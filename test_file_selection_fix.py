#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试文件选择功能修复
"""

import logging
import re

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def test_html_structure():
    """测试HTML结构"""
    logger = setup_logging()
    
    try:
        # 读取HTML文件
        with open('web/index.html', 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        logger.info("✅ HTML文件读取成功")
        
        # 检查关键元素
        required_elements = [
            ('fileBrowserModal', '文件浏览器模态框'),
            ('downloadBtn', '下载按钮'),
            ('fileList', '文件列表容器'),
            ('fileBrowserBreadcrumb', '面包屑导航'),
        ]
        
        for element_id, description in required_elements:
            if f'id="{element_id}"' in html_content:
                logger.info(f"✅ {description} 存在: {element_id}")
            else:
                logger.error(f"❌ {description} 缺失: {element_id}")
                return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试HTML结构失败: {e}")
        return False

def test_javascript_functions():
    """测试JavaScript函数"""
    logger = logging.getLogger(__name__)
    
    try:
        # 读取JavaScript文件
        with open('web/app.js', 'r', encoding='utf-8') as f:
            js_content = f.read()
        
        logger.info("✅ JavaScript文件读取成功")
        
        # 检查关键函数
        required_functions = [
            ('function updateDownloadButton()', '更新下载按钮函数'),
            ('function downloadSelectedFiles()', '下载选中文件函数'),
            ('function toggleAllFiles(', '全选文件函数'),
            ('function escapeJsString(', '字符串转义函数'),
            ('onchange="updateDownloadButton()"', '复选框变化事件'),
        ]
        
        for pattern, description in required_functions:
            if pattern in js_content:
                logger.info(f"✅ {description} 存在")
            else:
                logger.error(f"❌ {description} 缺失")
                return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试JavaScript函数失败: {e}")
        return False

def test_css_styles():
    """测试CSS样式"""
    logger = logging.getLogger(__name__)
    
    try:
        # 读取CSS文件
        with open('web/style.css', 'r', encoding='utf-8') as f:
            css_content = f.read()
        
        logger.info("✅ CSS文件读取成功")
        
        # 检查关键样式
        required_styles = [
            ('.file-checkbox', '文件复选框样式'),
            ('.checkbox-container', '复选框容器样式'),
            ('.checkmark', '复选框标记样式'),
            ('.file-item', '文件项样式'),
            ('input:checked', '选中状态样式'),
        ]
        
        for selector, description in required_styles:
            if selector in css_content:
                logger.info(f"✅ {description} 存在")
            else:
                logger.error(f"❌ {description} 缺失")
                return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试CSS样式失败: {e}")
        return False

def test_file_generation_logic():
    """测试文件生成逻辑"""
    logger = logging.getLogger(__name__)
    
    try:
        # 读取JavaScript文件
        with open('web/app.js', 'r', encoding='utf-8') as f:
            js_content = f.read()
        
        # 查找文件项生成的代码
        file_item_pattern = r'<div class="file-item.*?</div>\s*</div>\s*</div>'
        matches = re.findall(file_item_pattern, js_content, re.DOTALL)
        
        if matches:
            logger.info(f"✅ 找到文件项生成代码: {len(matches)} 处")
            
            # 检查第一个匹配项的内容
            first_match = matches[0]
            
            # 检查关键元素
            checks = [
                ('file-checkbox', '复选框容器'),
                ('checkbox-container', '复选框标签'),
                ('file-checkbox-input', '复选框输入'),
                ('onchange="updateDownloadButton()"', '变化事件'),
                ('checkmark', '复选框标记'),
            ]
            
            for pattern, description in checks:
                if pattern in first_match:
                    logger.info(f"✅ 文件项包含{description}")
                else:
                    logger.warning(f"⚠️ 文件项缺少{description}")
        else:
            logger.error("❌ 未找到文件项生成代码")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试文件生成逻辑失败: {e}")
        return False

def test_event_handling():
    """测试事件处理"""
    logger = logging.getLogger(__name__)
    
    try:
        # 读取JavaScript文件
        with open('web/app.js', 'r', encoding='utf-8') as f:
            js_content = f.read()
        
        # 检查事件处理相关代码
        event_patterns = [
            (r'onchange="updateDownloadButton\(\)"', '复选框变化事件'),
            (r'onclick="downloadSelectedFiles\(\)"', '下载按钮点击事件'),
            (r'onclick="toggleAllFiles\(', '全选按钮点击事件'),
            (r'onclick="navigateToPath\(', '导航点击事件'),
        ]
        
        for pattern, description in event_patterns:
            matches = re.findall(pattern, js_content)
            if matches:
                logger.info(f"✅ {description} 存在: {len(matches)} 处")
            else:
                logger.warning(f"⚠️ {description} 可能缺失")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试事件处理失败: {e}")
        return False

def analyze_potential_issues():
    """分析潜在问题"""
    logger = logging.getLogger(__name__)
    
    try:
        logger.info("🔍 分析潜在问题...")
        
        # 读取JavaScript文件
        with open('web/app.js', 'r', encoding='utf-8') as f:
            js_content = f.read()
        
        potential_issues = []
        
        # 检查是否有语法错误
        if "SyntaxError" in js_content:
            potential_issues.append("JavaScript中可能存在语法错误")
        
        # 检查是否有未转义的字符串
        unescaped_patterns = [
            r"onclick=['\"][^'\"]*\$\{[^}]*\}[^'\"]*['\"]",
            r"value=['\"][^'\"]*\$\{[^}]*\}[^'\"]*['\"]",
        ]
        
        for pattern in unescaped_patterns:
            matches = re.findall(pattern, js_content)
            if matches:
                potential_issues.append(f"发现可能未正确转义的字符串: {len(matches)} 处")
        
        # 检查是否有重复的ID
        id_pattern = r'id="([^"]+)"'
        ids = re.findall(id_pattern, js_content)
        duplicate_ids = [id for id in set(ids) if ids.count(id) > 1]
        if duplicate_ids:
            potential_issues.append(f"发现重复的ID: {duplicate_ids}")
        
        if potential_issues:
            logger.warning("⚠️ 发现潜在问题:")
            for issue in potential_issues:
                logger.warning(f"  - {issue}")
        else:
            logger.info("✅ 未发现明显的潜在问题")
        
        return len(potential_issues) == 0
        
    except Exception as e:
        logger.error(f"❌ 分析潜在问题失败: {e}")
        return False

def main():
    """主函数"""
    logger = setup_logging()
    
    logger.info("🧪 文件选择功能诊断测试")
    logger.info("=" * 60)
    
    tests = [
        ("HTML结构测试", test_html_structure),
        ("JavaScript函数测试", test_javascript_functions),
        ("CSS样式测试", test_css_styles),
        ("文件生成逻辑测试", test_file_generation_logic),
        ("事件处理测试", test_event_handling),
        ("潜在问题分析", analyze_potential_issues),
    ]
    
    passed_tests = 0
    
    for test_name, test_func in tests:
        logger.info(f"\n📋 执行测试: {test_name}")
        
        try:
            if test_func():
                logger.info(f"✅ {test_name} 通过")
                passed_tests += 1
            else:
                logger.error(f"❌ {test_name} 失败")
        except Exception as e:
            logger.error(f"❌ {test_name} 异常: {e}")
    
    logger.info(f"\n📊 测试结果: {passed_tests}/{len(tests)} 通过")
    
    if passed_tests == len(tests):
        logger.info("🎉 所有测试通过！文件选择功能应该正常工作")
        logger.info("")
        logger.info("💡 如果仍然无法选择文件，请检查:")
        logger.info("  1. 浏览器控制台是否有JavaScript错误")
        logger.info("  2. 复选框是否被其他元素遮挡")
        logger.info("  3. CSS样式是否正确加载")
        logger.info("  4. 事件监听器是否正确绑定")
        
        return 0
    else:
        logger.error("❌ 部分测试失败，需要修复相关问题")
        return 1

if __name__ == "__main__":
    exit(main())
