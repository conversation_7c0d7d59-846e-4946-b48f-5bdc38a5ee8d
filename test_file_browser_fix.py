#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试文件浏览器修复
"""

import logging
import tempfile
import os
from datetime import datetime, timezone

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def test_datetime_handling():
    """测试日期时间处理"""
    logger = setup_logging()
    
    try:
        from file_browser_manager import FileBrowserManager
        from unified_config_manager import UnifiedConfigManager
        
        config_manager = UnifiedConfigManager()
        browser = FileBrowserManager(config_manager)
        
        logger.info("✅ 文件浏览器管理器创建成功")
        
        # 测试不同类型的日期时间
        test_cases = [
            None,
            "2023-12-01T10:30:00Z",
            "2023-12-01T10:30:00+08:00",
            datetime.now(),
            datetime.now(timezone.utc),
            "invalid-date",
            123456789
        ]
        
        for i, test_dt in enumerate(test_cases):
            try:
                result = browser._safe_format_datetime(test_dt)
                logger.info(f"✅ 测试用例 {i+1}: {test_dt} -> {result}")
            except Exception as e:
                logger.error(f"❌ 测试用例 {i+1} 失败: {test_dt} -> {e}")
                return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试日期时间处理失败: {e}")
        return False

def test_file_metadata_handling():
    """测试文件元数据处理"""
    logger = logging.getLogger(__name__)
    
    try:
        from file_browser_manager import FileBrowserManager
        from unified_config_manager import UnifiedConfigManager
        
        config_manager = UnifiedConfigManager()
        browser = FileBrowserManager(config_manager)
        
        # 模拟不同类型的文件元数据对象
        class MockFileMetadata1:
            def __init__(self):
                self.key = "test/file1.txt"
                self.size = 1024
                self.last_modified = datetime.now()
        
        class MockFileMetadata2:
            def __init__(self):
                self.path = "test/file2.txt"
                self.size = 2048
                self.last_modified = "2023-12-01T10:30:00Z"
        
        class MockFileMetadata3:
            def __init__(self):
                self.key = "test/file3.txt"
                # 缺少size和last_modified
        
        test_metadata = [
            MockFileMetadata1(),
            MockFileMetadata2(),
            MockFileMetadata3(),
            "string_metadata",
            None
        ]
        
        for i, meta in enumerate(test_metadata):
            try:
                if meta is None:
                    continue
                    
                # 测试安全获取属性
                file_path = getattr(meta, 'key', '') or getattr(meta, 'path', '') or str(meta)
                size = getattr(meta, 'size', 0)
                last_modified = browser._safe_format_datetime(getattr(meta, 'last_modified', None))
                
                logger.info(f"✅ 元数据 {i+1}: path={file_path}, size={size}, modified={last_modified}")
                
            except Exception as e:
                logger.error(f"❌ 元数据 {i+1} 处理失败: {e}")
                return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试文件元数据处理失败: {e}")
        return False

def test_async_iteration_handling():
    """测试异步迭代处理"""
    logger = logging.getLogger(__name__)
    
    try:
        # 模拟不同类型的返回结果
        test_results = [
            [1, 2, 3],  # 普通列表
            (1, 2, 3),  # 元组
            "single_item",  # 单个项目
        ]
        
        for i, result in enumerate(test_results):
            try:
                items = []
                
                # 模拟文件浏览器中的逻辑
                if hasattr(result, '__aiter__'):
                    logger.info(f"结果 {i+1}: 异步迭代器")
                elif hasattr(result, '__iter__') and not isinstance(result, str):
                    logger.info(f"结果 {i+1}: 普通迭代器")
                    for item in result:
                        items.append(item)
                else:
                    logger.info(f"结果 {i+1}: 单个项目")
                    if isinstance(result, list):
                        items.extend(result)
                    else:
                        items.append(result)
                
                logger.info(f"✅ 处理结果 {i+1}: {items}")
                
            except Exception as e:
                logger.error(f"❌ 处理结果 {i+1} 失败: {e}")
                return False
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试异步迭代处理失败: {e}")
        return False

def test_local_storage_browsing():
    """测试本地存储浏览（简化版）"""
    logger = logging.getLogger(__name__)
    
    try:
        from file_browser_manager import FileBrowserManager
        from unified_config_manager import UnifiedConfigManager
        
        config_manager = UnifiedConfigManager()
        browser = FileBrowserManager(config_manager)
        
        # 创建临时测试目录
        temp_dir = tempfile.mkdtemp()
        logger.info(f"创建测试目录: {temp_dir}")
        
        # 创建测试文件
        test_files = ['test1.txt', 'test2.pdf']
        for filename in test_files:
            file_path = os.path.join(temp_dir, filename)
            with open(file_path, 'w') as f:
                f.write(f"Test content for {filename}")
        
        # 创建本地存储配置
        test_storage_id = "test_local_storage_fix"
        config_manager.add_source(test_storage_id, "local", {
            'name': "测试本地存储修复",
            'description': "用于测试文件浏览器修复的本地存储",
            'root_path': temp_dir
        })
        
        # 测试浏览功能
        result = browser.browse_storage(test_storage_id, 'source', '')
        
        if result['success']:
            files = result['files']
            logger.info(f"✅ 成功浏览目录，找到 {len(files)} 个项目")
            
            for file_info in files:
                logger.info(f"  文件: {file_info['name']}, 类型: {file_info['type']}, 大小: {file_info['size']}")
        else:
            logger.error(f"❌ 浏览目录失败: {result['message']}")
            return False
        
        # 清理测试数据
        import shutil
        shutil.rmtree(temp_dir)
        config_manager.remove_source(test_storage_id)
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试本地存储浏览失败: {e}")
        return False

def main():
    """主函数"""
    logger = setup_logging()
    
    logger.info("🧪 文件浏览器修复测试")
    logger.info("=" * 60)
    
    tests = [
        ("日期时间处理测试", test_datetime_handling),
        ("文件元数据处理测试", test_file_metadata_handling),
        ("异步迭代处理测试", test_async_iteration_handling),
        ("本地存储浏览测试", test_local_storage_browsing),
    ]
    
    passed_tests = 0
    
    for test_name, test_func in tests:
        logger.info(f"\n📋 执行测试: {test_name}")
        
        try:
            if test_func():
                logger.info(f"✅ {test_name} 通过")
                passed_tests += 1
            else:
                logger.error(f"❌ {test_name} 失败")
        except Exception as e:
            logger.error(f"❌ {test_name} 异常: {e}")
    
    logger.info(f"\n📊 测试结果: {passed_tests}/{len(tests)} 通过")
    
    if passed_tests == len(tests):
        logger.info("🎉 所有测试通过！文件浏览器修复成功")
        logger.info("")
        logger.info("📋 修复内容:")
        logger.info("  ✅ 修复了异步迭代器兼容性问题")
        logger.info("  ✅ 修复了时区处理问题")
        logger.info("  ✅ 增强了文件元数据处理的健壮性")
        logger.info("  ✅ 改进了错误处理和日志记录")
        logger.info("")
        logger.info("🚀 现在可以:")
        logger.info("  1. 正常浏览所有类型的存储")
        logger.info("  2. 处理各种格式的日期时间")
        logger.info("  3. 兼容不同的存储适配器返回格式")
        logger.info("  4. 安全处理异常情况")
        
        return 0
    else:
        logger.error("❌ 部分测试失败，需要进一步修复")
        return 1

if __name__ == "__main__":
    exit(main())
