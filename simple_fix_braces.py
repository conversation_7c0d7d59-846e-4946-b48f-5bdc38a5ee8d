#!/usr/bin/env python3
"""
简单直接的大括号修复
将所有JavaScript上下文中的单个大括号转换为双大括号
"""

import re
import shutil
from pathlib import Path

def simple_fix_braces(file_path):
    """简单修复大括号问题"""
    
    # 备份原文件
    backup_path = str(file_path) + '.simple_fix_backup'
    shutil.copy2(str(file_path), backup_path)
    print(f"已备份原文件到: {backup_path}")
    
    # 读取文件内容
    with open(str(file_path), 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 找到所有在JavaScript字符串模板中的 ${...} 并转换为 ${{...}}
    # 但要避免已经是 ${{...}} 的情况
    
    # 方法：使用更精确的正则表达式
    # 匹配 ${...} 但不匹配 ${{...}} 或 ${{{...}}}
    
    # 首先处理所有单个 ${...} 
    pattern = r'(?<!\{)\$\{([^{}]*(?:\{[^{}]*\}[^{}]*)*)\}(?!\})'
    
    def replace_single_brace(match):
        content = match.group(1)
        return f'${{{{{content}}}}}'
    
    # 统计修复前的数量
    matches = re.findall(pattern, content)
    print(f"发现 {len(matches)} 个需要修复的单个大括号")
    
    # 执行替换
    fixed_content = re.sub(pattern, replace_single_brace, content)
    
    # 统计修复后的结果
    remaining_matches = re.findall(pattern, fixed_content)
    fixed_count = len(matches) - len(remaining_matches)
    
    # 写入修复后的内容
    with open(str(file_path), 'w', encoding='utf-8') as f:
        f.write(fixed_content)
    
    print(f"修复完成:")
    print(f"  - 修复数量: {fixed_count}")
    print(f"  - 剩余问题: {len(remaining_matches)}")
    
    if len(remaining_matches) == 0:
        print("✅ 所有单个大括号问题已修复!")
    else:
        print("⚠️ 仍有部分问题未修复")
        for i, match in enumerate(remaining_matches[:5]):
            print(f"    {i+1}. ${{{match}}}")
    
    return fixed_count, len(remaining_matches)

def main():
    """主函数"""
    file_path = Path("complete_web_interface.py")
    
    if not file_path.exists():
        print(f"❌ 文件不存在: {file_path}")
        return
    
    print("🔧 开始简单修复大括号问题...")
    print("=" * 50)
    
    try:
        fixed_count, remaining_count = simple_fix_braces(file_path)
        
        print("\n" + "=" * 50)
        print("🎯 修复总结:")
        print(f"  - 文件: {file_path}")
        print(f"  - 修复数量: {fixed_count}")
        print(f"  - 剩余问题: {remaining_count}")
        
        if remaining_count == 0:
            print("\n🎉 修复成功! 所有大括号问题已解决")
            print("💡 请重启Web服务器并测试功能")
        else:
            print(f"\n⚠️ 仍有 {remaining_count} 个问题需要检查")
            
    except Exception as e:
        print(f"❌ 修复过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
