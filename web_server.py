#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LightRek 统一存储同步工具 - Web服务器
增强版：包含完整的同步任务和性能优化功能
"""

import threading
import json
import uuid
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
from complete_web_interface import CompleteWebInterface


class WebServer:
    """Web服务器类 - 增强版"""

    def __init__(self, config_manager, task_manager, db_manager=None, port=8001):
        """
        初始化Web服务器

        Args:
            config_manager: 配置管理器
            task_manager: 任务管理器
            db_manager: 数据库管理器（可选）
            port: 服务器端口
        """
        self.config_manager = config_manager
        self.task_manager = task_manager
        self.db_manager = db_manager
        self.port = port
        self.server = None
        self.server_thread = None
        self.running = False
    
    def start(self):
        """启动Web服务器"""
        try:
            # 创建完整Web界面
            web_interface = CompleteWebInterface(
                self.config_manager,
                self.task_manager,
                self.port
            )

            # 直接启动Web界面（它有自己的服务器）
            web_interface.start()
            self.server = web_interface.server
            self.server_thread = web_interface.server_thread

            self.running = True
            return True

        except Exception as e:
            print(f"❌ Web服务器启动失败: {e}")
            return False

    def start_enhanced(self):
        """启动增强版Web服务器（包含完整任务管理和性能优化功能）"""
        try:
            # 创建增强的请求处理器
            handler_class = self._create_enhanced_handler()

            # 创建HTTP服务器
            self.server = HTTPServer(('localhost', self.port), handler_class)

            # 在单独线程中运行服务器
            self.server_thread = threading.Thread(target=self._run_server, daemon=True)
            self.server_thread.start()

            self.running = True
            print(f"🌐 增强版Web服务器已启动: http://localhost:{self.port}")
            return True

        except Exception as e:
            print(f"❌ 增强版Web服务器启动失败: {e}")
            return False
    
    def stop(self):
        """停止Web服务器"""
        if self.server and self.running:
            try:
                self.server.shutdown()
                self.server.server_close()
                self.running = False
                print("🛑 Web服务器已停止")
            except Exception as e:
                print(f"❌ 停止Web服务器时出错: {e}")
    
    def _run_server(self):
        """运行服务器（在单独线程中）"""
        try:
            self.server.serve_forever()
        except Exception as e:
            if self.running:  # 只有在运行状态下才报告错误
                print(f"❌ Web服务器运行时出错: {e}")
    
    def is_running(self):
        """检查服务器是否正在运行"""
        return self.running and self.server_thread and self.server_thread.is_alive()
    
    def get_url(self):
        """获取Web界面URL"""
        return f"http://localhost:{self.port}"
    
    def restart(self):
        """重启Web服务器"""
        print("🔄 正在重启Web服务器...")
        self.stop()

        # 等待服务器完全停止
        if self.server_thread:
            self.server_thread.join(timeout=5)

        return self.start()

    def _create_enhanced_handler(self):
        """创建增强的请求处理器类"""
        # 保存管理器引用
        web_server_instance = self

        class EnhancedRequestHandler(BaseHTTPRequestHandler):
            """增强的请求处理器 - 包含完整的任务管理和性能优化功能"""

            def __init__(self, *args, **kwargs):
                # 获取管理器引用
                self.config_manager = web_server_instance.config_manager
                self.task_manager = web_server_instance.task_manager
                self.db_manager = web_server_instance.db_manager
                super().__init__(*args, **kwargs)

            def do_GET(self):
                """处理GET请求"""
                if self.path == '/' or self.path == '/dashboard':
                    self._serve_dashboard()
                elif self.path == '/api/sources':
                    self._serve_sources()
                elif self.path == '/api/targets':
                    self._serve_targets()
                elif self.path == '/api/tasks':
                    self._serve_tasks()
                elif self.path == '/api/task-status':
                    self._serve_task_status()
                elif self.path == '/api/task-executions':
                    self._serve_task_executions()
                elif self.path == '/api/statistics':
                    self._serve_statistics()
                elif self.path == '/api/optimization-config':
                    self._serve_optimization_config()
                elif self.path.startswith('/api/task-details/'):
                    task_id = self.path.split('/')[-1]
                    self._serve_task_details(task_id)
                elif self.path.startswith('/api/task-logs/'):
                    execution_id = self.path.split('/')[-1]
                    self._serve_task_logs(execution_id)
                else:
                    self._serve_static_file()

            def do_POST(self):
                """处理POST请求"""
                content_length = int(self.headers.get('Content-Length', 0))
                post_data = self.rfile.read(content_length)

                try:
                    data = json.loads(post_data.decode('utf-8')) if post_data else {}
                except:
                    data = {}

                if self.path == '/api/sources':
                    self._add_source(data)
                elif self.path == '/api/targets':
                    self._add_target(data)
                elif self.path == '/api/tasks':
                    self._add_task(data)
                elif self.path == '/api/test-connection':
                    self._test_connection(data)
                elif self.path.startswith('/api/tasks/') and self.path.endswith('/run'):
                    task_id = self.path.split('/')[-2]
                    self._run_task(task_id)
                elif self.path.startswith('/api/tasks/') and self.path.endswith('/stop'):
                    task_id = self.path.split('/')[-2]
                    self._stop_task(task_id)
                elif self.path == '/api/optimization-config':
                    self._save_optimization_config(data)
                else:
                    self._serve_404()

            def do_DELETE(self):
                """处理DELETE请求"""
                if self.path.startswith('/api/sources/'):
                    source_id = self.path.split('/')[-1]
                    self._delete_source(source_id)
                elif self.path.startswith('/api/targets/'):
                    target_id = self.path.split('/')[-1]
                    self._delete_target(target_id)
                elif self.path.startswith('/api/tasks/'):
                    task_id = self.path.split('/')[-1]
                    self._delete_task(task_id)
                else:
                    self._serve_404()

            def _send_json(self, data):
                """发送JSON响应"""
                self.send_response(200)
                self.send_header('Content-Type', 'application/json; charset=utf-8')
                self.send_header('Access-Control-Allow-Origin', '*')
                self.end_headers()
                self.wfile.write(json.dumps(data, ensure_ascii=False).encode('utf-8'))

            def _serve_404(self):
                """返回404错误"""
                self.send_response(404)
                self.send_header('Content-Type', 'text/plain; charset=utf-8')
                self.end_headers()
                self.wfile.write(b'404 Not Found')

            def _serve_dashboard(self):
                """提供仪表盘页面"""
                try:
                    # 这里可以返回简单的HTML或重定向到静态文件
                    html = """
                    <!DOCTYPE html>
                    <html>
                    <head>
                        <title>LightRek Dashboard</title>
                        <meta charset="utf-8">
                    </head>
                    <body>
                        <h1>LightRek 统一存储同步工具</h1>
                        <p>增强版Web服务器正在运行</p>
                        <p><a href="/api/statistics">查看统计信息</a></p>
                        <p><a href="/api/tasks">查看任务列表</a></p>
                        <p><a href="/api/optimization-config">查看优化配置</a></p>
                    </body>
                    </html>
                    """
                    self.send_response(200)
                    self.send_header('Content-Type', 'text/html; charset=utf-8')
                    self.end_headers()
                    self.wfile.write(html.encode('utf-8'))
                except Exception as e:
                    self._send_json({'success': False, 'message': f'加载仪表盘失败: {str(e)}'})

            def _serve_static_file(self):
                """提供静态文件（简化版）"""
                # 这里可以实现静态文件服务，或者返回404
                self._serve_404()

            # ==================== 数据源管理 ====================
            def _serve_sources(self):
                """获取数据源列表"""
                try:
                    sources = self.config_manager.get_all_sources()
                    self._send_json({'success': True, 'sources': sources})
                except Exception as e:
                    self._send_json({'success': False, 'message': str(e), 'sources': {}})

            def _add_source(self, data):
                """添加数据源"""
                try:
                    source_id = str(uuid.uuid4())
                    storage_type = data.get('type', 's3')
                    self.config_manager.add_source(source_id, storage_type, data)
                    self._send_json({'success': True, 'message': '数据源添加成功', 'source_id': source_id})
                except Exception as e:
                    self._send_json({'success': False, 'message': f'添加数据源失败: {str(e)}'})

            def _delete_source(self, source_id):
                """删除数据源"""
                try:
                    self.config_manager.remove_source(source_id)
                    self._send_json({'success': True, 'message': '数据源删除成功'})
                except Exception as e:
                    self._send_json({'success': False, 'message': f'删除数据源失败: {str(e)}'})

            # ==================== 目标存储管理 ====================
            def _serve_targets(self):
                """获取目标存储列表"""
                try:
                    targets = self.config_manager.get_all_targets()
                    self._send_json({'success': True, 'targets': targets})
                except Exception as e:
                    self._send_json({'success': False, 'message': str(e), 'targets': {}})

            def _add_target(self, data):
                """添加目标存储"""
                try:
                    target_id = str(uuid.uuid4())
                    storage_type = data.get('type', 's3')
                    self.config_manager.add_target(target_id, storage_type, data)
                    self._send_json({'success': True, 'message': '目标存储添加成功', 'target_id': target_id})
                except Exception as e:
                    self._send_json({'success': False, 'message': f'添加目标存储失败: {str(e)}'})

            def _delete_target(self, target_id):
                """删除目标存储"""
                try:
                    self.config_manager.remove_target(target_id)
                    self._send_json({'success': True, 'message': '目标存储删除成功'})
                except Exception as e:
                    self._send_json({'success': False, 'message': f'删除目标存储失败: {str(e)}'})

            # ==================== 同步任务管理 ====================
            def _serve_tasks(self):
                """获取同步任务列表"""
                try:
                    tasks = self.config_manager.get_all_tasks()
                    # 增强任务信息，添加运行状态
                    enhanced_tasks = {}
                    for task_id, task_config in tasks.items():
                        enhanced_task = task_config.copy()
                        # 获取任务运行状态
                        if hasattr(self.task_manager, 'get_task_status'):
                            status = self.task_manager.get_task_status(task_id)
                            if status:
                                enhanced_task.update(status)
                        enhanced_tasks[task_id] = enhanced_task

                    self._send_json({'success': True, 'tasks': enhanced_tasks})
                except Exception as e:
                    self._send_json({'success': False, 'message': str(e), 'tasks': {}})

            def _add_task(self, data):
                """创建同步任务"""
                try:
                    # 验证必需字段
                    required_fields = ['name', 'description', 'source_id', 'target_id']
                    for field in required_fields:
                        if field not in data:
                            raise ValueError(f'缺少必需字段: {field}')

                    # 使用任务管理器创建任务
                    if hasattr(self.task_manager, 'create_task'):
                        task_id = self.task_manager.create_task(
                            name=data['name'],
                            description=data['description'],
                            source_id=data['source_id'],
                            target_id=data['target_id'],
                            **{k: v for k, v in data.items() if k not in required_fields}
                        )
                    else:
                        # 回退到配置管理器
                        task_id = str(uuid.uuid4())
                        self.config_manager.add_task(task_id, data)

                    self._send_json({'success': True, 'message': '同步任务创建成功', 'task_id': task_id})
                except Exception as e:
                    self._send_json({'success': False, 'message': f'创建同步任务失败: {str(e)}'})

            def _delete_task(self, task_id):
                """删除同步任务"""
                try:
                    # 如果任务正在运行，先停止
                    if hasattr(self.task_manager, 'stop_task'):
                        self.task_manager.stop_task(task_id)

                    # 删除任务配置
                    if hasattr(self.task_manager, 'delete_task'):
                        self.task_manager.delete_task(task_id)
                    else:
                        self.config_manager.remove_task(task_id)

                    self._send_json({'success': True, 'message': '同步任务删除成功'})
                except Exception as e:
                    self._send_json({'success': False, 'message': f'删除同步任务失败: {str(e)}'})

            def _run_task(self, task_id):
                """运行同步任务"""
                try:
                    print(f"🔍 尝试运行任务: {task_id}")
                    print(f"📋 任务管理器类型: {type(self.task_manager)}")
                    print(f"🔧 任务管理器方法: {[method for method in dir(self.task_manager) if not method.startswith('_')]}")

                    if hasattr(self.task_manager, 'start_task'):
                        print("✅ 使用 start_task 方法")
                        success = self.task_manager.start_task(task_id)
                    elif hasattr(self.task_manager, 'run_task'):
                        print("✅ 使用 run_task 方法")
                        success = self.task_manager.run_task(task_id)
                    else:
                        available_methods = [method for method in dir(self.task_manager) if not method.startswith('_')]
                        raise Exception(f'任务管理器不支持运行任务。可用方法: {available_methods}')

                    if success:
                        self._send_json({'success': True, 'message': '任务启动成功'})
                    else:
                        self._send_json({'success': False, 'message': '任务启动失败'})
                except Exception as e:
                    print(f"❌ 任务启动异常: {e}")
                    self._send_json({'success': False, 'message': f'任务启动失败: {str(e)}'})

            def _stop_task(self, task_id):
                """停止同步任务"""
                try:
                    if hasattr(self.task_manager, 'stop_task'):
                        success = self.task_manager.stop_task(task_id)
                        if success:
                            self._send_json({'success': True, 'message': '任务停止成功'})
                        else:
                            self._send_json({'success': False, 'message': '任务停止失败'})
                    else:
                        self._send_json({'success': False, 'message': '任务管理器不支持停止任务'})
                except Exception as e:
                    self._send_json({'success': False, 'message': f'任务停止失败: {str(e)}'})

            def _serve_task_status(self):
                """获取任务状态"""
                try:
                    if hasattr(self.task_manager, 'get_all_task_status'):
                        status = self.task_manager.get_all_task_status()
                    elif hasattr(self.task_manager, 'task_status'):
                        status = self.task_manager.task_status
                    else:
                        status = {}

                    self._send_json({'success': True, 'status': status})
                except Exception as e:
                    self._send_json({'success': False, 'message': str(e), 'status': {}})

            def _serve_task_executions(self):
                """获取任务执行记录"""
                try:
                    executions = []
                    if self.db_manager and hasattr(self.db_manager, 'get_recent_executions'):
                        executions = self.db_manager.get_recent_executions(20)

                    self._send_json({'success': True, 'executions': executions})
                except Exception as e:
                    self._send_json({'success': False, 'message': str(e), 'executions': []})

            def _serve_task_details(self, task_id):
                """获取任务详细信息"""
                try:
                    tasks = self.config_manager.get_all_tasks()
                    if task_id in tasks:
                        task = tasks[task_id]
                        executions = []
                        if self.db_manager and hasattr(self.db_manager, 'get_task_executions'):
                            executions = self.db_manager.get_task_executions(task_id)

                        self._send_json({
                            'success': True,
                            'task': task,
                            'executions': executions
                        })
                    else:
                        self._send_json({'success': False, 'message': '任务不存在'})
                except Exception as e:
                    self._send_json({'success': False, 'message': str(e)})

            def _serve_task_logs(self, execution_id):
                """获取任务执行日志"""
                try:
                    logs = []
                    if self.db_manager and hasattr(self.db_manager, 'get_execution_logs'):
                        logs = self.db_manager.get_execution_logs(execution_id)

                    self._send_json({'success': True, 'logs': logs})
                except Exception as e:
                    self._send_json({'success': False, 'message': str(e), 'logs': []})

            def _serve_statistics(self):
                """获取统计信息"""
                try:
                    sources = self.config_manager.get_all_sources()
                    targets = self.config_manager.get_all_targets()
                    tasks = self.config_manager.get_all_tasks()

                    stats = {
                        'sources': len(sources),
                        'targets': len(targets),
                        'tasks': len(tasks),
                        'executions': 0
                    }

                    if self.db_manager and hasattr(self.db_manager, 'get_recent_executions'):
                        stats['executions'] = len(self.db_manager.get_recent_executions(1000))

                    self._send_json({'success': True, 'statistics': stats})
                except Exception as e:
                    self._send_json({'success': False, 'message': str(e), 'statistics': {}})

            # ==================== 性能优化配置 ====================
            def _serve_optimization_config(self):
                """获取性能优化配置"""
                try:
                    if hasattr(self.config_manager, 'get_optimization_config'):
                        config = self.config_manager.get_optimization_config()
                    else:
                        # 默认配置
                        config = {
                            'max_workers': 8,
                            'chunk_size_mb': 20,
                            'retry_times': 3,
                            'retry_delay': 2,
                            'enable_parallel_scan': True,
                            'enable_cache': True,
                            'cache_ttl_hours': 24,
                            'verify_integrity': True
                        }

                    self._send_json({'success': True, 'config': config})
                except Exception as e:
                    self._send_json({'success': False, 'message': str(e), 'config': {}})

            def _save_optimization_config(self, data):
                """保存性能优化配置"""
                try:
                    if hasattr(self.config_manager, 'update_optimization_settings'):
                        success = self.config_manager.update_optimization_settings(data)
                        if success:
                            self._send_json({'success': True, 'message': '优化配置保存成功'})
                        else:
                            self._send_json({'success': False, 'message': '优化配置验证失败，请检查参数值'})
                    else:
                        # 简单保存到配置中
                        if not hasattr(self.config_manager, 'config_data'):
                            self.config_manager.config_data = {}
                        if 'optimization' not in self.config_manager.config_data:
                            self.config_manager.config_data['optimization'] = {}

                        self.config_manager.config_data['optimization'].update(data)
                        self._send_json({'success': True, 'message': '优化配置保存成功'})
                except Exception as e:
                    self._send_json({'success': False, 'message': f'保存优化配置失败: {str(e)}'})

            # ==================== 连接测试 ====================
            def _test_connection(self, data):
                """测试存储连接"""
                try:
                    storage_type = data.get('type', 's3')

                    # 这里可以根据存储类型进行实际的连接测试
                    # 简化版本，直接返回成功
                    if storage_type in ['s3', 'sftp', 'smb', 'ftp', 'local']:
                        # 实际应该进行真实的连接测试
                        test_result = {
                            'success': True,
                            'message': f'{storage_type.upper()} 连接测试成功',
                            'details': {
                                'type': storage_type,
                                'latency': '50ms',
                                'status': 'connected'
                            }
                        }
                    else:
                        test_result = {
                            'success': False,
                            'message': f'不支持的存储类型: {storage_type}'
                        }

                    self._send_json(test_result)
                except Exception as e:
                    self._send_json({'success': False, 'message': f'连接测试失败: {str(e)}'})

            def log_message(self, format, *args):
                """重写日志方法，减少输出"""
                # 可以选择性地记录日志或完全静默
                pass

        return EnhancedRequestHandler


def create_web_server(config_manager, task_manager, db_manager=None, port=8001):
    """
    创建Web服务器实例的便捷函数
    
    Args:
        config_manager: 配置管理器
        task_manager: 任务管理器
        db_manager: 数据库管理器（可选）
        port: 服务器端口
    
    Returns:
        WebServer: Web服务器实例
    """
    return WebServer(config_manager, task_manager, db_manager, port)


if __name__ == "__main__":
    # 测试Web服务器
    print("🧪 测试Web服务器...")
    
    # 这里需要导入实际的管理器类进行测试
    try:
        from unified_config_manager import UnifiedConfigManager
        from unified_task_manager import UnifiedTaskManager
        from database_manager import DatabaseManager
        
        # 创建管理器实例
        config_manager = UnifiedConfigManager()
        task_manager = UnifiedTaskManager(config_manager)
        db_manager = DatabaseManager()
        
        # 创建并启动Web服务器
        web_server = create_web_server(config_manager, task_manager, db_manager)
        
        if web_server.start():
            print(f"✅ Web服务器测试成功，访问: {web_server.get_url()}")
            print("按 Ctrl+C 停止服务器")
            
            try:
                # 保持服务器运行
                while web_server.is_running():
                    import time
                    time.sleep(1)
            except KeyboardInterrupt:
                print("\n🛑 收到停止信号")
                web_server.stop()
        else:
            print("❌ Web服务器测试失败")
            
    except ImportError as e:
        print(f"❌ 导入模块失败: {e}")
        print("请确保所有依赖模块都已正确安装")
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
