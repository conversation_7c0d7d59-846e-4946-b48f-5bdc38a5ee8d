"""
SMB存储适配器 - 基于smbprotocol库实现
"""

from storage_abstraction import StorageAdapter, SMBStorageConfig, FileMetadata, ListResult, StorageType, StorageFactory
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import os
import ntpath
import uuid
import threading
import time
import queue

try:
    from smbprotocol.connection import Connection
    from smbprotocol.session import Session
    from smbprotocol.tree import TreeConnect
    from smbprotocol.open import (Open, CreateDisposition, CreateOptions, DirectoryAccessMask,
                                  FilePipePrinterAccessMask, ImpersonationLevel, ShareAccess)
    from smbprotocol.file_info import FileAttributes, FileInformationClass
    from smbprotocol import exceptions as smb_exceptions

    # 在smbprotocol中，File类实际上就是Open类
    File = Open

    # 使用FilePipePrinterAccessMask作为FileAccessMask的替代
    FileAccessMask = FilePipePrinterAccessMask

    SMBPROTOCOL_AVAILABLE = True
except ImportError as e:
    SMBPROTOCOL_AVAILABLE = False
    print(f"SMB适配器导入失败: {e}")


class SMBConnectionManager:
    """SMB连接管理器 - 单例模式，管理所有SMB连接"""

    _instance = None
    _lock = threading.Lock()

    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
                    cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        if hasattr(self, '_initialized') and self._initialized:
            return
        self._initialized = True
        self._download_lock = threading.Lock()  # 串行下载锁
        self._download_queue = queue.Queue()  # 下载队列
        self._active_downloads = 0
        self._max_concurrent_downloads = 1  # 限制为1个并发下载

    def get_download_lock(self):
        """获取下载锁，确保SMB下载串行执行"""
        return self._download_lock


class SMBStorageAdapter(StorageAdapter):
    """SMB存储适配器"""
    
    def __init__(self, config: SMBStorageConfig):
        if not SMBPROTOCOL_AVAILABLE:
            raise ImportError("smbprotocol库未安装，请运行: pip install smbprotocol")

        super().__init__(config)
        self.config: SMBStorageConfig = config
        self._connection = None
        self._session = None
        self._tree = None
        self._connection_manager = SMBConnectionManager()
    
    def _connect(self) -> bool:
        """建立SMB连接"""
        try:
            if self._connection is not None and self._connection.connected:
                return True
            
            # 建立连接
            self._connection = Connection(
                uuid.uuid4(),
                self.config.hostname,
                self.config.port
            )
            self._connection.connect()
            
            # 建立会话
            self._session = Session(self._connection, self.config.username, self.config.password)
            self._session.connect()
            
            # 连接到共享
            share_path = f"\\\\{self.config.hostname}\\{self.config.share_name}"
            self._tree = TreeConnect(self._session, share_path)
            self._tree.connect()
            
            return True
        except Exception as e:
            self._disconnect()
            raise Exception(f"SMB连接失败: {str(e)}")
    
    def _disconnect(self):
        """断开SMB连接"""
        if self._tree:
            try:
                self._tree.disconnect()
            except:
                pass
            self._tree = None
        
        if self._session:
            try:
                self._session.disconnect()
            except:
                pass
            self._session = None
        
        if self._connection:
            try:
                self._connection.disconnect()
            except:
                pass
            self._connection = None
    
    def _create_file_object(self, file_path: str, desired_access, create_disposition,
                           create_options=CreateOptions.FILE_NON_DIRECTORY_FILE,
                           file_attributes=FileAttributes.FILE_ATTRIBUTE_NORMAL):
        """创建文件对象的辅助方法"""
        file_obj = File(self._tree, file_path)
        file_obj.create(
            impersonation_level=ImpersonationLevel.Impersonation,
            desired_access=desired_access,
            file_attributes=file_attributes,
            share_access=ShareAccess.FILE_SHARE_READ | ShareAccess.FILE_SHARE_WRITE,
            create_disposition=create_disposition,
            create_options=create_options
        )
        return file_obj

    def _normalize_path(self, path: str) -> str:
        """规范化路径 - SMB协议专用"""
        if not path:
            # 空路径表示根目录
            return ""

        # 移除开头的路径分隔符
        path = path.lstrip('\\').lstrip('/')

        # 如果根路径不为空且不是根目录标识符，则组合路径
        root_path = self.config.root_path
        if root_path and root_path not in ('/', '\\', ''):
            root_path = root_path.lstrip('\\').lstrip('/')
            if root_path:
                path = f"{root_path}\\{path}" if path else root_path

        # SMB协议要求：
        # 1. 使用反斜杠作为路径分隔符
        # 2. 路径不能以反斜杠开头（相对于共享根目录）
        # 3. 路径不能以反斜杠结尾（除非是目录）
        path = path.replace('/', '\\')

        # 移除多余的分隔符
        while '\\\\' in path:
            path = path.replace('\\\\', '\\')

        # 移除开头和结尾的分隔符
        path = path.strip('\\')

        return path
    
    def _get_relative_path(self, full_path: str) -> str:
        """获取相对于根路径的路径"""
        root = self.config.root_path.rstrip('\\').rstrip('/')
        if full_path.startswith(root):
            relative = full_path[len(root):].lstrip('\\').lstrip('/')
            return relative.replace('\\', '/') if relative else ""
        return full_path.replace('\\', '/')
    
    def test_connection(self) -> Tuple[bool, str]:
        """测试连接"""
        try:
            self._connect()
            
            # 测试访问根目录
            root_path = self._normalize_path("")
            try:
                file_obj = self._create_file_object(
                    root_path,
                    desired_access=FileAccessMask.GENERIC_READ,
                    create_disposition=CreateDisposition.FILE_OPEN,
                    create_options=CreateOptions.FILE_DIRECTORY_FILE,
                    file_attributes=FileAttributes.FILE_ATTRIBUTE_DIRECTORY
                )
                # 简化：如果能成功打开就认为连接成功
                file_obj.close()
                return True, f"SMB连接成功，共享: \\\\{self.config.hostname}\\{self.config.share_name}"
            except smb_exceptions.SMBResponseException as e:
                if e.status == 0xC0000034:  # STATUS_OBJECT_NAME_NOT_FOUND
                    return False, f"根目录不存在: {root_path}"
                elif e.status == 0xC0000022:  # STATUS_ACCESS_DENIED
                    return False, f"无权限访问根目录: {root_path}"
                else:
                    return False, f"访问根目录失败: {str(e)}"
        except Exception as e:
            return False, f"SMB连接失败: {str(e)}"
        finally:
            self._disconnect()
    
    def list_files(self, prefix: str = "", max_keys: int = 1000, 
                   continuation_token: Optional[str] = None) -> ListResult:
        """列出文件"""
        try:
            self._connect()
            
            files = []
            start_path = self._normalize_path(prefix)
            
            # 如果指定了continuation_token，从该位置开始
            skip_until = continuation_token
            found_start = continuation_token is None
            
            def scan_directory(dir_path: str):
                nonlocal files, found_start, skip_until
                
                if len(files) >= max_keys:
                    return
                
                try:
                    # 打开目录
                    dir_obj = self._create_file_object(
                        dir_path,
                        desired_access=FileAccessMask.GENERIC_READ,
                        create_disposition=CreateDisposition.FILE_OPEN,
                        create_options=CreateOptions.FILE_DIRECTORY_FILE,
                        file_attributes=FileAttributes.FILE_ATTRIBUTE_DIRECTORY
                    )
                    
                    # 查询目录内容
                    try:
                        items = dir_obj.query_directory(
                            pattern="*",
                            file_information_class=FileInformationClass.FILE_DIRECTORY_INFORMATION
                        )
                        for item in items:
                            if len(files) >= max_keys:
                                break

                            # 获取文件名（通过字典方式访问）
                            file_name = item['file_name']
                            if hasattr(file_name, 'get_value'):
                                file_name = file_name.get_value()

                            # 修复文件名编码问题
                            if isinstance(file_name, bytes):
                                # 如果是bytes，尝试UTF-16解码
                                try:
                                    file_name = file_name.decode('utf-16le', errors='ignore')
                                except:
                                    try:
                                        file_name = file_name.decode('utf-8', errors='ignore')
                                    except:
                                        file_name = str(file_name)
                            elif isinstance(file_name, str):
                                # 如果是字符串但包含UTF-16编码，修复它
                                if '\x00' in file_name:
                                    try:
                                        # 移除null字符，这通常是UTF-16编码的残留
                                        file_name = file_name.replace('\x00', '')
                                    except:
                                        pass

                            # 跳过 . 和 ..
                            if file_name in ['.', '..']:
                                continue

                            item_path = ntpath.join(dir_path, file_name) if dir_path else file_name
                            relative_path = self._get_relative_path(item_path)

                            # 检查是否匹配前缀
                            if prefix and not relative_path.startswith(prefix.replace('\\', '/')):
                                continue

                            # 处理分页
                            if not found_start:
                                if relative_path == skip_until:
                                    found_start = True
                                continue

                            # 获取文件属性（通过字典方式访问）
                            file_attributes = item['file_attributes']
                            if hasattr(file_attributes, 'get_value'):
                                file_attributes = file_attributes.get_value()

                            # 获取文件大小
                            file_size = item['end_of_file']
                            if hasattr(file_size, 'get_value'):
                                file_size = file_size.get_value()

                            # 获取修改时间
                            last_write_time = item['last_write_time']
                            if hasattr(last_write_time, 'get_value'):
                                last_write_time = last_write_time.get_value()

                            if not (file_attributes & FileAttributes.FILE_ATTRIBUTE_DIRECTORY):
                                # 文件
                                try:
                                    # 检查last_write_time是否已经是datetime对象
                                    if isinstance(last_write_time, datetime):
                                        last_modified = last_write_time
                                    elif isinstance(last_write_time, (int, float)):
                                        # Windows FILETIME格式转换
                                        last_modified = datetime.fromtimestamp(last_write_time / 10000000 - 11644473600)
                                    else:
                                        last_modified = datetime.now()
                                except (ValueError, OSError, TypeError):
                                    last_modified = datetime.now()

                                file_meta = FileMetadata(
                                    key=relative_path,
                                    size=file_size,
                                    last_modified=last_modified,
                                    etag=None,  # SMB不支持ETag
                                    content_type='binary/octet-stream'
                                )
                                files.append(file_meta)
                            else:
                                # 递归扫描子目录
                                scan_directory(item_path)
                    except Exception as e:
                        # 如果查询目录失败，记录错误但继续
                        print(f"查询目录失败: {dir_path}, 错误: {e}")
                    
                    dir_obj.close()
                
                except smb_exceptions.SMBResponseException:
                    # 跳过无权限或其他错误的目录
                    pass
                except Exception:
                    # 跳过其他错误的目录
                    pass
            
            # 开始扫描
            scan_directory(start_path)
            
            # 确定是否还有更多文件
            is_truncated = len(files) >= max_keys
            next_token = files[-1].key if is_truncated and files else None
            
            return ListResult(
                files=files,
                is_truncated=is_truncated,
                next_token=next_token
            )
        
        except Exception as e:
            raise Exception(f"列出文件失败: {str(e)}")
        finally:
            self._disconnect()
    
    def get_file(self, key: str) -> Optional[bytes]:
        """下载文件 - 使用线程隔离避免状态污染"""
        # 使用连接管理器确保串行下载
        with self._connection_manager.get_download_lock():
            return self._download_with_thread_isolation(key)

    def _download_with_thread_isolation(self, key: str) -> Optional[bytes]:
        """使用线程隔离下载文件"""
        import threading
        import queue

        result_queue = queue.Queue()

        def isolated_worker():
            """隔离的工作线程"""
            try:
                # 在独立线程中创建全新的适配器
                from storage_abstraction import StorageFactory

                # 创建全新的配置对象，避免引用共享
                fresh_config = SMBStorageConfig(
                    storage_type=self.config.storage_type,
                    hostname=self.config.hostname,
                    port=self.config.port,
                    username=self.config.username,
                    password=self.config.password,
                    domain=self.config.domain,
                    share_name=self.config.share_name,
                    root_path=self.config.root_path
                )

                # 创建全新的适配器实例
                fresh_adapter = StorageFactory.create_adapter(fresh_config)

                # 使用简单下载方法
                data = fresh_adapter._simple_download(key)
                result_queue.put(('success', data))

            except Exception as e:
                result_queue.put(('error', str(e)))

        # 启动隔离的工作线程
        worker_thread = threading.Thread(target=isolated_worker)
        worker_thread.start()

        # 等待结果
        worker_thread.join(timeout=30)  # 30秒超时

        if worker_thread.is_alive():
            print(f"下载超时: {key}")
            return None

        try:
            result_type, result_data = result_queue.get_nowait()
            if result_type == 'success':
                return result_data
            else:
                print(f"线程隔离下载失败 {key}: {result_data}")
                return None
        except queue.Empty:
            print(f"线程无结果: {key}")
            return None

    def _force_disconnect_all(self):
        """强制断开所有连接"""
        try:
            if self._tree:
                self._tree.disconnect()
                self._tree = None
        except:
            pass
        try:
            if self._session:
                self._session.logoff()
                self._session = None
        except:
            pass
        try:
            if self._connection:
                self._connection.disconnect()
                self._connection = None
        except:
            pass

    def _simple_download(self, key: str) -> Optional[bytes]:
        """简单直接的下载方法"""
        max_retries = 3
        retry_count = 0

        while retry_count < max_retries:
            # 创建完全独立的连接对象
            temp_connection = None
            temp_session = None
            temp_tree = None

            try:
                # 添加短暂延迟避免连接冲突
                time.sleep(0.1 * retry_count)

                # 创建独立的SMB连接
                temp_connection = Connection(
                    uuid.uuid4(),
                    self.config.hostname,
                    self.config.port
                )
                temp_connection.connect()

                # 创建独立的会话
                temp_session = Session(
                    temp_connection,
                    self.config.username,
                    self.config.password
                )
                temp_session.connect()

                # 创建独立的树连接
                temp_tree = TreeConnect(
                    temp_session,
                    f"\\\\{self.config.hostname}\\{self.config.share_name}"
                )
                temp_tree.connect()

                # 准备文件路径 - 使用正确的路径规范化
                file_path = self._normalize_path(key)

                # 创建文件对象
                file_obj = Open(temp_tree, file_path)

                file_obj.create(
                    ImpersonationLevel.Impersonation,
                    FileAccessMask.GENERIC_READ,
                    FileAttributes.FILE_ATTRIBUTE_NORMAL,
                    ShareAccess.FILE_SHARE_READ,
                    CreateDisposition.FILE_OPEN,
                    CreateOptions.FILE_NON_DIRECTORY_FILE
                )

                # 读取文件内容
                try:
                    chunk_size = 32 * 1024  # 32KB chunks
                    all_data = bytearray()
                    offset = 0

                    while True:
                        chunk = file_obj.read(offset, chunk_size)
                        if not chunk or len(chunk) == 0:
                            break
                        all_data.extend(chunk)
                        offset += len(chunk)

                        # 防止无限循环和内存溢出
                        if len(all_data) > 100 * 1024 * 1024:  # 100MB limit
                            break

                        # 如果读取的数据少于请求的大小，说明已到文件末尾
                        if len(chunk) < chunk_size:
                            break

                    file_obj.close()

                    # 返回数据
                    return bytes(all_data) if all_data else b''

                except Exception as read_error:
                    file_obj.close()
                    raise read_error

            except Exception as e:
                retry_count += 1
                if retry_count < max_retries:
                    # 确保文件名正确编码
                    safe_key = key.encode('utf-8', errors='replace').decode('utf-8')
                    print(f"SMB文件下载失败 {safe_key} (尝试 {retry_count}/{max_retries}): {e}")
                    # 短暂等待后重试
                    time.sleep(1.0 * retry_count)
                else:
                    # 最后一次尝试失败，不打印错误（避免日志污染）
                    pass
            finally:
                # 清理独立的连接
                try:
                    if temp_tree:
                        temp_tree.disconnect()
                except:
                    pass
                try:
                    if temp_session:
                        temp_session.logoff()
                except:
                    pass
                try:
                    if temp_connection:
                        temp_connection.disconnect()
                except:
                    pass

        return None

    def _download_file_isolated(self, key: str) -> Optional[bytes]:
        """完全隔离的文件下载方法 - 创建独立的连接"""
        max_retries = 3
        retry_count = 0

        while retry_count < max_retries:
            # 创建完全独立的连接对象
            isolated_connection = None
            isolated_session = None
            isolated_tree = None

            try:
                # 添加短暂延迟避免连接冲突
                time.sleep(0.1 * retry_count)

                # 创建独立的SMB连接
                isolated_connection = Connection(
                    uuid.uuid4(),
                    self.config.hostname,
                    self.config.port
                )
                isolated_connection.connect()

                # 创建独立的会话
                isolated_session = Session(
                    isolated_connection,
                    self.config.username,
                    self.config.password
                )
                isolated_session.connect()

                # 创建独立的树连接
                isolated_tree = TreeConnect(
                    isolated_session,
                    f"\\\\{self.config.hostname}\\{self.config.share_name}"
                )
                isolated_tree.connect()

                # 准备文件路径
                file_path = key.lstrip('\\').lstrip('/')

                # 创建文件对象
                file_obj = Open(
                    isolated_tree,
                    file_path
                )

                file_obj.create(
                    ImpersonationLevel.Impersonation,
                    FileAccessMask.GENERIC_READ,
                    FileAttributes.FILE_ATTRIBUTE_NORMAL,
                    ShareAccess.FILE_SHARE_READ,
                    CreateDisposition.FILE_OPEN,
                    CreateOptions.FILE_NON_DIRECTORY_FILE
                )

                # 读取文件内容
                try:
                    chunk_size = 32 * 1024  # 32KB chunks
                    all_data = bytearray()
                    offset = 0

                    while True:
                        chunk = file_obj.read(offset, chunk_size)
                        if not chunk or len(chunk) == 0:
                            break
                        all_data.extend(chunk)
                        offset += len(chunk)

                        # 防止无限循环和内存溢出
                        if len(all_data) > 100 * 1024 * 1024:  # 100MB limit
                            break

                        # 如果读取的数据少于请求的大小，说明已到文件末尾
                        if len(chunk) < chunk_size:
                            break

                    file_obj.close()

                    # 返回数据
                    return bytes(all_data) if all_data else b''

                except Exception as read_error:
                    file_obj.close()
                    raise read_error

            except Exception as e:
                retry_count += 1
                if retry_count < max_retries:
                    print(f"SMB文件下载失败 {key} (尝试 {retry_count}/{max_retries}): {e}")
                    # 短暂等待后重试
                    time.sleep(1.0 * retry_count)
                else:
                    # 最后一次尝试失败，不打印错误（避免日志污染）
                    pass
            finally:
                # 清理独立的连接
                try:
                    if isolated_tree:
                        isolated_tree.disconnect()
                except:
                    pass
                try:
                    if isolated_session:
                        isolated_session.logoff()
                except:
                    pass
                try:
                    if isolated_connection:
                        isolated_connection.disconnect()
                except:
                    pass

        return None

    def _download_file_internal(self, key: str) -> Optional[bytes]:
        """内部文件下载方法"""
        max_retries = 3
        retry_count = 0

        while retry_count < max_retries:
            try:
                # 每次都使用全新的连接
                self._disconnect()

                # 添加短暂延迟避免连接冲突
                time.sleep(0.1 * retry_count)

                self._connect()

                # 不使用路径规范化，直接使用原始文件名
                file_path = key.lstrip('\\').lstrip('/')

                # 使用经过验证的方法创建文件对象
                file_obj = self._create_file_object(
                    file_path,
                    desired_access=FileAccessMask.GENERIC_READ,
                    create_disposition=CreateDisposition.FILE_OPEN
                )

                # 使用经过验证的分块读取方法
                try:
                    chunk_size = 32 * 1024  # 32KB chunks (更小的块)
                    all_data = bytearray()
                    offset = 0

                    while True:
                        chunk = file_obj.read(offset, chunk_size)
                        if not chunk or len(chunk) == 0:
                            break
                        all_data.extend(chunk)
                        offset += len(chunk)

                        # 防止无限循环和内存溢出
                        if len(all_data) > 100 * 1024 * 1024:  # 100MB limit
                            break

                        # 如果读取的数据少于请求的大小，说明已到文件末尾
                        if len(chunk) < chunk_size:
                            break

                    file_obj.close()

                    # 返回数据
                    return bytes(all_data) if all_data else b''

                except Exception as read_error:
                    file_obj.close()
                    raise read_error

            except Exception as e:
                retry_count += 1
                if retry_count < max_retries:
                    print(f"SMB文件下载失败 {key} (尝试 {retry_count}/{max_retries}): {e}")
                    # 短暂等待后重试
                    time.sleep(1.0 * retry_count)
                else:
                    # 最后一次尝试失败，不打印错误（避免日志污染）
                    pass
            finally:
                try:
                    self._disconnect()
                except:
                    pass

        return None
    
    def put_file(self, key: str, data: bytes, 
                 content_type: str = 'binary/octet-stream',
                 metadata: Optional[Dict[str, str]] = None) -> bool:
        """上传文件"""
        try:
            self._connect()
            
            file_path = self._normalize_path(key)
            
            # 确保目录存在
            dir_path = ntpath.dirname(file_path)
            if dir_path and dir_path != '\\':
                self._ensure_directory_exists(dir_path)
            
            # 创建/打开文件
            file_obj = self._create_file_object(
                file_path,
                desired_access=FileAccessMask.GENERIC_WRITE,
                create_disposition=CreateDisposition.FILE_OVERWRITE_IF
            )
            
            # 写入文件内容
            file_obj.write(data, 0)
            file_obj.close()
            
            return True
        
        except Exception:
            return False
        finally:
            self._disconnect()
    
    def _ensure_directory_exists(self, dir_path: str):
        """确保目录存在"""
        try:
            # 尝试打开目录
            dir_obj = self._create_file_object(
                dir_path,
                desired_access=FileAccessMask.GENERIC_READ,
                create_disposition=CreateDisposition.FILE_OPEN,
                create_options=CreateOptions.FILE_DIRECTORY_FILE,
                file_attributes=FileAttributes.FILE_ATTRIBUTE_DIRECTORY
            )
            dir_obj.close()
        except smb_exceptions.SMBResponseException as e:
            if e.status == 0xC0000034:  # STATUS_OBJECT_NAME_NOT_FOUND
                # 目录不存在，递归创建
                parent_dir = ntpath.dirname(dir_path)
                if parent_dir and parent_dir != '\\' and parent_dir != dir_path:
                    self._ensure_directory_exists(parent_dir)
                
                # 创建目录
                dir_obj = self._create_file_object(
                    dir_path,
                    desired_access=FileAccessMask.GENERIC_WRITE,
                    create_disposition=CreateDisposition.FILE_CREATE,
                    create_options=CreateOptions.FILE_DIRECTORY_FILE,
                    file_attributes=FileAttributes.FILE_ATTRIBUTE_DIRECTORY
                )
                dir_obj.close()
    
    def delete_file(self, key: str) -> bool:
        """删除文件"""
        try:
            self._connect()
            
            file_path = self._normalize_path(key)
            
            # 打开文件并删除
            file_obj = self._create_file_object(
                file_path,
                desired_access=FileAccessMask.DELETE,
                create_disposition=CreateDisposition.FILE_OPEN
            )
            file_obj.delete()
            file_obj.close()
            
            return True
        
        except Exception:
            return False
        finally:
            self._disconnect()
    
    def get_file_metadata(self, key: str) -> Optional[FileMetadata]:
        """获取文件元数据"""
        try:
            self._connect()
            
            file_path = self._normalize_path(key)
            
            # 打开文件
            file_obj = self._create_file_object(
                file_path,
                desired_access=FileAccessMask.GENERIC_READ,
                create_disposition=CreateDisposition.FILE_OPEN
            )
            
            # 简化：返回基本文件信息
            file_obj.close()

            # 返回简化的文件元数据
            return FileMetadata(
                key=key,
                size=0,  # 暂时无法获取大小
                last_modified=datetime.now(),  # 使用当前时间
                etag=None,  # SMB不支持ETag
                content_type='binary/octet-stream'
            )
        
        except Exception:
            return None
        finally:
            self._disconnect()
    
    def file_exists(self, key: str) -> bool:
        """检查文件是否存在"""
        try:
            self._connect()
            
            file_path = self._normalize_path(key)
            
            # 尝试打开文件
            file_obj = self._create_file_object(
                file_path,
                desired_access=FileAccessMask.GENERIC_READ,
                create_disposition=CreateDisposition.FILE_OPEN
            )
            # 简化：如果能打开就认为文件存在
            file_obj.close()
            return True
        
        except Exception:
            return False
        finally:
            self._disconnect()


# 注册SMB适配器
if SMBPROTOCOL_AVAILABLE:
    StorageFactory.register_adapter(StorageType.SMB, SMBStorageAdapter)
