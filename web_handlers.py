#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LightRek 统一存储同步工具 - Web请求处理器
"""

import json
import uuid
from http.server import BaseHTTPRequestHandler
from web_templates import WebTemplates


class WebRequestHandler(BaseHTTPRequestHandler):
    """Web请求处理器"""
    
    def __init__(self, config_manager, task_manager, db_manager=None):
        self.config_manager = config_manager
        self.task_manager = task_manager
        self.db_manager = db_manager
        self.templates = WebTemplates()
    
    def create_handler_class(self):
        """创建请求处理器类"""
        config_manager = self.config_manager
        task_manager = self.task_manager
        db_manager = self.db_manager
        templates = self.templates
        
        class RequestHandler(BaseHTTPRequestHandler):
            def do_GET(self):
                if self.path == '/' or self.path == '/dashboard':
                    self._serve_dashboard()
                elif self.path == '/manual':
                    self._serve_manual()
                elif self.path.startswith('/logs'):
                    self._serve_task_logs_page()
                elif self.path == '/api/sources':
                    self._serve_sources()
                elif self.path == '/api/targets':
                    self._serve_targets()
                elif self.path == '/api/tasks':
                    self._serve_tasks()
                elif self.path == '/api/task-status' or self.path == '/api/task_status':
                    self._serve_task_status()
                elif self.path == '/api/task-executions' or self.path == '/api/task_executions':
                    self._serve_task_executions()
                elif self.path.startswith('/api/task-logs/') or self.path.startswith('/api/task_logs/'):
                    execution_id = self.path.split('/')[-1]
                    self._serve_task_logs(execution_id)
                elif self.path.startswith('/api/task-details/'):
                    task_id = self.path.split('/')[-1]
                    self._serve_task_details(task_id)
                elif self.path == '/api/statistics':
                    self._serve_statistics()
                elif self.path == '/api/optimization-config':
                    self._serve_optimization_config()
                else:
                    self._serve_404()
            
            def do_POST(self):
                content_length = int(self.headers.get('Content-Length', 0))
                post_data = self.rfile.read(content_length)
                
                try:
                    data = json.loads(post_data.decode('utf-8')) if post_data else {}
                except:
                    data = {}
                
                if self.path == '/api/sources':
                    self._add_source(data)
                elif self.path == '/api/targets':
                    self._add_target(data)
                elif self.path == '/api/tasks':
                    self._add_task(data)
                elif self.path == '/api/test-connection':
                    self._test_connection(data)
                elif self.path == '/api/list-buckets':
                    self._list_buckets(data)
                elif self.path.startswith('/api/browse/'):
                    self._browse_files(data)
                elif self.path.startswith('/api/tasks/') and self.path.endswith('/run'):
                    task_id = self.path.split('/')[-2]
                    self._run_task(task_id)
                elif self.path.startswith('/api/tasks/') and self.path.endswith('/stop'):
                    task_id = self.path.split('/')[-2]
                    self._stop_task(task_id)
                elif self.path == '/api/optimization-config':
                    self._save_optimization_config(data)
                else:
                    self._serve_404()
            
            def do_PUT(self):
                content_length = int(self.headers.get('Content-Length', 0))
                post_data = self.rfile.read(content_length)
                
                try:
                    data = json.loads(post_data.decode('utf-8')) if post_data else {}
                except:
                    data = {}
                
                if self.path.startswith('/api/sources/'):
                    source_id = self.path.split('/')[-1]
                    self._update_source(source_id, data)
                elif self.path.startswith('/api/targets/'):
                    target_id = self.path.split('/')[-1]
                    self._update_target(target_id, data)
                elif self.path.startswith('/api/tasks/'):
                    task_id = self.path.split('/')[-1]
                    self._update_task(task_id, data)
                else:
                    self._serve_404()
            
            def do_DELETE(self):
                if self.path.startswith('/api/sources/'):
                    source_id = self.path.split('/')[-1]
                    self._delete_source(source_id)
                elif self.path.startswith('/api/targets/'):
                    target_id = self.path.split('/')[-1]
                    self._delete_target(target_id)
                elif self.path.startswith('/api/tasks/'):
                    task_id = self.path.split('/')[-1]
                    self._delete_task(task_id)
                else:
                    self._serve_404()
            
            def _serve_dashboard(self):
                """提供仪表盘页面"""
                stats = self._get_dashboard_stats()
                html = templates.get_dashboard_html(stats)
                self.send_response(200)
                self.send_header('Content-Type', 'text/html; charset=utf-8')
                self.end_headers()
                self.wfile.write(html.encode('utf-8'))
            
            def _serve_manual(self):
                """提供用户手册页面"""
                html = templates.get_manual_html()
                self.send_response(200)
                self.send_header('Content-Type', 'text/html; charset=utf-8')
                self.end_headers()
                self.wfile.write(html.encode('utf-8'))
            
            def _serve_task_logs_page(self):
                """重定向到仪表盘的任务日志页面"""
                self.send_response(302)
                self.send_header('Location', '/#logs')
                self.end_headers()
            
            def _serve_sources(self):
                """提供数据源列表"""
                try:
                    sources = config_manager.get_all_sources()
                    self._send_json({'success': True, 'sources': sources})
                except Exception as e:
                    self._send_json({'success': False, 'message': str(e), 'sources': {}})

            def _serve_targets(self):
                """提供目标存储列表"""
                try:
                    targets = config_manager.get_all_targets()
                    self._send_json({'success': True, 'targets': targets})
                except Exception as e:
                    self._send_json({'success': False, 'message': str(e), 'targets': {}})

            def _serve_tasks(self):
                """提供任务列表"""
                try:
                    tasks = config_manager.get_all_tasks()
                    self._send_json({'success': True, 'tasks': tasks})
                except Exception as e:
                    self._send_json({'success': False, 'message': str(e), 'tasks': {}})
            
            def _serve_task_status(self):
                """提供任务状态"""
                try:
                    status = task_manager.get_all_task_status()
                    self._send_json({'success': True, 'status': status})
                except Exception as e:
                    self._send_json({'success': False, 'message': str(e), 'status': {}})
            
            def _serve_task_executions(self):
                """提供任务执行记录"""
                try:
                    executions = db_manager.get_recent_executions(20) if db_manager else []
                    self._send_json({'success': True, 'executions': executions})
                except Exception as e:
                    self._send_json({'success': False, 'message': str(e), 'executions': []})
            
            def _serve_task_logs(self, execution_id):
                """提供特定任务的日志数据"""
                try:
                    logs = db_manager.get_execution_logs(execution_id) if db_manager else []
                    self._send_json({'success': True, 'logs': logs})
                except Exception as e:
                    self._send_json({'success': False, 'message': str(e), 'logs': []})
            
            def _serve_task_details(self, task_id):
                """提供任务详细信息"""
                try:
                    tasks = config_manager.get_all_tasks()
                    if task_id in tasks:
                        task = tasks[task_id]
                        executions = db_manager.get_task_executions(task_id) if db_manager else []
                        self._send_json({
                            'success': True,
                            'task': task,
                            'executions': executions
                        })
                    else:
                        self._send_json({'success': False, 'message': '任务不存在'})
                except Exception as e:
                    self._send_json({'success': False, 'message': str(e)})
            
            def _serve_statistics(self):
                """提供统计信息"""
                try:
                    stats = self._get_dashboard_stats()
                    self._send_json({'success': True, 'statistics': stats})
                except Exception as e:
                    self._send_json({'success': False, 'message': str(e), 'statistics': {}})
            
            def _serve_optimization_config(self):
                """提供优化配置"""
                try:
                    config = config_manager.get_optimization_config()
                    self._send_json({'success': True, 'config': config})
                except Exception as e:
                    self._send_json({'success': False, 'message': str(e), 'config': {}})
            
            def _serve_404(self):
                """提供404页面"""
                self.send_response(404)
                self.send_header('Content-Type', 'text/html; charset=utf-8')
                self.end_headers()
                html = """
                <!DOCTYPE html>
                <html>
                <head>
                    <title>404 - 页面未找到</title>
                    <style>
                        body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
                        h1 { color: #ff6b35; }
                    </style>
                </head>
                <body>
                    <h1>404 - 页面未找到</h1>
                    <p>请求的页面不存在</p>
                    <a href="/">返回首页</a>
                </body>
                </html>
                """
                self.wfile.write(html.encode('utf-8'))
            
            def _send_json(self, data):
                """发送JSON响应"""
                self.send_response(200)
                self.send_header('Content-Type', 'application/json; charset=utf-8')
                self.end_headers()
                self.wfile.write(json.dumps(data, ensure_ascii=False).encode('utf-8'))
            
            def _get_dashboard_stats(self):
                """获取仪表盘统计数据"""
                try:
                    sources = config_manager.get_all_sources()
                    targets = config_manager.get_all_targets()
                    tasks = config_manager.get_all_tasks()
                    
                    stats = {
                        'sources': len(sources),
                        'targets': len(targets),
                        'tasks': len(tasks),
                        'executions': 0
                    }
                    
                    if db_manager:
                        stats['executions'] = len(db_manager.get_recent_executions(1000))
                    
                    return stats
                except Exception as e:
                    print(f"获取统计数据失败: {e}")
                    return {
                        'sources': 0,
                        'targets': 0,
                        'tasks': 0,
                        'executions': 0
                    }

            def _add_source(self, data):
                """添加数据源"""
                try:
                    source_id = str(uuid.uuid4())
                    storage_type = data.get('type', 's3')
                    config_manager.add_source(source_id, storage_type, data)
                    self._send_json({'success': True, 'message': '数据源添加成功', 'source_id': source_id})
                except Exception as e:
                    self._send_json({'success': False, 'message': f'添加数据源失败: {str(e)}'})

            def _add_target(self, data):
                """添加目标存储"""
                try:
                    target_id = str(uuid.uuid4())
                    storage_type = data.get('type', 's3')
                    config_manager.add_target(target_id, storage_type, data)
                    self._send_json({'success': True, 'message': '目标存储添加成功', 'target_id': target_id})
                except Exception as e:
                    self._send_json({'success': False, 'message': f'添加目标存储失败: {str(e)}'})

            def _add_task(self, data):
                """添加同步任务"""
                try:
                    task_id = str(uuid.uuid4())
                    config_manager.add_task(task_id, data)
                    self._send_json({'success': True, 'message': '同步任务添加成功', 'task_id': task_id})
                except Exception as e:
                    self._send_json({'success': False, 'message': f'添加同步任务失败: {str(e)}'})

            def _update_source(self, source_id, data):
                """更新数据源"""
                try:
                    # UnifiedConfigManager没有update_source方法，需要先删除再添加
                    config_manager.remove_source(source_id)
                    storage_type = data.get('type', 's3')
                    config_manager.add_source(source_id, storage_type, data)
                    self._send_json({'success': True, 'message': '数据源更新成功'})
                except Exception as e:
                    self._send_json({'success': False, 'message': f'更新数据源失败: {str(e)}'})

            def _update_target(self, target_id, data):
                """更新目标存储"""
                try:
                    # UnifiedConfigManager没有update_target方法，需要先删除再添加
                    config_manager.remove_target(target_id)
                    storage_type = data.get('type', 's3')
                    config_manager.add_target(target_id, storage_type, data)
                    self._send_json({'success': True, 'message': '目标存储更新成功'})
                except Exception as e:
                    self._send_json({'success': False, 'message': f'更新目标存储失败: {str(e)}'})

            def _update_task(self, task_id, data):
                """更新同步任务"""
                try:
                    config_manager.update_task(task_id, data)
                    self._send_json({'success': True, 'message': '同步任务更新成功'})
                except Exception as e:
                    self._send_json({'success': False, 'message': f'更新同步任务失败: {str(e)}'})

            def _delete_source(self, source_id):
                """删除数据源"""
                try:
                    config_manager.remove_source(source_id)
                    self._send_json({'success': True, 'message': '数据源删除成功'})
                except Exception as e:
                    self._send_json({'success': False, 'message': f'删除数据源失败: {str(e)}'})

            def _delete_target(self, target_id):
                """删除目标存储"""
                try:
                    config_manager.remove_target(target_id)
                    self._send_json({'success': True, 'message': '目标存储删除成功'})
                except Exception as e:
                    self._send_json({'success': False, 'message': f'删除目标存储失败: {str(e)}'})

            def _delete_task(self, task_id):
                """删除同步任务"""
                try:
                    config_manager.remove_task(task_id)
                    self._send_json({'success': True, 'message': '同步任务删除成功'})
                except Exception as e:
                    self._send_json({'success': False, 'message': f'删除同步任务失败: {str(e)}'})

            def _test_connection(self, data):
                """测试连接"""
                try:
                    # 这里应该调用相应的连接测试方法
                    storage_type = data.get('type', '')
                    if storage_type == 's3':
                        result = self._test_s3_connection(data)
                    elif storage_type == 'sftp':
                        result = self._test_sftp_connection(data)
                    elif storage_type == 'ftp':
                        result = self._test_ftp_connection(data)
                    elif storage_type == 'smb':
                        result = self._test_smb_connection(data)
                    elif storage_type == 'local':
                        result = self._test_local_connection(data)
                    else:
                        result = {'success': False, 'message': f'不支持的存储类型: {storage_type}'}

                    self._send_json(result)
                except Exception as e:
                    self._send_json({'success': False, 'message': f'连接测试失败: {str(e)}'})

            def _test_s3_connection(self, data):
                """测试S3连接"""
                try:
                    from s3_storage_adapter import S3StorageAdapter
                    adapter = S3StorageAdapter(data)
                    adapter.test_connection()
                    return {'success': True, 'message': 'S3连接测试成功'}
                except Exception as e:
                    return {'success': False, 'message': f'S3连接测试失败: {str(e)}'}

            def _test_sftp_connection(self, data):
                """测试SFTP连接"""
                try:
                    from sftp_storage_adapter import SFTPStorageAdapter
                    adapter = SFTPStorageAdapter(data)
                    adapter.test_connection()
                    return {'success': True, 'message': 'SFTP连接测试成功'}
                except Exception as e:
                    return {'success': False, 'message': f'SFTP连接测试失败: {str(e)}'}

            def _test_ftp_connection(self, data):
                """测试FTP连接"""
                try:
                    from ftp_storage_adapter import FTPStorageAdapter
                    adapter = FTPStorageAdapter(data)
                    adapter.test_connection()
                    return {'success': True, 'message': 'FTP连接测试成功'}
                except Exception as e:
                    return {'success': False, 'message': f'FTP连接测试失败: {str(e)}'}

            def _test_smb_connection(self, data):
                """测试SMB连接"""
                try:
                    from smb_storage_adapter import SMBStorageAdapter
                    adapter = SMBStorageAdapter(data)
                    adapter.test_connection()
                    return {'success': True, 'message': 'SMB连接测试成功'}
                except Exception as e:
                    return {'success': False, 'message': f'SMB连接测试失败: {str(e)}'}

            def _test_local_connection(self, data):
                """测试本地存储连接"""
                try:
                    from local_storage_adapter import LocalStorageAdapter
                    adapter = LocalStorageAdapter(data)
                    adapter.test_connection()
                    return {'success': True, 'message': '本地存储连接测试成功'}
                except Exception as e:
                    return {'success': False, 'message': f'本地存储连接测试失败: {str(e)}'}

            def _list_buckets(self, data):
                """列出S3存储桶"""
                try:
                    from storage_abstraction import S3StorageConfig, StorageType
                    from s3_storage_adapter import S3StorageAdapter

                    # 创建S3配置对象
                    config = S3StorageConfig(
                        storage_type=StorageType.S3,
                        name="临时配置",
                        endpoint=data.get('endpoint', ''),
                        access_key=data.get('access_key', ''),
                        secret_key=data.get('secret_key', ''),
                        bucket="",  # 不指定bucket来列出所有bucket
                        region=data.get('region', '')
                    )

                    adapter = S3StorageAdapter(config)
                    buckets = adapter.list_buckets()
                    self._send_json({'success': True, 'buckets': buckets})
                except Exception as e:
                    self._send_json({'success': False, 'message': f'获取存储桶列表失败: {str(e)}', 'buckets': []})

            def _run_task(self, task_id):
                """运行任务"""
                try:
                    task_manager.run_task(task_id)
                    self._send_json({'success': True, 'message': '任务启动成功'})
                except Exception as e:
                    self._send_json({'success': False, 'message': f'任务启动失败: {str(e)}'})

            def _stop_task(self, task_id):
                """停止任务"""
                try:
                    task_manager.stop_task(task_id)
                    self._send_json({'success': True, 'message': '任务停止成功'})
                except Exception as e:
                    self._send_json({'success': False, 'message': f'任务停止失败: {str(e)}'})

            def _save_optimization_config(self, data):
                """保存优化配置"""
                try:
                    config_manager.update_optimization_settings(data)
                    self._send_json({'success': True, 'message': '优化配置保存成功'})
                except Exception as e:
                    self._send_json({'success': False, 'message': f'保存优化配置失败: {str(e)}'})

            def _browse_files(self, data):
                """浏览存储中的文件"""
                try:
                    # 解析URL路径获取存储类型和ID
                    path_parts = self.path.split('/')
                    if len(path_parts) < 5:
                        self._send_json({'success': False, 'message': '无效的API路径'})
                        return

                    storage_type = path_parts[3]  # source 或 target
                    storage_id = path_parts[4]
                    browse_path = data.get('path', '')

                    # 获取存储配置
                    if storage_type == 'source':
                        storage_config = config_manager.get_source(storage_id)
                    elif storage_type == 'target':
                        storage_config = config_manager.get_target(storage_id)
                    else:
                        self._send_json({'success': False, 'message': '无效的存储类型'})
                        return

                    if not storage_config:
                        self._send_json({'success': False, 'message': '存储配置不存在'})
                        return

                    # 创建存储适配器
                    from storage_abstraction import StorageFactory
                    adapter = StorageFactory.create_adapter(storage_config)

                    # 列出文件
                    result = adapter.list_files(prefix=browse_path, max_keys=1000)

                    # 处理文件列表，分离目录和文件
                    files = []
                    directories = set()

                    for file_meta in result.files:
                        # 移除前缀路径
                        relative_path = file_meta.key
                        if browse_path and relative_path.startswith(browse_path):
                            relative_path = relative_path[len(browse_path):].lstrip('/')

                        # 如果路径包含斜杠，说明是子目录中的文件
                        if '/' in relative_path:
                            dir_name = relative_path.split('/')[0]
                            directories.add(dir_name)
                        else:
                            # 直接文件
                            files.append({
                                'name': relative_path,
                                'type': 'file',
                                'size': file_meta.size,
                                'modified': file_meta.last_modified.isoformat() if file_meta.last_modified else '',
                                'path': file_meta.key
                            })

                    # 添加目录到文件列表
                    for dir_name in directories:
                        files.insert(0, {
                            'name': dir_name,
                            'type': 'directory',
                            'size': 0,
                            'modified': '',
                            'path': f"{browse_path}/{dir_name}".strip('/')
                        })

                    # 计算父路径
                    parent_path = ''
                    if browse_path:
                        path_parts = browse_path.rstrip('/').split('/')
                        if len(path_parts) > 1:
                            parent_path = '/'.join(path_parts[:-1])

                    self._send_json({
                        'success': True,
                        'files': files,
                        'current_path': browse_path,
                        'parent_path': parent_path,
                        'total_count': len(files)
                    })

                except Exception as e:
                    self._send_json({'success': False, 'message': f'浏览文件失败: {str(e)}'})

        return RequestHandler
