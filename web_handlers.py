#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LightRek 统一存储同步工具 - Web请求处理器
"""

import json
import uuid
from http.server import BaseHTTPRequestHandler
from web_templates import WebTemplates


class WebRequestHandler(BaseHTTPRequestHandler):
    """Web请求处理器"""
    
    def __init__(self, config_manager, task_manager, db_manager=None):
        self.config_manager = config_manager
        self.task_manager = task_manager
        self.db_manager = db_manager
        self.templates = WebTemplates()
    
    def create_handler_class(self):
        """创建请求处理器类"""
        config_manager = self.config_manager
        task_manager = self.task_manager
        db_manager = self.db_manager
        templates = self.templates
        
        class RequestHandler(BaseHTTPRequestHandler):
            def do_GET(self):
                if self.path == '/' or self.path == '/dashboard':
                    self._serve_dashboard()
                elif self.path == '/manual':
                    self._serve_manual()
                elif self.path.startswith('/logs'):
                    self._serve_task_logs_page()
                elif self.path == '/api/sources':
                    self._serve_sources()
                elif self.path == '/api/targets':
                    self._serve_targets()
                elif self.path == '/api/tasks':
                    self._serve_tasks()
                elif self.path == '/api/task-status' or self.path == '/api/task_status':
                    self._serve_task_status()
                elif self.path == '/api/task-executions' or self.path == '/api/task_executions':
                    self._serve_task_executions()
                elif self.path.startswith('/api/task-logs/') or self.path.startswith('/api/task_logs/'):
                    execution_id = self.path.split('/')[-1]
                    self._serve_task_logs(execution_id)
                elif self.path.startswith('/api/task-details/'):
                    task_id = self.path.split('/')[-1]
                    self._serve_task_details(task_id)
                elif self.path == '/api/statistics':
                    self._serve_statistics()
                elif self.path == '/api/optimization-config':
                    self._serve_optimization_config()
                elif self.path == '/api/database-stats':
                    self._serve_database_stats()
                else:
                    self._serve_404()
            
            def do_POST(self):
                content_length = int(self.headers.get('Content-Length', 0))
                post_data = self.rfile.read(content_length)

                try:
                    data = json.loads(post_data.decode('utf-8')) if post_data else {}
                except:
                    data = {}

                print(f"🔍 调试: POST请求路径: {self.path}")

                if self.path == '/api/sources':
                    self._add_source(data)
                elif self.path == '/api/targets':
                    self._add_target(data)
                elif self.path == '/api/tasks':
                    self._add_task(data)
                elif self.path == '/api/test-connection':
                    self._test_connection(data)
                elif self.path == '/api/list-buckets':
                    self._list_buckets(data)
                elif self.path.startswith('/api/browse/'):
                    self._browse_files(data)
                elif self.path.startswith('/api/tasks/') and self.path.endswith('/run'):
                    task_id = self.path.split('/')[-2]
                    self._run_task(task_id)
                elif self.path.startswith('/api/tasks/') and self.path.endswith('/stop'):
                    task_id = self.path.split('/')[-2]
                    self._stop_task(task_id)
                elif self.path == '/api/optimization-config':
                    self._save_optimization_config(data)
                elif self.path == '/api/database-optimize':
                    self._optimize_database(data)
                else:
                    self._serve_404()
            
            def do_PUT(self):
                content_length = int(self.headers.get('Content-Length', 0))
                post_data = self.rfile.read(content_length)
                
                try:
                    data = json.loads(post_data.decode('utf-8')) if post_data else {}
                except:
                    data = {}
                
                if self.path.startswith('/api/sources/'):
                    source_id = self.path.split('/')[-1]
                    self._update_source(source_id, data)
                elif self.path.startswith('/api/targets/'):
                    target_id = self.path.split('/')[-1]
                    self._update_target(target_id, data)
                elif self.path.startswith('/api/tasks/'):
                    task_id = self.path.split('/')[-1]
                    self._update_task(task_id, data)
                else:
                    self._serve_404()
            
            def do_DELETE(self):
                if self.path.startswith('/api/sources/'):
                    source_id = self.path.split('/')[-1]
                    self._delete_source(source_id)
                elif self.path.startswith('/api/targets/'):
                    target_id = self.path.split('/')[-1]
                    self._delete_target(target_id)
                elif self.path.startswith('/api/tasks/'):
                    task_id = self.path.split('/')[-1]
                    self._delete_task(task_id)
                else:
                    self._serve_404()
            
            def _serve_dashboard(self):
                """提供仪表盘页面"""
                stats = self._get_dashboard_stats()
                html = templates.get_dashboard_html(stats)
                self.send_response(200)
                self.send_header('Content-Type', 'text/html; charset=utf-8')
                self.end_headers()
                self.wfile.write(html.encode('utf-8'))
            
            def _serve_manual(self):
                """提供用户手册页面"""
                html = templates.get_manual_html()
                self.send_response(200)
                self.send_header('Content-Type', 'text/html; charset=utf-8')
                self.end_headers()
                self.wfile.write(html.encode('utf-8'))
            
            def _serve_task_logs_page(self):
                """重定向到仪表盘的任务日志页面"""
                self.send_response(302)
                self.send_header('Location', '/#logs')
                self.end_headers()
            
            def _serve_sources(self):
                """提供数据源列表"""
                try:
                    sources = config_manager.get_all_sources()
                    self._send_json({'success': True, 'sources': sources})
                except Exception as e:
                    self._send_json({'success': False, 'message': str(e), 'sources': {}})

            def _serve_targets(self):
                """提供目标存储列表"""
                try:
                    targets = config_manager.get_all_targets()
                    self._send_json({'success': True, 'targets': targets})
                except Exception as e:
                    self._send_json({'success': False, 'message': str(e), 'targets': {}})

            def _serve_tasks(self):
                """提供任务列表"""
                try:
                    tasks = config_manager.get_all_tasks()
                    self._send_json({'success': True, 'tasks': tasks})
                except Exception as e:
                    self._send_json({'success': False, 'message': str(e), 'tasks': {}})
            
            def _serve_task_status(self):
                """提供任务状态"""
                try:
                    status = task_manager.get_all_task_status()
                    self._send_json({'success': True, 'status': status})
                except Exception as e:
                    self._send_json({'success': False, 'message': str(e), 'status': {}})
            
            def _serve_task_executions(self):
                """提供任务执行记录"""
                try:
                    executions = db_manager.get_recent_executions(20) if db_manager else []
                    self._send_json({'success': True, 'executions': executions})
                except Exception as e:
                    self._send_json({'success': False, 'message': str(e), 'executions': []})
            
            def _serve_task_logs(self, execution_id):
                """提供特定任务的日志数据"""
                try:
                    logs = db_manager.get_execution_logs(execution_id) if db_manager else []
                    self._send_json({'success': True, 'logs': logs})
                except Exception as e:
                    self._send_json({'success': False, 'message': str(e), 'logs': []})
            
            def _serve_task_details(self, task_id):
                """提供任务详细信息"""
                try:
                    tasks = config_manager.get_all_tasks()
                    if task_id in tasks:
                        task = tasks[task_id]
                        executions = db_manager.get_task_executions(task_id) if db_manager else []
                        self._send_json({
                            'success': True,
                            'task': task,
                            'executions': executions
                        })
                    else:
                        self._send_json({'success': False, 'message': '任务不存在'})
                except Exception as e:
                    self._send_json({'success': False, 'message': str(e)})
            
            def _serve_statistics(self):
                """提供统计信息"""
                try:
                    stats = self._get_dashboard_stats()
                    self._send_json({'success': True, 'statistics': stats})
                except Exception as e:
                    self._send_json({'success': False, 'message': str(e), 'statistics': {}})
            
            def _serve_optimization_config(self):
                """提供优化配置"""
                try:
                    config = config_manager.get_optimization_config()
                    self._send_json({'success': True, 'config': config})
                except Exception as e:
                    self._send_json({'success': False, 'message': str(e), 'config': {}})
            
            def _serve_404(self):
                """提供404页面"""
                self.send_response(404)
                self.send_header('Content-Type', 'text/html; charset=utf-8')
                self.end_headers()
                html = """
                <!DOCTYPE html>
                <html>
                <head>
                    <title>404 - 页面未找到</title>
                    <style>
                        body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
                        h1 { color: #ff6b35; }
                    </style>
                </head>
                <body>
                    <h1>404 - 页面未找到</h1>
                    <p>请求的页面不存在</p>
                    <a href="/">返回首页</a>
                </body>
                </html>
                """
                self.wfile.write(html.encode('utf-8'))
            
            def _send_json(self, data):
                """发送JSON响应"""
                self.send_response(200)
                self.send_header('Content-Type', 'application/json; charset=utf-8')
                self.end_headers()
                self.wfile.write(json.dumps(data, ensure_ascii=False).encode('utf-8'))
            
            def _get_dashboard_stats(self):
                """获取仪表盘统计数据"""
                try:
                    sources = config_manager.get_all_sources()
                    targets = config_manager.get_all_targets()
                    tasks = config_manager.get_all_tasks()

                    stats = {
                        'sources': len(sources),
                        'targets': len(targets),
                        'tasks': len(tasks),
                        'executions': 0,
                        'active_tasks': 0,
                        'success_rate': 0,
                        'total_bytes_transferred': 0,
                        'last_24h_executions': 0
                    }

                    if db_manager:
                        # 基本执行统计
                        recent_executions = db_manager.get_recent_executions(1000)
                        stats['executions'] = len(recent_executions)

                        # 24小时内执行次数
                        from datetime import datetime, timedelta
                        yesterday = datetime.now() - timedelta(days=1)
                        stats['last_24h_executions'] = len([
                            exec for exec in recent_executions
                            if exec.get('start_time') and
                            datetime.fromisoformat(exec['start_time'].replace('Z', '+00:00')) > yesterday
                        ])

                        # 成功率计算
                        if recent_executions:
                            successful = len([exec for exec in recent_executions if exec.get('status') == 'completed'])
                            stats['success_rate'] = round((successful / len(recent_executions)) * 100, 1)

                        # 任务状态分布
                        task_status_chart = {}
                        active_count = 0
                        for task in tasks.values():
                            status = getattr(task, 'last_status', 'idle')
                            task_status_chart[status] = task_status_chart.get(status, 0) + 1
                            if status in ['running', 'pending']:
                                active_count += 1
                        stats['active_tasks'] = active_count
                        stats['task_status_chart'] = task_status_chart

                        # 最近7天执行趋势
                        execution_trend = self._get_execution_trend(7)
                        stats['execution_trend'] = execution_trend

                        # 存储使用情况（模拟数据，实际应该从存储适配器获取）
                        storage_usage = self._get_storage_usage(sources, targets)
                        stats['storage_usage'] = storage_usage

                        # 传输字节统计
                        total_bytes = 0
                        for exec in recent_executions:
                            if exec.get('bytes_transferred'):
                                total_bytes += exec.get('bytes_transferred', 0)
                        stats['total_bytes_transferred'] = total_bytes

                    return stats
                except Exception as e:
                    print(f"获取统计数据失败: {e}")
                    return {
                        'sources': 0,
                        'targets': 0,
                        'tasks': 0,
                        'executions': 0,
                        'active_tasks': 0,
                        'success_rate': 0,
                        'total_bytes_transferred': 0,
                        'last_24h_executions': 0,
                        'task_status_chart': {},
                        'execution_trend': {},
                        'storage_usage': {}
                    }

            def _get_execution_trend(self, days=7):
                """获取执行趋势数据"""
                try:
                    from datetime import datetime, timedelta
                    import collections

                    trend_data = collections.OrderedDict()

                    # 初始化最近N天的数据
                    for i in range(days):
                        date = datetime.now() - timedelta(days=days-1-i)
                        date_str = date.strftime('%m-%d')
                        trend_data[date_str] = 0

                    if db_manager:
                        recent_executions = db_manager.get_recent_executions(1000)

                        # 统计每天的执行次数
                        for exec in recent_executions:
                            if exec.get('start_time'):
                                try:
                                    exec_date = datetime.fromisoformat(exec['start_time'].replace('Z', '+00:00'))
                                    date_str = exec_date.strftime('%m-%d')
                                    if date_str in trend_data:
                                        trend_data[date_str] += 1
                                except:
                                    continue

                    return dict(trend_data)
                except Exception as e:
                    print(f"获取执行趋势失败: {e}")
                    return {}

            def _get_storage_usage(self, sources, targets):
                """获取存储使用情况"""
                try:
                    storage_usage = {
                        'total_capacity': 0,
                        'used_space': 0,
                        'available_space': 0,
                        'usage_percentage': 0,
                        'by_type': {}
                    }

                    # 统计不同存储类型的数量
                    all_storages = {**sources, **targets}
                    type_counts = {}

                    for storage in all_storages.values():
                        storage_type = getattr(storage, 'storage_type', 'unknown')
                        if hasattr(storage_type, 'value'):
                            storage_type = storage_type.value
                        type_counts[storage_type] = type_counts.get(storage_type, 0) + 1

                    storage_usage['by_type'] = type_counts

                    # 模拟存储使用情况（实际应该从存储适配器获取）
                    # 这里提供一些示例数据
                    total_storages = len(all_storages)
                    if total_storages > 0:
                        storage_usage['total_capacity'] = total_storages * 1000  # GB
                        storage_usage['used_space'] = total_storages * 650  # GB
                        storage_usage['available_space'] = total_storages * 350  # GB
                        storage_usage['usage_percentage'] = 65

                    return storage_usage
                except Exception as e:
                    print(f"获取存储使用情况失败: {e}")
                    return {
                        'total_capacity': 0,
                        'used_space': 0,
                        'available_space': 0,
                        'usage_percentage': 0,
                        'by_type': {}
                    }

            def _add_source(self, data):
                """添加数据源"""
                try:
                    source_id = str(uuid.uuid4())
                    storage_type = data.get('type', 's3')
                    config_manager.add_source(source_id, storage_type, data)
                    self._send_json({'success': True, 'message': '数据源添加成功', 'source_id': source_id})
                except Exception as e:
                    self._send_json({'success': False, 'message': f'添加数据源失败: {str(e)}'})

            def _add_target(self, data):
                """添加目标存储"""
                try:
                    target_id = str(uuid.uuid4())
                    storage_type = data.get('type', 's3')
                    config_manager.add_target(target_id, storage_type, data)
                    self._send_json({'success': True, 'message': '目标存储添加成功', 'target_id': target_id})
                except Exception as e:
                    self._send_json({'success': False, 'message': f'添加目标存储失败: {str(e)}'})

            def _add_task(self, data):
                """添加同步任务"""
                try:
                    task_id = str(uuid.uuid4())
                    config_manager.add_task(task_id, data)
                    self._send_json({'success': True, 'message': '同步任务添加成功', 'task_id': task_id})
                except Exception as e:
                    self._send_json({'success': False, 'message': f'添加同步任务失败: {str(e)}'})

            def _update_source(self, source_id, data):
                """更新数据源"""
                try:
                    # UnifiedConfigManager没有update_source方法，需要先删除再添加
                    config_manager.remove_source(source_id)
                    storage_type = data.get('type', 's3')
                    config_manager.add_source(source_id, storage_type, data)
                    self._send_json({'success': True, 'message': '数据源更新成功'})
                except Exception as e:
                    self._send_json({'success': False, 'message': f'更新数据源失败: {str(e)}'})

            def _update_target(self, target_id, data):
                """更新目标存储"""
                try:
                    # UnifiedConfigManager没有update_target方法，需要先删除再添加
                    config_manager.remove_target(target_id)
                    storage_type = data.get('type', 's3')
                    config_manager.add_target(target_id, storage_type, data)
                    self._send_json({'success': True, 'message': '目标存储更新成功'})
                except Exception as e:
                    self._send_json({'success': False, 'message': f'更新目标存储失败: {str(e)}'})

            def _update_task(self, task_id, data):
                """更新同步任务"""
                try:
                    config_manager.update_task(task_id, data)
                    self._send_json({'success': True, 'message': '同步任务更新成功'})
                except Exception as e:
                    self._send_json({'success': False, 'message': f'更新同步任务失败: {str(e)}'})

            def _delete_source(self, source_id):
                """删除数据源"""
                try:
                    config_manager.remove_source(source_id)
                    self._send_json({'success': True, 'message': '数据源删除成功'})
                except Exception as e:
                    self._send_json({'success': False, 'message': f'删除数据源失败: {str(e)}'})

            def _delete_target(self, target_id):
                """删除目标存储"""
                try:
                    config_manager.remove_target(target_id)
                    self._send_json({'success': True, 'message': '目标存储删除成功'})
                except Exception as e:
                    self._send_json({'success': False, 'message': f'删除目标存储失败: {str(e)}'})

            def _delete_task(self, task_id):
                """删除同步任务"""
                try:
                    config_manager.remove_task(task_id)
                    self._send_json({'success': True, 'message': '同步任务删除成功'})
                except Exception as e:
                    self._send_json({'success': False, 'message': f'删除同步任务失败: {str(e)}'})

            def _test_connection(self, data):
                """测试连接"""
                try:
                    # 这里应该调用相应的连接测试方法
                    storage_type = data.get('type', '')
                    if storage_type == 's3':
                        result = self._test_s3_connection(data)
                    elif storage_type == 'sftp':
                        result = self._test_sftp_connection(data)
                    elif storage_type == 'ftp':
                        result = self._test_ftp_connection(data)
                    elif storage_type == 'smb':
                        result = self._test_smb_connection(data)
                    elif storage_type == 'local':
                        result = self._test_local_connection(data)
                    else:
                        result = {'success': False, 'message': f'不支持的存储类型: {storage_type}'}

                    self._send_json(result)
                except Exception as e:
                    self._send_json({'success': False, 'message': f'连接测试失败: {str(e)}'})

            def _test_s3_connection(self, data):
                """测试S3连接"""
                try:
                    from s3_storage_adapter import S3StorageAdapter
                    adapter = S3StorageAdapter(data)
                    adapter.test_connection()
                    return {'success': True, 'message': 'S3连接测试成功'}
                except Exception as e:
                    return {'success': False, 'message': f'S3连接测试失败: {str(e)}'}

            def _test_sftp_connection(self, data):
                """测试SFTP连接"""
                try:
                    from sftp_storage_adapter import SFTPStorageAdapter
                    adapter = SFTPStorageAdapter(data)
                    adapter.test_connection()
                    return {'success': True, 'message': 'SFTP连接测试成功'}
                except Exception as e:
                    return {'success': False, 'message': f'SFTP连接测试失败: {str(e)}'}

            def _test_ftp_connection(self, data):
                """测试FTP连接"""
                try:
                    from ftp_storage_adapter import FTPStorageAdapter
                    adapter = FTPStorageAdapter(data)
                    adapter.test_connection()
                    return {'success': True, 'message': 'FTP连接测试成功'}
                except Exception as e:
                    return {'success': False, 'message': f'FTP连接测试失败: {str(e)}'}

            def _test_smb_connection(self, data):
                """测试SMB连接"""
                try:
                    from smb_storage_adapter import SMBStorageAdapter
                    adapter = SMBStorageAdapter(data)
                    adapter.test_connection()
                    return {'success': True, 'message': 'SMB连接测试成功'}
                except Exception as e:
                    return {'success': False, 'message': f'SMB连接测试失败: {str(e)}'}

            def _test_local_connection(self, data):
                """测试本地存储连接"""
                try:
                    from local_storage_adapter import LocalStorageAdapter
                    adapter = LocalStorageAdapter(data)
                    adapter.test_connection()
                    return {'success': True, 'message': '本地存储连接测试成功'}
                except Exception as e:
                    return {'success': False, 'message': f'本地存储连接测试失败: {str(e)}'}

            def _list_buckets(self, data):
                """列出S3存储桶"""
                print(f"🔍 调试: _list_buckets 被调用，数据: {data}")
                try:
                    from storage_abstraction import S3StorageConfig, StorageType
                    from s3_storage_adapter import S3StorageAdapter

                    # 创建S3配置对象
                    config = S3StorageConfig(
                        storage_type=StorageType.S3,
                        name="临时配置",
                        endpoint=data.get('endpoint', ''),
                        access_key=data.get('access_key', ''),
                        secret_key=data.get('secret_key', ''),
                        bucket="",  # 不指定bucket来列出所有bucket
                        region=data.get('region', '')
                    )
                    print(f"🔍 调试: 配置创建成功，端点: {config.endpoint}")

                    adapter = S3StorageAdapter(config)
                    print(f"🔍 调试: 适配器创建成功")

                    buckets = adapter.list_buckets()
                    print(f"🔍 调试: 获取到 {len(buckets)} 个存储桶")

                    self._send_json({'success': True, 'buckets': buckets})
                except Exception as e:
                    print(f"🔍 调试: 异常: {e}")
                    self._send_json({'success': False, 'message': f'获取存储桶列表失败: {str(e)}', 'buckets': []})

            def _run_task(self, task_id):
                """运行任务"""
                try:
                    task_manager.run_task(task_id)
                    self._send_json({'success': True, 'message': '任务启动成功'})
                except Exception as e:
                    self._send_json({'success': False, 'message': f'任务启动失败: {str(e)}'})

            def _stop_task(self, task_id):
                """停止任务"""
                try:
                    task_manager.stop_task(task_id)
                    self._send_json({'success': True, 'message': '任务停止成功'})
                except Exception as e:
                    self._send_json({'success': False, 'message': f'任务停止失败: {str(e)}'})

            def _save_optimization_config(self, data):
                """保存优化配置"""
                try:
                    config_manager.update_optimization_settings(data)
                    self._send_json({'success': True, 'message': '优化配置保存成功'})
                except Exception as e:
                    self._send_json({'success': False, 'message': f'保存优化配置失败: {str(e)}'})

            def _serve_database_stats(self):
                """获取数据库统计信息"""
                try:
                    from database_optimizer import DatabaseOptimizer
                    optimizer = DatabaseOptimizer()
                    stats = optimizer.get_database_stats()
                    self._send_json({'success': True, 'stats': stats})
                except Exception as e:
                    self._send_json({'success': False, 'message': f'获取数据库统计失败: {str(e)}'})

            def _optimize_database(self, data):
                """执行数据库优化"""
                try:
                    from database_optimizer import DatabaseOptimizer
                    optimizer = DatabaseOptimizer()

                    # 获取优化参数
                    clean_file_hashes_days = data.get('clean_file_hashes_days', 7)
                    clean_executions_days = data.get('clean_executions_days', 30)
                    vacuum = data.get('vacuum', True)

                    # 执行优化
                    results = optimizer.optimize_database(
                        clean_file_hashes_days=clean_file_hashes_days,
                        clean_executions_days=clean_executions_days,
                        vacuum=vacuum
                    )

                    self._send_json({
                        'success': True,
                        'message': f'数据库优化完成，节省空间: {results.get("space_saved_mb", 0)} MB',
                        'results': results
                    })
                except Exception as e:
                    self._send_json({'success': False, 'message': f'数据库优化失败: {str(e)}'})

            def _browse_files(self, data):
                """浏览存储中的文件"""
                try:
                    # 解析URL路径获取存储类型和ID
                    path_parts = self.path.split('/')
                    if len(path_parts) < 5:
                        self._send_json({'success': False, 'message': '无效的API路径'})
                        return

                    storage_type = path_parts[3]  # source 或 target
                    storage_id = path_parts[4]
                    browse_path = data.get('path', '')

                    # 获取存储配置
                    if storage_type == 'source':
                        storage_config = config_manager.get_source(storage_id)
                    elif storage_type == 'target':
                        storage_config = config_manager.get_target(storage_id)
                    else:
                        self._send_json({'success': False, 'message': '无效的存储类型'})
                        return

                    if not storage_config:
                        self._send_json({'success': False, 'message': '存储配置不存在'})
                        return

                    # 创建存储适配器
                    from storage_abstraction import StorageFactory
                    adapter = StorageFactory.create_adapter(storage_config)

                    # 列出文件
                    result = adapter.list_files(prefix=browse_path, max_keys=1000)

                    # 处理文件列表，分离目录和文件
                    files = []
                    directories = set()

                    for file_meta in result.files:
                        # 移除前缀路径
                        relative_path = file_meta.key
                        if browse_path and relative_path.startswith(browse_path):
                            relative_path = relative_path[len(browse_path):].lstrip('/')

                        # 如果路径包含斜杠，说明是子目录中的文件
                        if '/' in relative_path:
                            dir_name = relative_path.split('/')[0]
                            directories.add(dir_name)
                        else:
                            # 直接文件
                            files.append({
                                'name': relative_path,
                                'type': 'file',
                                'size': file_meta.size,
                                'modified': file_meta.last_modified.isoformat() if file_meta.last_modified else '',
                                'path': file_meta.key
                            })

                    # 添加目录到文件列表
                    for dir_name in directories:
                        files.insert(0, {
                            'name': dir_name,
                            'type': 'directory',
                            'size': 0,
                            'modified': '',
                            'path': f"{browse_path}/{dir_name}".strip('/')
                        })

                    # 计算父路径
                    parent_path = ''
                    if browse_path:
                        path_parts = browse_path.rstrip('/').split('/')
                        if len(path_parts) > 1:
                            parent_path = '/'.join(path_parts[:-1])

                    self._send_json({
                        'success': True,
                        'files': files,
                        'current_path': browse_path,
                        'parent_path': parent_path,
                        'total_count': len(files)
                    })

                except Exception as e:
                    self._send_json({'success': False, 'message': f'浏览文件失败: {str(e)}'})

        return RequestHandler
