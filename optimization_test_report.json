{"timestamp": "2025-06-26 20:33:36", "test_results": {"syntax_check": {"passed": true, "errors": []}, "dependencies": {"passed": true, "missing": []}, "config_validation": {"passed": true, "errors": []}, "build": {"passed": true}, "web_interface": {"passed": false, "error": "HTTPConnectionPool(host='localhost', port=8001): Max retries exceeded with url: / (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001C13CF1A660>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))"}, "api_endpoints": {"/api/sources": {"passed": false, "error": "HTTPConnectionPool(host='localhost', port=8001): Max retries exceeded with url: /api/sources (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001C13D436210>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))"}, "/api/targets": {"passed": false, "error": "HTTPConnectionPool(host='localhost', port=8001): Max retries exceeded with url: /api/targets (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001C13D436AD0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))"}, "/api/tasks": {"passed": false, "error": "HTTPConnectionPool(host='localhost', port=8001): Max retries exceeded with url: /api/tasks (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001C13D44A060>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))"}, "/api/file-tree?storage_id=test&path=": {"passed": false, "error": "HTTPConnectionPool(host='localhost', port=8001): Max retries exceeded with url: /api/file-tree?storage_id=test&path= (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001C13D44A780>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。'))"}}}, "optimization_results": {"performance": ["并发线程数优化", "内存管理优化", "连接池优化"], "error_handling": ["异常处理完善", "日志记录优化", "重试机制改进"]}, "summary": {"total_tests": 6, "passed_tests": 4, "success_rate": "66.7%"}}