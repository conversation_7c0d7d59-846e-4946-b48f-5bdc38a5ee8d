#!/usr/bin/env python3
"""
阿里云OSS连接测试脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from storage_abstraction import S3StorageConfig, StorageType
from s3_storage_adapter import S3StorageAdapter

def test_aliyun_oss():
    """测试阿里云OSS连接"""
    print("🔍 测试阿里云OSS连接...")
    
    # 这里需要替换为实际的阿里云OSS配置
    config = S3StorageConfig(
        storage_type=StorageType.S3,
        name="阿里云OSS测试",
        endpoint="https://oss-cn-shanghai.aliyuncs.com",  # 替换为你的区域
        access_key="your-access-key",  # 替换为你的Access Key
        secret_key="your-secret-key",  # 替换为你的Secret Key
        bucket="jayce-s3test",  # 替换为你的bucket名称
        region="cn-shanghai"  # 替换为你的区域
    )
    
    adapter = S3StorageAdapter(config)
    
    print(f"📋 配置信息:")
    print(f"   端点: {config.endpoint}")
    print(f"   存储桶: {config.bucket}")
    print(f"   区域: {config.region}")
    
    # 测试连接
    print("\n🔗 测试连接...")
    success, message = adapter.test_connection()
    
    if success:
        print(f"✅ {message}")
        
        # 尝试列出文件
        print("\n📁 尝试列出文件...")
        try:
            result = adapter.list_files(max_keys=5)
            print(f"✅ 成功列出 {len(result.files)} 个文件")
            for file in result.files[:3]:  # 显示前3个文件
                print(f"   - {file.key} ({file.size} bytes)")
        except Exception as e:
            print(f"❌ 列出文件失败: {e}")
    else:
        print(f"❌ {message}")
        
        # 提供调试信息
        print("\n🔧 调试信息:")
        print("   请检查以下配置:")
        print("   1. Access Key ID 是否正确")
        print("   2. Secret Access Key 是否正确")
        print("   3. 存储桶名称是否正确")
        print("   4. 区域设置是否正确")
        print("   5. 存储桶是否存在且有访问权限")

if __name__ == "__main__":
    test_aliyun_oss()
