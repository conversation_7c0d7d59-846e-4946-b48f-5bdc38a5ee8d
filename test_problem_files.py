#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试问题文件
"""

import smb_storage_adapter
from storage_abstraction import StorageFactory, SMBStorageConfig, StorageType

def test_problem_files():
    """测试问题文件"""
    print("🧪 测试问题文件")
    
    # 创建SMB配置
    config = SMBStorageConfig(
        storage_type=StorageType.SMB,
        hostname='<PERSON><PERSON>',
        port=445,
        username='smb',
        password='smbsmb',
        domain='WORKGROUP',
        share_name='hlmj',
        root_path=''
    )
    
    # 创建适配器
    adapter = StorageFactory.create_adapter(config)
    
    # 测试特定的问题文件
    problem_files = ['TerSafe.dll', 'TP3Helper.exe', 'UnityPlayer.dll']
    
    for file_key in problem_files:
        print(f"\n测试文件: {file_key}")
        print(f"  文件名类型: {type(file_key)}")
        print(f"  文件名编码: {repr(file_key)}")
        
        try:
            # 尝试下载
            data = adapter.get_file(file_key)
            if data:
                print(f"  ✅ 下载成功: {len(data)} bytes")
            else:
                print(f"  ❌ 下载失败: 返回None")
        except Exception as e:
            print(f"  ❌ 下载异常: {e}")

if __name__ == "__main__":
    test_problem_files()
