#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试路径修复
"""

import smb_storage_adapter
from storage_abstraction import StorageFactory, SMBStorageConfig, StorageType

def test_path_fix():
    """测试路径修复"""
    print("🧪 测试路径修复")
    
    # 创建SMB配置
    config = SMBStorageConfig(
        storage_type=StorageType.SMB,
        hostname='<PERSON><PERSON>',
        port=445,
        username='smb',
        password='smbsmb',
        domain='WORKGROUP',
        share_name='hlmj',
        root_path=''
    )
    
    # 创建适配器
    adapter = StorageFactory.create_adapter(config)
    
    # 测试带路径的文件
    test_files = [
        'HLMJ_Data/Managed/Assembly-CSharp.dll',
        'HLMJ_Data/level5',
        'TerSafe.dll',
        '398.xml'
    ]
    
    for file_key in test_files:
        print(f"\n测试文件: {file_key}")
        
        # 测试路径规范化
        normalized_path = adapter._normalize_path(file_key)
        print(f"  规范化路径: {repr(normalized_path)}")
        
        try:
            # 尝试下载
            data = adapter._simple_download(file_key)
            if data:
                print(f"  ✅ 下载成功: {len(data)} bytes")
            else:
                print(f"  ❌ 下载失败: 返回None")
        except Exception as e:
            print(f"  ❌ 下载异常: {e}")

if __name__ == "__main__":
    test_path_fix()
