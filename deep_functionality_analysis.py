#!/usr/bin/env python3
"""
深入功能分析 - 检查用户提到的具体问题
"""

import requests
import json
import time
from pathlib import Path

class DeepFunctionalityAnalyzer:
    """深入功能分析器"""
    
    def __init__(self, base_url: str = "http://localhost:8001"):
        self.base_url = base_url
        self.issues_found = []
        
    def log_issue(self, category: str, issue: str, severity: str = "HIGH"):
        """记录发现的问题"""
        self.issues_found.append({
            "category": category,
            "issue": issue,
            "severity": severity,
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        })
        print(f"🔍 [{severity}] {category}: {issue}")
        
    def test_edit_storage_forms(self):
        """测试编辑存储配置表单问题"""
        print("\n📝 测试编辑存储配置表单...")
        
        # 这个问题需要通过代码分析来验证
        # 从代码中可以看到editSource和editTarget函数只填充S3字段
        self.log_issue(
            "编辑表单", 
            "编辑数据源/目标时只显示S3参数字段，不根据存储类型动态显示",
            "HIGH"
        )
        
        # 具体问题：
        # 1. edit-source页面HTML只有S3字段
        # 2. editSource()函数只填充S3相关字段
        # 3. 缺少存储类型选择和动态字段更新
        
        print("   - 编辑页面HTML模板只包含S3字段")
        print("   - editSource/editTarget函数只填充S3参数")
        print("   - 缺少根据存储类型动态显示字段的逻辑")
        
    def test_task_compression_options(self):
        """测试任务配置中的压缩传输选项"""
        print("\n📦 测试任务压缩传输配置...")
        
        try:
            # 获取现有任务
            response = requests.get(f"{self.base_url}/api/tasks")
            if response.status_code == 200:
                tasks = response.json()
                
                # 检查任务配置中是否有压缩传输选项
                for task_id, task in tasks.items():
                    print(f"   任务 {task.get('name', task_id)}:")
                    
                    # 检查是否有压缩传输相关配置
                    compression_fields = ['compression_enabled', 'compression_format', 'use_compression']
                    has_compression = any(field in task for field in compression_fields)
                    
                    if not has_compression:
                        self.log_issue(
                            "任务配置",
                            f"任务 {task_id} 缺少压缩传输配置选项",
                            "MEDIUM"
                        )
                    
                    # 检查文件选择功能
                    if 'selected_files' not in task and 'file_selection' not in task:
                        self.log_issue(
                            "文件选择",
                            f"任务 {task_id} 缺少文件选择功能配置",
                            "MEDIUM"
                        )
                        
        except Exception as e:
            self.log_issue("API测试", f"获取任务配置失败: {e}", "HIGH")
            
    def test_file_tree_functionality(self):
        """测试文件选择/目录列表功能"""
        print("\n🌳 测试文件树/目录列表功能...")
        
        try:
            # 获取数据源列表
            response = requests.get(f"{self.base_url}/api/sources")
            if response.status_code == 200:
                sources = response.json()
                
                for source_id, source in sources.items():
                    storage_type = source.get('storage_type', 's3')
                    print(f"   测试 {source.get('name', source_id)} ({storage_type}) 的文件列表...")
                    
                    # 测试文件树API
                    try:
                        tree_response = requests.get(
                            f"{self.base_url}/api/file-tree?storage_id={source_id}&path=",
                            timeout=10
                        )
                        
                        if tree_response.status_code == 200:
                            tree_data = tree_response.json()
                            if tree_data.get('success'):
                                files = tree_data.get('files', [])
                                print(f"     ✅ 成功列出 {len(files)} 个文件/目录")
                            else:
                                error_msg = tree_data.get('message', '未知错误')
                                self.log_issue(
                                    "文件列表",
                                    f"{storage_type} 存储 {source_id} 文件列表失败: {error_msg}",
                                    "HIGH"
                                )
                        else:
                            self.log_issue(
                                "文件列表",
                                f"{storage_type} 存储 {source_id} 文件列表API返回 {tree_response.status_code}",
                                "HIGH"
                            )
                            
                    except Exception as e:
                        self.log_issue(
                            "文件列表",
                            f"{storage_type} 存储 {source_id} 文件列表请求异常: {e}",
                            "HIGH"
                        )
                        
        except Exception as e:
            self.log_issue("API测试", f"获取数据源失败: {e}", "HIGH")
            
    def analyze_filter_differences(self):
        """分析排除文件和文件过滤的区别"""
        print("\n🔍 分析排除文件和文件过滤功能...")
        
        # 从代码分析可以看出：
        print("   根据代码分析:")
        print("   - file_filter: 包含过滤，只同步匹配的文件")
        print("   - exclude_filter: 排除过滤，排除匹配的文件")
        print("   - 两者都支持通配符模式")
        
        # 但是用户界面可能没有清楚说明这个区别
        self.log_issue(
            "用户界面",
            "排除文件和文件过滤功能区别不够明确，缺少说明文档",
            "MEDIUM"
        )
        
    def test_bandwidth_limit_feature(self):
        """测试带宽限制功能"""
        print("\n🚀 测试带宽限制功能...")
        
        # 从代码中可以看到有bandwidth_limit字段，但需要检查实际实现
        try:
            response = requests.get(f"{self.base_url}/api/tasks")
            if response.status_code == 200:
                tasks = response.json()
                
                bandwidth_implemented = False
                for task_id, task in tasks.items():
                    if 'bandwidth_limit' in task:
                        bandwidth_implemented = True
                        limit = task.get('bandwidth_limit', 0)
                        print(f"   任务 {task_id} 带宽限制: {limit} MB/s")
                        
                if not bandwidth_implemented:
                    self.log_issue(
                        "带宽限制",
                        "任务配置中未发现带宽限制功能实现",
                        "MEDIUM"
                    )
                else:
                    # 需要进一步测试实际是否生效
                    self.log_issue(
                        "带宽限制",
                        "带宽限制配置存在，但需要验证实际传输时是否生效",
                        "LOW"
                    )
                    
        except Exception as e:
            self.log_issue("API测试", f"测试带宽限制失败: {e}", "HIGH")
            
    def test_file_chunking_feature(self):
        """测试文件切片功能"""
        print("\n✂️ 测试文件切片功能...")
        
        try:
            response = requests.get(f"{self.base_url}/api/tasks")
            if response.status_code == 200:
                tasks = response.json()
                
                chunking_implemented = False
                for task_id, task in tasks.items():
                    chunk_threshold = task.get('chunk_threshold')
                    chunk_size = task.get('chunk_size')
                    
                    if chunk_threshold is not None and chunk_size is not None:
                        chunking_implemented = True
                        print(f"   任务 {task_id} 切片配置:")
                        print(f"     - 切片阈值: {chunk_threshold} MB")
                        print(f"     - 切片大小: {chunk_size} MB")
                        
                if not chunking_implemented:
                    self.log_issue(
                        "文件切片",
                        "任务配置中未发现文件切片功能",
                        "MEDIUM"
                    )
                else:
                    # 检查是否有开关控制
                    self.log_issue(
                        "文件切片",
                        "文件切片功能存在但可能缺少启用/禁用开关",
                        "LOW"
                    )
                    
        except Exception as e:
            self.log_issue("API测试", f"测试文件切片失败: {e}", "HIGH")
            
    def test_integrity_verification(self):
        """测试完整性验证功能"""
        print("\n🔐 测试完整性验证功能...")
        
        try:
            response = requests.get(f"{self.base_url}/api/tasks")
            if response.status_code == 200:
                tasks = response.json()
                
                for task_id, task in tasks.items():
                    verify_integrity = task.get('verify_integrity', False)
                    print(f"   任务 {task_id} 完整性验证: {'启用' if verify_integrity else '禁用'}")
                    
                    if verify_integrity:
                        # 检查验证方式
                        verification_method = task.get('verification_method', '未指定')
                        if verification_method == '未指定':
                            self.log_issue(
                                "完整性验证",
                                f"任务 {task_id} 启用了完整性验证但未指定验证方式",
                                "MEDIUM"
                            )
                        else:
                            print(f"     验证方式: {verification_method}")
                            
        except Exception as e:
            self.log_issue("API测试", f"测试完整性验证失败: {e}", "HIGH")
            
    def test_compression_storage_compatibility(self):
        """测试压缩传输与存储类型的兼容性"""
        print("\n🗜️ 测试压缩传输存储兼容性...")
        
        # 根据代码分析，压缩传输主要适用于SFTP/FTP
        compatible_types = ['sftp', 'ftp']
        incompatible_types = ['s3', 'local']
        
        try:
            response = requests.get(f"{self.base_url}/api/sources")
            if response.status_code == 200:
                sources = response.json()
                
                for source_id, source in sources.items():
                    storage_type = source.get('storage_type', 's3')
                    
                    if storage_type in compatible_types:
                        print(f"   ✅ {storage_type} 存储支持压缩传输")
                    elif storage_type in incompatible_types:
                        print(f"   ⚠️ {storage_type} 存储不适合压缩传输")
                        
                # 检查界面是否有相应的提示
                self.log_issue(
                    "压缩传输",
                    "界面可能缺少对不同存储类型压缩传输适用性的说明",
                    "LOW"
                )
                
        except Exception as e:
            self.log_issue("API测试", f"测试压缩传输兼容性失败: {e}", "HIGH")
            
    def run_deep_analysis(self):
        """运行深入分析"""
        print("🔬 开始深入功能分析")
        print("=" * 60)
        
        # 测试各项功能
        self.test_edit_storage_forms()
        self.test_task_compression_options()
        self.test_file_tree_functionality()
        self.analyze_filter_differences()
        self.test_bandwidth_limit_feature()
        self.test_file_chunking_feature()
        self.test_integrity_verification()
        self.test_compression_storage_compatibility()
        
        # 生成分析报告
        self.generate_analysis_report()
        
    def generate_analysis_report(self):
        """生成分析报告"""
        print("\n" + "=" * 60)
        print("📊 深入功能分析报告")
        print("=" * 60)
        
        # 按严重程度分类
        high_issues = [issue for issue in self.issues_found if issue['severity'] == 'HIGH']
        medium_issues = [issue for issue in self.issues_found if issue['severity'] == 'MEDIUM']
        low_issues = [issue for issue in self.issues_found if issue['severity'] == 'LOW']
        
        print(f"发现问题总数: {len(self.issues_found)}")
        print(f"  - 高优先级: {len(high_issues)}")
        print(f"  - 中优先级: {len(medium_issues)}")
        print(f"  - 低优先级: {len(low_issues)}")
        
        if high_issues:
            print("\n🚨 高优先级问题:")
            for issue in high_issues:
                print(f"  - [{issue['category']}] {issue['issue']}")
                
        if medium_issues:
            print("\n⚠️ 中优先级问题:")
            for issue in medium_issues:
                print(f"  - [{issue['category']}] {issue['issue']}")
                
        if low_issues:
            print("\n💡 低优先级问题:")
            for issue in low_issues:
                print(f"  - [{issue['category']}] {issue['issue']}")
                
        # 保存详细报告
        report_file = "deep_functionality_analysis_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump({
                "summary": {
                    "total_issues": len(self.issues_found),
                    "high_priority": len(high_issues),
                    "medium_priority": len(medium_issues),
                    "low_priority": len(low_issues)
                },
                "issues": self.issues_found
            }, f, ensure_ascii=False, indent=2)
            
        print(f"\n📄 详细报告已保存到: {report_file}")

def main():
    """主函数"""
    analyzer = DeepFunctionalityAnalyzer()
    
    try:
        analyzer.run_deep_analysis()
    except KeyboardInterrupt:
        print("\n⚠️ 分析被用户中断")
    except Exception as e:
        print(f"\n❌ 分析过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
