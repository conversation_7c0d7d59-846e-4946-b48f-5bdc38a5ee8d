#!/usr/bin/env python3
"""
LightRek 统一存储同步工具 - 全面功能测试
测试所有存储类型、Web界面功能和同步操作
"""

import os
import sys
import json
import time
import requests
import tempfile
import shutil
from pathlib import Path
from typing import Dict, Any, List

class LightRekFunctionalityTester:
    """LightRek功能测试器"""
    
    def __init__(self, base_url: str = "http://localhost:8001"):
        self.base_url = base_url
        self.test_results = []
        self.demo_data_dir = Path("demo_data")
        self.temp_dirs = []
        
    def log_test(self, test_name: str, success: bool, message: str = ""):
        """记录测试结果"""
        result = {
            "test": test_name,
            "success": success,
            "message": message,
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }
        self.test_results.append(result)
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{status} {test_name}: {message}")
        
    def setup_demo_data(self):
        """设置演示数据"""
        print("🔧 设置演示数据...")
        
        # 创建演示目录
        source_dir = self.demo_data_dir / "source"
        target_dir = self.demo_data_dir / "target"
        
        source_dir.mkdir(parents=True, exist_ok=True)
        target_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建测试文件
        test_files = [
            "test1.txt",
            "test2.json",
            "folder1/nested_file.txt",
            "folder2/data.csv"
        ]
        
        for file_path in test_files:
            full_path = source_dir / file_path
            full_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(full_path, 'w', encoding='utf-8') as f:
                f.write(f"测试文件内容: {file_path}\n创建时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        self.log_test("演示数据设置", True, f"创建了{len(test_files)}个测试文件")
        return source_dir, target_dir
        
    def test_web_interface_accessibility(self):
        """测试Web界面可访问性"""
        try:
            response = requests.get(self.base_url, timeout=10)
            if response.status_code == 200:
                self.log_test("Web界面可访问性", True, "Web界面正常访问")
                return True
            else:
                self.log_test("Web界面可访问性", False, f"HTTP状态码: {response.status_code}")
                return False
        except Exception as e:
            self.log_test("Web界面可访问性", False, f"连接失败: {e}")
            return False
            
    def test_api_endpoints(self):
        """测试API端点"""
        endpoints = [
            "/api/sources",
            "/api/targets",
            "/api/tasks",
            "/api/statistics"
        ]

        for endpoint in endpoints:
            try:
                response = requests.get(f"{self.base_url}{endpoint}", timeout=5)
                if response.status_code == 200:
                    self.log_test(f"API端点 {endpoint}", True, "响应正常")
                else:
                    self.log_test(f"API端点 {endpoint}", False, f"状态码: {response.status_code}")
            except Exception as e:
                self.log_test(f"API端点 {endpoint}", False, f"请求失败: {e}")
                
    def test_storage_configurations(self):
        """测试存储配置"""
        storage_configs = {
            "local_source": {
                "storage_type": "local",
                "name": "本地测试源",
                "description": "本地文件系统测试源",
                "root_path": str(self.demo_data_dir / "source")
            },
            "local_target": {
                "storage_type": "local", 
                "name": "本地测试目标",
                "description": "本地文件系统测试目标",
                "root_path": str(self.demo_data_dir / "target")
            },
            "sftp_test": {
                "storage_type": "sftp",
                "name": "SFTP测试",
                "description": "SFTP连接测试",
                "hostname": "test.example.com",
                "port": 22,
                "username": "testuser",
                "password": "testpass",
                "root_path": "/home/<USER>"
            }
        }
        
        for storage_id, config in storage_configs.items():
            try:
                # 测试添加数据源 - 使用正确的API端点
                response = requests.post(
                    f"{self.base_url}/api/sources",
                    json=config,
                    timeout=10
                )

                if response.status_code == 200:
                    result = response.json()
                    if result.get("success"):
                        self.log_test(f"添加存储配置 {storage_id}", True, "配置添加成功")
                    else:
                        self.log_test(f"添加存储配置 {storage_id}", False, result.get("message", "未知错误"))
                else:
                    self.log_test(f"添加存储配置 {storage_id}", False, f"HTTP {response.status_code}")

            except Exception as e:
                self.log_test(f"添加存储配置 {storage_id}", False, f"请求异常: {e}")
                
    def test_task_creation(self):
        """测试任务创建"""
        task_config = {
            "task_id": "test_sync_task",
            "name": "测试同步任务",
            "description": "本地到本地的测试同步任务",
            "source_id": "local_source",
            "target_id": "local_target",
            "sync_mode": "incremental",
            "schedule_type": "manual",
            "enabled": True,
            "prefix": ""
        }
        
        try:
            response = requests.post(
                f"{self.base_url}/api/tasks",
                json=task_config,
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    self.log_test("任务创建", True, "同步任务创建成功")
                    return True
                else:
                    self.log_test("任务创建", False, result.get("message", "未知错误"))
                    return False
            else:
                self.log_test("任务创建", False, f"HTTP {response.status_code}")
                return False
                
        except Exception as e:
            self.log_test("任务创建", False, f"请求异常: {e}")
            return False
            
    def test_task_execution(self):
        """测试任务执行"""
        try:
            response = requests.post(
                f"{self.base_url}/api/tasks/test_sync_task/run",
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    self.log_test("任务执行", True, "同步任务启动成功")
                    
                    # 等待任务完成
                    time.sleep(5)
                    
                    # 检查目标目录是否有文件
                    target_dir = self.demo_data_dir / "target"
                    if any(target_dir.rglob("*")):
                        self.log_test("文件同步验证", True, "文件已成功同步到目标目录")
                    else:
                        self.log_test("文件同步验证", False, "目标目录中未发现同步文件")
                        
                    return True
                else:
                    self.log_test("任务执行", False, result.get("message", "未知错误"))
                    return False
            else:
                self.log_test("任务执行", False, f"HTTP {response.status_code}")
                return False
                
        except Exception as e:
            self.log_test("任务执行", False, f"请求异常: {e}")
            return False
            
    def test_connection_testing(self):
        """测试连接测试功能"""
        test_configs = [
            {
                "storage_type": "local",
                "root_path": str(self.demo_data_dir / "source")
            }
        ]
        
        for config in test_configs:
            try:
                storage_type = config["storage_type"]
                response = requests.post(
                    f"{self.base_url}/api/test-connection",
                    json=config,
                    timeout=10
                )

                if response.status_code == 200:
                    result = response.json()
                    if result.get("success"):
                        self.log_test(f"连接测试 {storage_type}", True, "连接测试成功")
                    else:
                        self.log_test(f"连接测试 {storage_type}", False, result.get("message", "连接失败"))
                else:
                    self.log_test(f"连接测试 {storage_type}", False, f"HTTP {response.status_code}")

            except Exception as e:
                self.log_test(f"连接测试 {storage_type}", False, f"请求异常: {e}")
                
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始LightRek统一存储同步工具全面功能测试")
        print("=" * 60)
        
        # 设置演示数据
        self.setup_demo_data()
        
        # 测试Web界面
        if not self.test_web_interface_accessibility():
            print("❌ Web界面不可访问，跳过后续测试")
            return
            
        # 测试API端点
        self.test_api_endpoints()
        
        # 测试存储配置
        self.test_storage_configurations()
        
        # 测试连接功能
        self.test_connection_testing()
        
        # 测试任务创建
        if self.test_task_creation():
            # 测试任务执行
            self.test_task_execution()
            
        # 生成测试报告
        self.generate_report()
        
    def generate_report(self):
        """生成测试报告"""
        print("\n" + "=" * 60)
        print("📊 测试报告")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests
        
        print(f"总测试数: {total_tests}")
        print(f"通过: {passed_tests}")
        print(f"失败: {failed_tests}")
        print(f"成功率: {passed_tests/total_tests*100:.1f}%")
        
        if failed_tests > 0:
            print("\n❌ 失败的测试:")
            for result in self.test_results:
                if not result["success"]:
                    print(f"  - {result['test']}: {result['message']}")
                    
        # 保存详细报告
        report_file = "comprehensive_test_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump({
                "summary": {
                    "total": total_tests,
                    "passed": passed_tests,
                    "failed": failed_tests,
                    "success_rate": passed_tests/total_tests*100
                },
                "details": self.test_results
            }, f, ensure_ascii=False, indent=2)
            
        print(f"\n📄 详细报告已保存到: {report_file}")
        
    def cleanup(self):
        """清理测试数据"""
        try:
            if self.demo_data_dir.exists():
                shutil.rmtree(self.demo_data_dir)
            for temp_dir in self.temp_dirs:
                if temp_dir.exists():
                    shutil.rmtree(temp_dir)
            print("🧹 测试数据清理完成")
        except Exception as e:
            print(f"⚠️ 清理失败: {e}")

def main():
    """主函数"""
    tester = LightRekFunctionalityTester()
    
    try:
        tester.run_all_tests()
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 询问是否清理测试数据
        try:
            cleanup = input("\n是否清理测试数据? (y/N): ").lower().strip()
            if cleanup == 'y':
                tester.cleanup()
        except:
            pass

if __name__ == "__main__":
    main()
