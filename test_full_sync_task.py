#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试完整的同步任务
"""

import logging
import sys
import os
import tempfile
import shutil

# 设置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_full_sync_task():
    """测试完整的同步任务"""
    print("🧪 测试完整的同步任务")
    
    try:
        from unified_task_manager import UnifiedTaskManager, UnifiedSyncTask
        from unified_config_manager import UnifiedConfigManager
        from storage_abstraction import SMBStorageConfig, LocalStorageConfig, StorageType
        
        # 创建配置管理器和任务管理器
        config_manager = UnifiedConfigManager()
        task_manager = UnifiedTaskManager(config_manager)
        
        # 创建临时目标目录
        temp_dir = tempfile.mkdtemp(prefix='smb_sync_test_')
        print(f"临时目标目录: {temp_dir}")
        
        try:
            # 创建SMB源配置
            source_config = SMBStorageConfig(
                storage_type=StorageType.SMB,
                hostname='Jay<PERSON>',
                port=445,
                username='smb',
                password='smbsmb',
                domain='WORKGROUP',
                share_name='hlmj',
                root_path=''
            )
            
            # 创建本地目标配置
            target_config = LocalStorageConfig(
                storage_type=StorageType.LOCAL,
                root_path=temp_dir
            )
            
            # 创建同步任务
            task = UnifiedSyncTask(
                task_id='test-full-sync',
                name='完整同步测试',
                source_config=source_config,
                target_config=target_config,
                sync_mode='full',
                prefix='',
                verify_integrity=False,
                delete_extra=False,
                retry_times=2,
                retry_delay=1,
                bandwidth_limit=0,
                chunk_size=1024,
                chunk_threshold=10,
                enable_compression=False,
                compression_level=6,
                file_filter='',
                exclude_patterns=[],
                include_patterns=['398.xml', '398_MonitorData.ini'],  # 只同步这两个文件
                max_workers=1,
                enable_deduplication=False,
                enable_encryption=False,
                encryption_key='',
                schedule_type='manual',
                schedule_config={},
                notification_config={},
                advanced_options={}
            )
            
            print("开始执行同步任务...")
            
            # 执行任务
            success = task_manager.execute_task(task.task_id, task)
            
            if success:
                print("✅ 任务执行成功")
                
                # 检查同步的文件
                synced_files = []
                for root, dirs, files in os.walk(temp_dir):
                    for file in files:
                        file_path = os.path.join(root, file)
                        file_size = os.path.getsize(file_path)
                        rel_path = os.path.relpath(file_path, temp_dir)
                        synced_files.append((rel_path, file_size))
                        print(f"  同步文件: {rel_path} ({file_size} bytes)")
                
                if len(synced_files) >= 2:
                    print(f"✅ 成功同步 {len(synced_files)} 个文件")
                    return True
                else:
                    print(f"❌ 只同步了 {len(synced_files)} 个文件，期望至少2个")
                    return False
            else:
                print("❌ 任务执行失败")
                return False
                
        finally:
            # 清理临时目录
            try:
                shutil.rmtree(temp_dir)
                print(f"清理临时目录: {temp_dir}")
            except:
                pass
            
    except Exception as e:
        print(f"❌ 异常: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    if test_full_sync_task():
        print("🎉 完整同步任务测试成功！")
        print("")
        print("✅ SMB子进程隔离修复方案完全正常工作！")
        print("✅ 现在可以重新运行实际的同步任务了！")
        sys.exit(0)
    else:
        print("❌ 完整同步任务测试失败！")
        sys.exit(1)
