{"timestamp": "2025-06-26 20:54:47", "summary": {"total_categories": 8, "passed_categories": 3, "total_tests": 28, "passed_tests": 8, "success_rate": "28.6%"}, "detailed_results": {"storage_configurations": {"s3_aws": {"add_success": true, "validation": {"required_fields": true, "field_types": true, "logical_consistency": true, "issues": []}}, "s3_aliyun": {"add_success": true, "validation": {"required_fields": true, "field_types": true, "logical_consistency": true, "issues": []}}, "sftp": {"add_success": true, "validation": {"required_fields": true, "field_types": true, "logical_consistency": true, "issues": []}}, "smb": {"add_success": true, "validation": {"required_fields": true, "field_types": true, "logical_consistency": true, "issues": []}}, "ftp": {"add_success": true, "validation": {"required_fields": true, "field_types": true, "logical_consistency": true, "issues": []}}}, "parameter_validation": [{"passed": false, "status": 200}, {"passed": false, "status": 200}, {"passed": false, "status": 200}], "configuration_logic": [{"test": "port_0", "passed": false}, {"test": "port_65536", "passed": false}, {"test": "port_22", "passed": true}, {"test": "port_443", "passed": true}, {"test": "url_invalid-url", "passed": false}, {"test": "url_http://example.com", "passed": true}, {"test": "url_https://s3.amazonaws.com", "passed": true}], "api_completeness": {"GET": {"/api/sources": {"status": 200, "available": true}, "/api/targets": {"status": 200, "available": true}, "/api/tasks": {"status": 200, "available": true}, "/api/task-executions": {"status": 200, "available": true}, "/api/file-tree?storage_id=test&path=": {"status": 200, "available": true}}, "POST": {"/api/sources": {"status": 200, "available": true}, "/api/targets": {"status": 200, "available": true}, "/api/tasks": {"status": 200, "available": true}, "/api/compressed-transfer": {"status": 200, "available": true}}, "PUT": {"/api/sources/test": {"status": 200, "available": true}, "/api/targets/test": {"status": 200, "available": true}, "/api/tasks/test": {"status": 200, "available": true}}, "DELETE": {"/api/sources/test": {"status": 200, "available": true}, "/api/targets/test": {"status": 200, "available": true}, "/api/tasks/test": {"status": 200, "available": true}}}, "error_handling": [{"scenario": "无效JSON", "handled_correctly": false, "status_code": 200}, {"scenario": "超大请求", "handled_correctly": false, "status_code": 200}, {"scenario": "不存在的资源", "handled_correctly": true, "status_code": 404}, {"scenario": "无效参数", "handled_correctly": false, "status_code": 200}], "boundary_conditions": [{"test": "name_length_0", "passed": false}, {"test": "name_length_1", "passed": true}, {"test": "name_length_255", "passed": true}, {"test": "name_length_1000", "passed": false}, {"test": "port_boundary_-1", "passed": false}, {"test": "port_boundary_0", "passed": false}, {"test": "port_boundary_1", "passed": true}, {"test": "port_boundary_65535", "passed": true}, {"test": "port_boundary_65536", "passed": false}], "web_interface": [{"page": "/", "accessible": true}, {"page": "/index.html", "accessible": false, "status": 404}, {"page": "/api/sources", "accessible": true}, {"page": "/api/targets", "accessible": true}, {"page": "/api/tasks", "accessible": true}], "actual_transfer": {"setup_success": true, "test_file_created": true, "storage_configured": true}}}