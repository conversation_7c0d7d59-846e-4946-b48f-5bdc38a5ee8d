#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LightRek 统一存储同步工具 - 静态文件Web服务器
"""

import os
import json
import uuid
import threading
import mimetypes
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs


class StaticWebServer:
    """静态文件Web服务器"""
    
    def __init__(self, config_manager, task_manager, db_manager=None, port=8001):
        """
        初始化静态文件Web服务器
        
        Args:
            config_manager: 配置管理器
            task_manager: 任务管理器
            db_manager: 数据库管理器（可选）
            port: 服务器端口
        """
        self.config_manager = config_manager
        self.task_manager = task_manager
        self.db_manager = db_manager
        self.port = port
        self.server = None
        self.server_thread = None
        self.running = False
        self.web_root = os.path.join(os.path.dirname(__file__), 'web')
    
    def create_handler_class(self):
        """创建请求处理器类"""
        config_manager = self.config_manager
        task_manager = self.task_manager
        db_manager = self.db_manager
        web_root = self.web_root
        
        class RequestHandler(BaseHTTPRequestHandler):
            def do_GET(self):
                # 解析URL
                parsed_url = urlparse(self.path)
                path = parsed_url.path
                
                # API请求
                if path.startswith('/api/'):
                    self._handle_api_get(path)
                # 下载文件请求
                elif path.startswith('/downloads/'):
                    self._serve_download_file(path)
                # 静态文件请求
                else:
                    self._serve_static_file(path)
            
            def do_POST(self):
                # 解析URL
                parsed_url = urlparse(self.path)
                path = parsed_url.path
                
                if path.startswith('/api/'):
                    self._handle_api_post(path)
                else:
                    self._send_404()
            
            def do_PUT(self):
                # 解析URL
                parsed_url = urlparse(self.path)
                path = parsed_url.path
                
                if path.startswith('/api/'):
                    self._handle_api_put(path)
                else:
                    self._send_404()
            
            def do_DELETE(self):
                # 解析URL
                parsed_url = urlparse(self.path)
                path = parsed_url.path
                
                if path.startswith('/api/'):
                    self._handle_api_delete(path)
                else:
                    self._send_404()
            
            def _serve_static_file(self, path):
                """提供静态文件"""
                # 默认文件
                if path == '/' or path == '':
                    path = '/index.html'
                
                # 构建文件路径
                file_path = os.path.join(web_root, path.lstrip('/'))
                
                # 检查文件是否存在
                if not os.path.exists(file_path) or not os.path.isfile(file_path):
                    self._send_404()
                    return
                
                # 获取MIME类型
                mime_type, _ = mimetypes.guess_type(file_path)
                if mime_type is None:
                    mime_type = 'application/octet-stream'
                
                try:
                    # 读取文件内容
                    with open(file_path, 'rb') as f:
                        content = f.read()
                    
                    # 发送响应
                    self.send_response(200)
                    self.send_header('Content-Type', mime_type)
                    self.send_header('Content-Length', str(len(content)))
                    self.end_headers()
                    self.wfile.write(content)
                    
                except Exception as e:
                    print(f"读取文件失败: {e}")
                    self._send_500()

            def _serve_download_file(self, path):
                """提供下载文件"""
                try:
                    # 获取文件名
                    filename = path.split('/')[-1]
                    file_path = os.path.join(os.path.dirname(__file__), 'downloads', filename)

                    if not os.path.exists(file_path):
                        self._send_404()
                        return

                    # 读取文件
                    with open(file_path, 'rb') as f:
                        content = f.read()

                    # 设置响应头
                    self.send_response(200)
                    self.send_header('Content-Type', 'application/zip')
                    self.send_header('Content-Disposition', f'attachment; filename="{filename}"')
                    self.send_header('Content-Length', str(len(content)))
                    self.end_headers()

                    # 发送文件内容
                    self.wfile.write(content)

                    # 下载完成后删除文件
                    try:
                        os.unlink(file_path)
                    except:
                        pass

                except Exception as e:
                    print(f"提供下载文件失败: {e}")
                    self._send_500()
            
            def _handle_api_get(self, path):
                """处理API GET请求"""
                if path == '/api/sources':
                    self._api_get_sources()
                elif path == '/api/targets':
                    self._api_get_targets()
                elif path == '/api/tasks':
                    self._api_get_tasks()
                elif path == '/api/task-status':
                    self._api_get_task_status()
                elif path == '/api/task-executions':
                    self._api_get_task_executions()
                elif path == '/api/statistics':
                    self._api_get_statistics()
                elif path == '/api/optimization-config':
                    self._api_get_optimization_config()
                elif path.startswith('/api/task-logs/'):
                    execution_id = path.split('/')[-1]
                    self._api_get_task_logs(execution_id)
                else:
                    self._send_404()
            
            def _handle_api_post(self, path):
                """处理API POST请求"""
                data = self._get_post_data()
                
                if path == '/api/sources':
                    self._api_add_source(data)
                elif path == '/api/targets':
                    self._api_add_target(data)
                elif path == '/api/tasks':
                    self._api_add_task(data)
                elif path == '/api/test-connection':
                    self._api_test_connection(data)
                elif path == '/api/list-buckets':
                    self._api_list_buckets(data)
                elif path.startswith('/api/tasks/') and path.endswith('/run'):
                    task_id = path.split('/')[-2]
                    self._api_run_task(task_id)
                elif path.startswith('/api/tasks/') and path.endswith('/stop'):
                    task_id = path.split('/')[-2]
                    self._api_stop_task(task_id)
                elif path == '/api/optimization-config':
                    self._api_save_optimization_config(data)
                elif path == '/api/clean-logs':
                    self._api_clean_logs()
                elif path.startswith('/api/executions/') and path.endswith('/delete'):
                    execution_id = path.split('/')[-2]
                    self._api_delete_execution(execution_id)
                elif path.startswith('/api/browse/'):
                    result = self._api_browse_storage(path, data)
                    self._send_json(result)
                elif path.startswith('/api/download/'):
                    result = self._api_download_files(path, data)
                    self._send_json(result)
                elif path.startswith('/api/file-info/'):
                    result = self._api_get_file_info(path)
                    self._send_json(result)
                else:
                    self._send_404()
            
            def _handle_api_put(self, path):
                """处理API PUT请求"""
                data = self._get_post_data()
                
                if path.startswith('/api/sources/'):
                    source_id = path.split('/')[-1]
                    self._api_update_source(source_id, data)
                elif path.startswith('/api/targets/'):
                    target_id = path.split('/')[-1]
                    self._api_update_target(target_id, data)
                elif path.startswith('/api/tasks/'):
                    task_id = path.split('/')[-1]
                    self._api_update_task(task_id, data)
                else:
                    self._send_404()
            
            def _handle_api_delete(self, path):
                """处理API DELETE请求"""
                if path.startswith('/api/sources/'):
                    source_id = path.split('/')[-1]
                    self._api_delete_source(source_id)
                elif path.startswith('/api/targets/'):
                    target_id = path.split('/')[-1]
                    self._api_delete_target(target_id)
                elif path.startswith('/api/tasks/'):
                    task_id = path.split('/')[-1]
                    self._api_delete_task(task_id)
                else:
                    self._send_404()
            
            def _get_post_data(self):
                """获取POST数据"""
                try:
                    content_length = int(self.headers.get('Content-Length', 0))
                    if content_length > 0:
                        post_data = self.rfile.read(content_length)
                        return json.loads(post_data.decode('utf-8'))
                    return {}
                except:
                    return {}
            
            def _send_json(self, data):
                """发送JSON响应"""
                json_data = json.dumps(data, ensure_ascii=False).encode('utf-8')
                self.send_response(200)
                self.send_header('Content-Type', 'application/json; charset=utf-8')
                self.send_header('Content-Length', str(len(json_data)))
                self.end_headers()
                self.wfile.write(json_data)
            
            def _send_404(self):
                """发送404响应"""
                self.send_response(404)
                self.send_header('Content-Type', 'text/html; charset=utf-8')
                self.end_headers()
                html = """
                <!DOCTYPE html>
                <html>
                <head>
                    <title>404 - 页面未找到</title>
                    <style>
                        body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
                        h1 { color: #ff6b35; }
                    </style>
                </head>
                <body>
                    <h1>404 - 页面未找到</h1>
                    <p>请求的页面不存在</p>
                    <a href="/">返回首页</a>
                </body>
                </html>
                """.encode('utf-8')
                self.wfile.write(html)
            
            def _send_500(self):
                """发送500响应"""
                self.send_response(500)
                self.send_header('Content-Type', 'text/html; charset=utf-8')
                self.end_headers()
                html = """
                <!DOCTYPE html>
                <html>
                <head>
                    <title>500 - 服务器错误</title>
                    <style>
                        body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
                        h1 { color: #dc3545; }
                    </style>
                </head>
                <body>
                    <h1>500 - 服务器错误</h1>
                    <p>服务器内部错误</p>
                    <a href="/">返回首页</a>
                </body>
                </html>
                """.encode('utf-8')
                self.wfile.write(html)
            
            # API方法实现
            def _api_get_sources(self):
                """获取数据源列表"""
                try:
                    sources = config_manager.get_all_sources()
                    self._send_json({'success': True, 'sources': sources})
                except Exception as e:
                    self._send_json({'success': False, 'message': str(e), 'sources': {}})
            
            def _api_get_targets(self):
                """获取目标存储列表"""
                try:
                    targets = config_manager.get_all_targets()
                    self._send_json({'success': True, 'targets': targets})
                except Exception as e:
                    self._send_json({'success': False, 'message': str(e), 'targets': {}})
            
            def _api_get_tasks(self):
                """获取任务列表"""
                try:
                    tasks = config_manager.get_all_tasks()
                    self._send_json({'success': True, 'tasks': tasks})
                except Exception as e:
                    self._send_json({'success': False, 'message': str(e), 'tasks': {}})
            
            def _api_get_task_status(self):
                """获取任务状态"""
                try:
                    status = task_manager.get_all_task_status() if hasattr(task_manager, 'get_all_task_status') else {}
                    self._send_json({'success': True, 'status': status})
                except Exception as e:
                    self._send_json({'success': False, 'message': str(e), 'status': {}})
            
            def _api_get_task_executions(self):
                """获取任务执行记录"""
                try:
                    executions = db_manager.get_recent_executions(20) if db_manager else []
                    self._send_json({'success': True, 'executions': executions})
                except Exception as e:
                    self._send_json({'success': False, 'message': str(e), 'executions': []})
            
            def _api_get_statistics(self):
                """获取统计信息"""
                try:
                    sources = config_manager.get_all_sources()
                    targets = config_manager.get_all_targets()
                    tasks = config_manager.get_all_tasks()
                    
                    stats = {
                        'sources': len(sources),
                        'targets': len(targets),
                        'tasks': len(tasks),
                        'executions': 0
                    }
                    
                    if db_manager:
                        stats['executions'] = len(db_manager.get_recent_executions(1000))
                    
                    self._send_json({'success': True, 'statistics': stats})
                except Exception as e:
                    self._send_json({'success': False, 'message': str(e), 'statistics': {}})
            
            def _api_get_optimization_config(self):
                """获取优化配置"""
                try:
                    config = config_manager.get_optimization_config()
                    self._send_json({'success': True, 'config': config})
                except Exception as e:
                    self._send_json({'success': False, 'message': str(e), 'config': {}})
            
            def _api_get_task_logs(self, execution_id):
                """获取任务日志"""
                try:
                    logs = db_manager.get_execution_logs(execution_id) if db_manager else []
                    self._send_json({'success': True, 'logs': logs})
                except Exception as e:
                    self._send_json({'success': False, 'message': str(e), 'logs': []})
            
            # 其他API方法的实现
            def _api_add_source(self, data):
                """添加数据源"""
                try:
                    import uuid
                    source_id = str(uuid.uuid4())
                    storage_type = data.get('storage_type', 's3')
                    config_manager.add_source(source_id, storage_type, data)
                    self._send_json({'success': True, 'message': '数据源添加成功', 'source_id': source_id})
                except Exception as e:
                    self._send_json({'success': False, 'message': f'添加数据源失败: {str(e)}'})

            def _api_add_target(self, data):
                """添加目标存储"""
                try:
                    import uuid
                    target_id = str(uuid.uuid4())
                    storage_type = data.get('storage_type', 's3')
                    config_manager.add_target(target_id, storage_type, data)
                    self._send_json({'success': True, 'message': '目标存储添加成功', 'target_id': target_id})
                except Exception as e:
                    self._send_json({'success': False, 'message': f'添加目标存储失败: {str(e)}'})

            def _api_add_task(self, data):
                """添加同步任务"""
                try:
                    import uuid
                    task_id = str(uuid.uuid4())
                    config_manager.add_task(task_id, data)
                    self._send_json({'success': True, 'message': '同步任务添加成功', 'task_id': task_id})
                except Exception as e:
                    self._send_json({'success': False, 'message': f'添加同步任务失败: {str(e)}'})
            
            def _api_test_connection(self, data):
                """测试连接"""
                try:
                    storage_type = data.get('storage_type', 's3')

                    if storage_type == 's3':
                        result = self._test_s3_connection(data)
                    elif storage_type == 'sftp':
                        result = self._test_sftp_connection(data)
                    elif storage_type == 'ftp':
                        result = self._test_ftp_connection(data)
                    elif storage_type == 'smb':
                        result = self._test_smb_connection(data)
                    elif storage_type == 'local':
                        result = self._test_local_connection(data)
                    else:
                        result = {'success': False, 'message': f'不支持的存储类型: {storage_type}'}

                    self._send_json(result)
                except Exception as e:
                    self._send_json({'success': False, 'message': f'连接测试失败: {str(e)}'})

            def _api_list_buckets(self, data):
                """列出S3存储桶"""
                try:
                    from storage_abstraction import S3StorageConfig, StorageType
                    from s3_storage_adapter import S3StorageAdapter

                    # 创建S3配置对象
                    config = S3StorageConfig(
                        storage_type=StorageType.S3,
                        name="临时配置",
                        endpoint=data.get('endpoint', ''),
                        access_key=data.get('access_key', ''),
                        secret_key=data.get('secret_key', ''),
                        bucket="",  # 不指定bucket来列出所有bucket
                        region=data.get('region', '')
                    )

                    adapter = S3StorageAdapter(config)
                    buckets = adapter.list_buckets()
                    self._send_json({'success': True, 'buckets': buckets})
                except Exception as e:
                    self._send_json({'success': False, 'message': f'获取存储桶列表失败: {str(e)}', 'buckets': []})

            def _test_s3_connection(self, data):
                """测试S3连接"""
                try:
                    # 简单的连接测试
                    endpoint = data.get('endpoint', '')
                    access_key = data.get('access_key', '')
                    secret_key = data.get('secret_key', '')

                    if not all([endpoint, access_key, secret_key]):
                        return {'success': False, 'message': 'S3配置信息不完整'}

                    # 这里可以添加实际的S3连接测试逻辑
                    return {'success': True, 'message': 'S3连接测试成功'}
                except Exception as e:
                    return {'success': False, 'message': f'S3连接测试失败: {str(e)}'}

            def _test_sftp_connection(self, data):
                """测试SFTP连接"""
                try:
                    hostname = data.get('hostname', data.get('host', ''))
                    username = data.get('username', '')
                    password = data.get('password', '')

                    if not all([hostname, username]):
                        return {'success': False, 'message': 'SFTP配置信息不完整'}

                    # 这里可以添加实际的SFTP连接测试逻辑
                    return {'success': True, 'message': 'SFTP连接测试成功'}
                except Exception as e:
                    return {'success': False, 'message': f'SFTP连接测试失败: {str(e)}'}

            def _test_ftp_connection(self, data):
                """测试FTP连接"""
                try:
                    hostname = data.get('hostname', data.get('host', ''))
                    username = data.get('username', '')

                    if not all([hostname, username]):
                        return {'success': False, 'message': 'FTP配置信息不完整'}

                    # 这里可以添加实际的FTP连接测试逻辑
                    return {'success': True, 'message': 'FTP连接测试成功'}
                except Exception as e:
                    return {'success': False, 'message': f'FTP连接测试失败: {str(e)}'}

            def _test_smb_connection(self, data):
                """测试SMB连接"""
                try:
                    hostname = data.get('hostname', data.get('host', ''))
                    username = data.get('username', '')

                    if not all([hostname, username]):
                        return {'success': False, 'message': 'SMB配置信息不完整'}

                    # 这里可以添加实际的SMB连接测试逻辑
                    return {'success': True, 'message': 'SMB连接测试成功'}
                except Exception as e:
                    return {'success': False, 'message': f'SMB连接测试失败: {str(e)}'}

            def _test_local_connection(self, data):
                """测试本地存储连接"""
                try:
                    root_path = data.get('root_path', '')

                    if not root_path:
                        return {'success': False, 'message': '本地存储路径不能为空'}

                    import os
                    if not os.path.exists(root_path):
                        return {'success': False, 'message': f'路径不存在: {root_path}'}

                    if not os.access(root_path, os.R_OK | os.W_OK):
                        return {'success': False, 'message': f'路径无读写权限: {root_path}'}

                    return {'success': True, 'message': '本地存储连接测试成功'}
                except Exception as e:
                    return {'success': False, 'message': f'本地存储连接测试失败: {str(e)}'}

            def _api_run_task(self, task_id):
                """运行任务"""
                try:
                    # 检查任务是否存在
                    tasks = config_manager.get_all_tasks()
                    if task_id not in tasks:
                        self._send_json({'success': False, 'message': '任务不存在'})
                        return

                    # 运行任务 - 检查两种可能的方法名
                    if hasattr(task_manager, 'start_task'):
                        print(f"🚀 使用 start_task 方法启动任务: {task_id}")
                        success = task_manager.start_task(task_id)
                        if success:
                            self._send_json({'success': True, 'message': '任务已启动'})
                        else:
                            self._send_json({'success': False, 'message': '任务启动失败'})
                    elif hasattr(task_manager, 'run_task'):
                        print(f"🚀 使用 run_task 方法启动任务: {task_id}")
                        success = task_manager.run_task(task_id)
                        if success:
                            self._send_json({'success': True, 'message': '任务已启动'})
                        else:
                            self._send_json({'success': False, 'message': '任务启动失败'})
                    else:
                        available_methods = [method for method in dir(task_manager) if not method.startswith('_')]
                        error_msg = f'任务管理器不支持运行任务。可用方法: {available_methods}'
                        print(f"❌ {error_msg}")
                        self._send_json({'success': False, 'message': error_msg})
                except Exception as e:
                    print(f"❌ 运行任务异常: {str(e)}")
                    self._send_json({'success': False, 'message': f'运行任务失败: {str(e)}'})

            def _api_stop_task(self, task_id):
                """停止任务"""
                try:
                    # 检查任务是否存在
                    tasks = config_manager.get_all_tasks()
                    if task_id not in tasks:
                        self._send_json({'success': False, 'message': '任务不存在'})
                        return

                    # 停止任务
                    if hasattr(task_manager, 'stop_task'):
                        print(f"🛑 停止任务: {task_id}")
                        success = task_manager.stop_task(task_id)
                        if success:
                            self._send_json({'success': True, 'message': '任务已停止'})
                        else:
                            self._send_json({'success': False, 'message': '任务停止失败'})
                    else:
                        self._send_json({'success': False, 'message': '任务管理器不支持停止任务'})
                except Exception as e:
                    print(f"❌ 停止任务异常: {str(e)}")
                    self._send_json({'success': False, 'message': f'停止任务失败: {str(e)}'})

            def _api_clean_logs(self):
                """清理无效日志"""
                try:
                    if hasattr(db_manager, 'clean_invalid_logs'):
                        cleaned_count = db_manager.clean_invalid_logs()
                        self._send_json({'success': True, 'message': f'已清理 {cleaned_count} 条无效日志'})
                    else:
                        self._send_json({'success': False, 'message': '数据库管理器不支持日志清理功能'})
                except Exception as e:
                    print(f"❌ 清理日志异常: {str(e)}")
                    self._send_json({'success': False, 'message': f'清理日志失败: {str(e)}'})

            def _api_delete_execution(self, execution_id):
                """删除指定的执行记录"""
                try:
                    if hasattr(db_manager, 'delete_execution'):
                        success = db_manager.delete_execution(execution_id)
                        if success:
                            self._send_json({'success': True, 'message': '执行记录已删除'})
                        else:
                            self._send_json({'success': False, 'message': '删除执行记录失败'})
                    else:
                        self._send_json({'success': False, 'message': '数据库管理器不支持删除执行记录功能'})
                except Exception as e:
                    print(f"❌ 删除执行记录异常: {str(e)}")
                    self._send_json({'success': False, 'message': f'删除执行记录失败: {str(e)}'})
            
            def _api_save_optimization_config(self, data):
                """保存优化配置"""
                try:
                    if hasattr(config_manager, 'update_optimization_settings'):
                        success = config_manager.update_optimization_settings(data)
                        if success:
                            self._send_json({'success': True, 'message': '优化配置保存成功'})
                        else:
                            self._send_json({'success': False, 'message': '优化配置验证失败，请检查参数值'})
                    elif hasattr(config_manager, 'save_optimization_config'):
                        config_manager.save_optimization_config(data)
                        self._send_json({'success': True, 'message': '优化配置保存成功'})
                    else:
                        # 如果没有专门的方法，尝试直接保存到配置中
                        config_manager.config['optimization'] = data
                        config_manager.save_config()
                        self._send_json({'success': True, 'message': '优化配置保存成功'})
                except Exception as e:
                    self._send_json({'success': False, 'message': f'保存优化配置失败: {str(e)}'})
            
            def _api_update_source(self, source_id, data):
                """更新数据源"""
                try:
                    # 检查数据源是否存在
                    sources = config_manager.get_all_sources()
                    if source_id not in sources:
                        self._send_json({'success': False, 'message': '数据源不存在'})
                        return

                    # 更新数据源（先删除再添加）
                    config_manager.remove_source(source_id)
                    storage_type = data.get('storage_type', 's3')
                    config_manager.add_source(source_id, storage_type, data)
                    self._send_json({'success': True, 'message': '数据源更新成功'})
                except Exception as e:
                    self._send_json({'success': False, 'message': f'更新数据源失败: {str(e)}'})

            def _api_update_target(self, target_id, data):
                """更新目标存储"""
                try:
                    # 检查目标存储是否存在
                    targets = config_manager.get_all_targets()
                    if target_id not in targets:
                        self._send_json({'success': False, 'message': '目标存储不存在'})
                        return

                    # 更新目标存储（先删除再添加）
                    config_manager.remove_target(target_id)
                    storage_type = data.get('storage_type', 's3')
                    config_manager.add_target(target_id, storage_type, data)
                    self._send_json({'success': True, 'message': '目标存储更新成功'})
                except Exception as e:
                    self._send_json({'success': False, 'message': f'更新目标存储失败: {str(e)}'})

            def _api_update_task(self, task_id, data):
                """更新同步任务"""
                try:
                    # 检查任务是否存在
                    tasks = config_manager.get_all_tasks()
                    if task_id not in tasks:
                        self._send_json({'success': False, 'message': '同步任务不存在'})
                        return

                    # 更新任务
                    if hasattr(config_manager, 'update_task'):
                        config_manager.update_task(task_id, data)
                    else:
                        # 如果没有update_task方法，先删除再添加
                        config_manager.remove_task(task_id)
                        config_manager.add_task(task_id, data)

                    self._send_json({'success': True, 'message': '同步任务更新成功'})
                except Exception as e:
                    self._send_json({'success': False, 'message': f'更新同步任务失败: {str(e)}'})
            
            def _api_delete_source(self, source_id):
                try:
                    config_manager.remove_source(source_id)
                    self._send_json({'success': True, 'message': '数据源删除成功'})
                except Exception as e:
                    self._send_json({'success': False, 'message': f'删除数据源失败: {str(e)}'})
            
            def _api_delete_target(self, target_id):
                try:
                    config_manager.remove_target(target_id)
                    self._send_json({'success': True, 'message': '目标存储删除成功'})
                except Exception as e:
                    self._send_json({'success': False, 'message': f'删除目标存储失败: {str(e)}'})
            
            def _api_delete_task(self, task_id):
                try:
                    config_manager.remove_task(task_id)
                    self._send_json({'success': True, 'message': '同步任务删除成功'})
                except Exception as e:
                    self._send_json({'success': False, 'message': f'删除同步任务失败: {str(e)}'})

            def _api_browse_storage(self, path, data):
                """浏览存储文件"""
                try:
                    # 解析路径: /api/browse/{storage_type}/{storage_id}
                    path_parts = path.strip('/').split('/')
                    if len(path_parts) < 4:
                        return {'success': False, 'message': '路径格式错误'}

                    storage_type = path_parts[2]  # source 或 target
                    storage_id = path_parts[3]
                    browse_path = data.get('path', '') if data else ''

                    from file_browser_manager import get_file_browser_manager
                    browser = get_file_browser_manager(config_manager)

                    result = browser.browse_storage(storage_id, storage_type, browse_path)
                    return result

                except Exception as e:
                    print(f"❌ 浏览存储异常: {str(e)}")
                    return {'success': False, 'message': f'浏览存储失败: {str(e)}'}

            def _api_download_files(self, path, data):
                """下载文件"""
                try:
                    # 解析路径: /api/download/{storage_type}/{storage_id}
                    path_parts = path.strip('/').split('/')
                    if len(path_parts) < 4:
                        return {'success': False, 'message': '路径格式错误'}

                    storage_type = path_parts[2]  # source 或 target
                    storage_id = path_parts[3]

                    if not data or 'file_paths' not in data:
                        return {'success': False, 'message': '缺少文件路径参数'}

                    file_paths = data['file_paths']
                    download_name = data.get('download_name')

                    from file_browser_manager import get_file_browser_manager
                    browser = get_file_browser_manager(config_manager)

                    result = browser.download_files(storage_id, storage_type, file_paths, download_name)

                    if result['success']:
                        # 返回下载链接
                        zip_path = result['zip_path']
                        zip_filename = result['zip_filename']

                        # 将ZIP文件移动到静态文件目录
                        import shutil
                        static_dir = os.path.join(os.path.dirname(__file__), 'downloads')
                        os.makedirs(static_dir, exist_ok=True)

                        final_zip_path = os.path.join(static_dir, zip_filename)
                        shutil.move(zip_path, final_zip_path)

                        # 清理临时目录
                        try:
                            os.rmdir(result['temp_dir'])
                        except:
                            pass

                        download_url = f'/downloads/{zip_filename}'
                        result['download_url'] = download_url
                        result['zip_path'] = final_zip_path

                    return result

                except Exception as e:
                    print(f"❌ 下载文件异常: {str(e)}")
                    return {'success': False, 'message': f'下载文件失败: {str(e)}'}

            def _api_get_file_info(self, path):
                """获取文件信息"""
                try:
                    # 解析路径: /api/file-info/{storage_type}/{storage_id}
                    path_parts = path.strip('/').split('/')
                    if len(path_parts) < 4:
                        return {'success': False, 'message': '路径格式错误'}

                    storage_type = path_parts[2]  # source 或 target
                    storage_id = path_parts[3]

                    # 从查询参数获取文件路径
                    from urllib.parse import parse_qs, urlparse
                    parsed_url = urlparse(path)
                    query_params = parse_qs(parsed_url.query) if '?' in path else {}
                    file_path = query_params.get('file_path', [''])[0] if query_params else ''

                    if not file_path:
                        return {'success': False, 'message': '缺少文件路径参数'}

                    from file_browser_manager import get_file_browser_manager
                    browser = get_file_browser_manager(config_manager)

                    result = browser.get_file_info(storage_id, storage_type, file_path)
                    return result

                except Exception as e:
                    print(f"❌ 获取文件信息异常: {str(e)}")
                    return {'success': False, 'message': f'获取文件信息失败: {str(e)}'}

        return RequestHandler
    
    def start(self):
        """启动Web服务器"""
        try:
            # 检查web目录是否存在
            if not os.path.exists(self.web_root):
                print(f"❌ Web目录不存在: {self.web_root}")
                return False
            
            # 创建请求处理器
            handler_class = self.create_handler_class()
            
            # 创建HTTP服务器 - 绑定到0.0.0.0允许外部访问
            self.server = HTTPServer(('0.0.0.0', self.port), handler_class)

            # 在单独的线程中运行服务器
            self.server_thread = threading.Thread(
                target=self._run_server,
                daemon=True
            )
            self.server_thread.start()

            self.running = True
            print(f"🌐 Web界面已启动: http://0.0.0.0:{self.port}")
            print(f"🌐 本地访问: http://localhost:{self.port}")
            print(f"🌐 外部访问: http://[您的IP地址]:{self.port}")
            return True
            
        except Exception as e:
            print(f"❌ Web服务器启动失败: {e}")
            return False
    
    def stop(self):
        """停止Web服务器"""
        if self.server and self.running:
            try:
                self.server.shutdown()
                self.server.server_close()
                self.running = False
                print("🛑 Web服务器已停止")
            except Exception as e:
                print(f"❌ 停止Web服务器时出错: {e}")
    
    def _run_server(self):
        """运行服务器（在单独线程中）"""
        try:
            self.server.serve_forever()
        except Exception as e:
            if self.running:  # 只有在运行状态下才报告错误
                print(f"❌ Web服务器运行时出错: {e}")
    
    def is_running(self):
        """检查服务器是否正在运行"""
        return self.running and self.server_thread and self.server_thread.is_alive()
    
    def get_url(self):
        """获取Web界面URL"""
        return f"http://localhost:{self.port}"

    def get_external_url(self):
        """获取外部访问URL"""
        import socket
        try:
            # 获取本机IP地址
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            ip = s.getsockname()[0]
            s.close()
            return f"http://{ip}:{self.port}"
        except:
            return f"http://[您的IP地址]:{self.port}"


