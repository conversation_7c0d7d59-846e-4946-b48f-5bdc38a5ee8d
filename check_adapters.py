#!/usr/bin/env python3
"""
检查存储适配器注册情况
"""

import sys
sys.path.insert(0, '.')

def check_adapters():
    """检查存储适配器"""
    print("🔍 检查存储适配器注册情况")
    print("=" * 50)
    
    print("1. 检查paramiko是否可用...")
    try:
        import paramiko
        print(f"✅ paramiko可用，版本: {paramiko.__version__}")
    except ImportError as e:
        print(f"❌ paramiko不可用: {e}")
        return
    
    print("\n2. 导入存储抽象...")
    try:
        from storage_abstraction import StorageFactory, StorageType
        print("✅ 存储抽象导入成功")
        print(f"初始支持的类型: {[t.value for t in StorageFactory.get_supported_types()]}")
    except Exception as e:
        print(f"❌ 存储抽象导入失败: {e}")
        return
    
    print("\n3. 导入SFTP适配器...")
    try:
        import sftp_storage_adapter
        print("✅ SFTP适配器导入成功")
        print(f"导入后支持的类型: {[t.value for t in StorageFactory.get_supported_types()]}")
    except Exception as e:
        print(f"❌ SFTP适配器导入失败: {e}")
    
    print("\n4. 导入其他适配器...")
    adapters = [
        ('s3_storage_adapter', 'S3'),
        ('smb_storage_adapter', 'SMB'),
        ('ftp_storage_adapter', 'FTP'),
        ('local_storage_adapter', 'Local')
    ]
    
    for module_name, adapter_name in adapters:
        try:
            __import__(module_name)
            print(f"✅ {adapter_name}适配器导入成功")
        except Exception as e:
            print(f"❌ {adapter_name}适配器导入失败: {e}")
    
    print(f"\n最终支持的类型: {[t.value for t in StorageFactory.get_supported_types()]}")
    
    print("\n5. 测试SFTP适配器创建...")
    try:
        from storage_abstraction import SFTPStorageConfig
        config = SFTPStorageConfig(
            hostname="test.example.com",
            username="test",
            password="test"
        )
        adapter = StorageFactory.create_adapter(config)
        print(f"✅ SFTP适配器创建成功: {type(adapter).__name__}")
    except Exception as e:
        print(f"❌ SFTP适配器创建失败: {e}")

if __name__ == "__main__":
    check_adapters()
