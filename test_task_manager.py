#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一任务管理器功能测试
"""

import os
import tempfile
import time
from unified_config_manager import UnifiedConfigManager
from unified_task_manager import UnifiedTaskManager

def test_task_manager():
    """测试统一任务管理器功能"""
    print('=== 统一任务管理器测试 ===')

    # 创建临时配置文件
    temp_config = tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False)
    temp_config.close()
    config_file = temp_config.name

    try:
        # 创建配置管理器和任务管理器
        config_manager = UnifiedConfigManager(config_file)
        task_manager = UnifiedTaskManager(config_manager)

        print('\n--- 准备测试数据 ---')
        # 创建测试源和目标
        source_config = {
            'name': '测试本地源',
            'description': '用于测试的本地数据源',
            'root_path': 'C:/temp/test_source'
        }
        config_manager.add_source('test_source', 'local', source_config)

        target_config = {
            'name': '测试本地目标',
            'description': '用于测试的本地目标',
            'root_path': 'C:/temp/test_target'
        }
        config_manager.add_target('test_target', 'local', target_config)

        print('测试源和目标创建完成')

        print('\n--- 测试任务创建 ---')
        # 创建基本任务
        task_id = task_manager.create_task(
            name='基本同步任务',
            description='测试基本同步功能',
            source_id='test_source',
            target_id='test_target',
            sync_mode='incremental',
            max_workers=5,
            verify_integrity=True
        )
        print(f'创建基本任务: {task_id}')

        # 创建高级任务
        advanced_task_id = task_manager.create_task(
            name='高级同步任务',
            description='测试高级同步功能',
            source_id='test_source',
            target_id='test_target',
            sync_mode='full',
            max_workers=10,
            retry_times=3,
            retry_delay=5,
            verify_integrity=True,
            delete_extra=False,
            file_filter='*.txt,*.pdf',
            exclude_filter='temp/*,*.tmp',
            bandwidth_limit=50,  # 50 MB/s
            chunk_threshold=100,  # 100 MB
            chunk_size=20,  # 20 MB
            schedule_type='daily',
            schedule_time='02:00'
        )
        print(f'创建高级任务: {advanced_task_id}')

        print('\n--- 测试任务获取 ---')
        basic_task = task_manager.get_task(task_id)
        if basic_task:
            print(f'基本任务: name={basic_task.name}, sync_mode={basic_task.sync_mode}, workers={basic_task.max_workers}')
        else:
            print('获取基本任务失败')

        advanced_task = task_manager.get_task(advanced_task_id)
        if advanced_task:
            print(f'高级任务: name={advanced_task.name}, bandwidth_limit={advanced_task.bandwidth_limit}MB/s')
            print(f'  文件过滤: include={advanced_task.file_filter}, exclude={advanced_task.exclude_filter}')
            print(f'  调度: type={advanced_task.schedule_type}, time={advanced_task.schedule_time}')
        else:
            print('获取高级任务失败')

        print('\n--- 测试获取所有任务 ---')
        all_tasks = task_manager.get_all_tasks()
        print(f'总任务数量: {len(all_tasks)}')
        for tid, task in all_tasks.items():
            print(f'  - {tid}: {task.name} ({task.sync_mode})')

        print('\n--- 测试任务更新 ---')
        update_success = task_manager.update_task(task_id, 
            description='更新后的任务描述',
            max_workers=15,
            sync_mode='mirror'
        )
        print(f'更新任务: {update_success}')

        updated_task = task_manager.get_task(task_id)
        if updated_task:
            print(f'更新后任务: description={updated_task.description}, workers={updated_task.max_workers}, mode={updated_task.sync_mode}')

        print('\n--- 测试任务状态管理 ---')
        # 获取任务状态
        status = task_manager.get_task_status(task_id)
        print(f'任务状态: {status}')

        # 获取所有任务状态
        all_status = task_manager.get_all_task_status()
        print(f'所有任务状态数量: {len(all_status)}')

        print('\n--- 测试任务启动（模拟） ---')
        # 注意：实际启动可能会失败，因为测试路径可能不存在
        # 这里主要测试启动逻辑
        try:
            start_success = task_manager.start_task(task_id)
            print(f'启动任务: {start_success}')
            
            # 等待一小段时间查看状态变化
            time.sleep(2)
            status_after_start = task_manager.get_task_status(task_id)
            print(f'启动后状态: {status_after_start}')
            
            # 停止任务
            stop_success = task_manager.stop_task(task_id)
            print(f'停止任务: {stop_success}')
            
            time.sleep(1)
            status_after_stop = task_manager.get_task_status(task_id)
            print(f'停止后状态: {status_after_stop}')
            
        except Exception as e:
            print(f'任务启动测试异常（预期）: {e}')

        print('\n--- 测试任务验证 ---')
        # 测试无效源ID的任务
        try:
            invalid_task_id = task_manager.create_task(
                name='无效任务',
                description='使用无效源ID的任务',
                source_id='invalid_source',
                target_id='test_target'
            )
            print(f'创建无效任务: {invalid_task_id}')
            
            # 尝试启动无效任务
            start_invalid = task_manager.start_task(invalid_task_id)
            print(f'启动无效任务: {start_invalid}')
            
        except Exception as e:
            print(f'无效任务异常（预期）: {e}')

        print('\n--- 测试任务删除 ---')
        # 删除任务
        delete_success = task_manager.delete_task(advanced_task_id)
        print(f'删除高级任务: {delete_success}')

        # 验证删除
        deleted_task = task_manager.get_task(advanced_task_id)
        print(f'删除后获取任务: {deleted_task is None}')

        remaining_tasks = task_manager.get_all_tasks()
        print(f'剩余任务数量: {len(remaining_tasks)}')

        print('\n--- 测试任务配置验证 ---')
        # 测试各种配置参数
        config_test_task_id = task_manager.create_task(
            name='配置测试任务',
            description='测试各种配置参数',
            source_id='test_source',
            target_id='test_target',
            prefix='data/',
            max_workers=25,
            retry_times=10,
            retry_delay=2,
            verify_integrity=False,
            incremental_sync=False,
            sync_mode='mirror',
            delete_extra=True,
            bandwidth_limit=100,
            chunk_threshold=50,
            chunk_size=5,
            schedule_type='weekly',
            schedule_interval=2,
            enabled=False
        )
        
        config_test_task = task_manager.get_task(config_test_task_id)
        if config_test_task:
            print(f'配置测试任务创建成功:')
            print(f'  prefix: {config_test_task.prefix}')
            print(f'  max_workers: {config_test_task.max_workers}')
            print(f'  retry_times: {config_test_task.retry_times}')
            print(f'  verify_integrity: {config_test_task.verify_integrity}')
            print(f'  delete_extra: {config_test_task.delete_extra}')
            print(f'  enabled: {config_test_task.enabled}')

        print('\n--- 测试并发任务管理 ---')
        # 创建多个任务测试并发
        concurrent_task_ids = []
        for i in range(3):
            tid = task_manager.create_task(
                name=f'并发任务{i+1}',
                description=f'测试并发执行的任务{i+1}',
                source_id='test_source',
                target_id='test_target',
                max_workers=5
            )
            concurrent_task_ids.append(tid)
        
        print(f'创建了{len(concurrent_task_ids)}个并发测试任务')
        
        # 清理并发测试任务
        for tid in concurrent_task_ids:
            task_manager.delete_task(tid)
        
        print('清理并发测试任务完成')

    finally:
        # 清理临时文件
        if os.path.exists(config_file):
            os.unlink(config_file)
            print(f'\n清理临时配置文件: {config_file}')

    print('统一任务管理器测试完成')

if __name__ == '__main__':
    test_task_manager()
