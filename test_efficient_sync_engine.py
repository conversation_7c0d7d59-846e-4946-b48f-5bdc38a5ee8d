#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试EfficientSyncEngine是否包含我们的修改
"""

import inspect

def test_efficient_sync_engine():
    """测试EfficientSyncEngine是否包含我们的修改"""
    print("🧪 测试EfficientSyncEngine修改")
    
    try:
        from efficient_sync_engine import EfficientSyncEngine
        
        # 检查方法是否存在
        if hasattr(EfficientSyncEngine, '_download_smb_file_with_subprocess'):
            print('✅ _download_smb_file_with_subprocess 方法存在')
        else:
            print('❌ _download_smb_file_with_subprocess 方法不存在')
            return False
        
        # 检查源代码
        try:
            source = inspect.getsource(EfficientSyncEngine.sync_files)
            if '检查存储类型' in source:
                print('✅ 调试日志代码存在')
            else:
                print('❌ 调试日志代码不存在')
                return False
                
            if '_download_smb_file_with_subprocess' in source:
                print('✅ 子进程下载调用存在')
            else:
                print('❌ 子进程下载调用不存在')
                return False
                
            print("✅ EfficientSyncEngine修改验证成功")
            return True
            
        except Exception as e:
            print(f'❌ 检查源代码失败: {e}')
            return False
            
    except Exception as e:
        print(f'❌ 导入失败: {e}')
        return False

if __name__ == "__main__":
    if test_efficient_sync_engine():
        print("🎉 EfficientSyncEngine修改验证成功！")
    else:
        print("❌ EfficientSyncEngine修改验证失败！")
