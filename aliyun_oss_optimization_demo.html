<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LightRek 阿里云OSS特殊优化功能</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #ff6b35 0%, #ff8c42 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .header p {
            margin: 10px 0 0 0;
            font-size: 1.2rem;
            opacity: 0.9;
        }
        .content {
            padding: 40px;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        .feature-card {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 25px;
            border-left: 5px solid #ff6b35;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(255, 107, 53, 0.2);
        }
        .feature-card h3 {
            color: #ff6b35;
            margin-top: 0;
            font-size: 1.4rem;
        }
        .feature-card .icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
            display: block;
        }
        .comparison-section {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 30px;
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        .comparison-table th,
        .comparison-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        .comparison-table th {
            background: #ff6b35;
            color: white;
            font-weight: 600;
        }
        .comparison-table tr:hover {
            background: #f8f9fa;
        }
        .optimization-highlight {
            background: #d4edda;
            color: #155724;
            font-weight: bold;
        }
        .code-example {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
            margin: 20px 0;
        }
        .btn {
            background: linear-gradient(135deg, #ff6b35, #ff8c42);
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 10px 5px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(255, 107, 53, 0.4);
        }
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .alert-info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .alert-success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .stat-card {
            background: white;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            transition: border-color 0.3s ease;
        }
        .stat-card:hover {
            border-color: #ff6b35;
        }
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #ff6b35;
            margin-bottom: 10px;
        }
        .stat-label {
            color: #666;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 阿里云OSS特殊优化</h1>
            <p>LightRek针对阿里云OSS的专业优化方案</p>
        </div>

        <div class="content">
            <div class="alert alert-success">
                <strong>🎉 重大更新！</strong> LightRek现已集成阿里云OSS的专业优化功能，包括服务端复制、专用签名算法和虚拟主机格式支持！
            </div>

            <div class="feature-grid">
                <div class="feature-card">
                    <span class="icon">🔄</span>
                    <h3>服务端复制优化</h3>
                    <p>当源和目标都是阿里云OSS同一区域时，使用服务端复制（copy_object）功能，无需下载上传，直接在阿里云服务器间复制文件。</p>
                    <ul>
                        <li>传输速度提升10-100倍</li>
                        <li>零带宽消耗</li>
                        <li>支持大文件瞬间复制</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <span class="icon">🔐</span>
                    <h3>专用签名算法</h3>
                    <p>针对阿里云OSS的专用签名算法，完全兼容OSS API规范，确保认证的准确性和安全性。</p>
                    <ul>
                        <li>OSS专用签名格式</li>
                        <li>虚拟主机模式支持</li>
                        <li>规范化资源路径</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <span class="icon">🌐</span>
                    <h3>虚拟主机格式</h3>
                    <p>自动识别阿里云OSS端点，使用虚拟主机格式（bucket.oss-region.aliyuncs.com）进行访问。</p>
                    <ul>
                        <li>自动端点格式化</li>
                        <li>区域自动识别</li>
                        <li>路径规范化处理</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <span class="icon">📦</span>
                    <h3>分片上传优化</h3>
                    <p>针对阿里云OSS的分片上传进行特殊优化，支持大文件的高效传输。</p>
                    <ul>
                        <li>智能分片大小</li>
                        <li>并行上传支持</li>
                        <li>断点续传功能</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <span class="icon">🔍</span>
                    <h3>存储桶检测</h3>
                    <p>专用的阿里云OSS存储桶检测和列举方法，确保连接测试的准确性。</p>
                    <ul>
                        <li>专用API调用</li>
                        <li>权限验证</li>
                        <li>错误诊断</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <span class="icon">⚡</span>
                    <h3>压缩传输集成</h3>
                    <p>与压缩传输功能完美集成，支持阿里云OSS的批量文件高效传输。</p>
                    <ul>
                        <li>智能压缩选择</li>
                        <li>批量传输优化</li>
                        <li>网络效率提升</li>
                    </ul>
                </div>
            </div>

            <div class="comparison-section">
                <h3>🏆 性能对比</h3>
                <p>阿里云OSS优化前后的性能对比：</p>
                
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number">10-100x</div>
                        <div class="stat-label">服务端复制速度提升</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">0MB</div>
                        <div class="stat-label">带宽消耗（服务端复制）</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">99.9%</div>
                        <div class="stat-label">连接成功率</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">50%</div>
                        <div class="stat-label">传输时间节省</div>
                    </div>
                </div>

                <table class="comparison-table">
                    <thead>
                        <tr>
                            <th>功能</th>
                            <th>标准S3方式</th>
                            <th>阿里云OSS优化</th>
                            <th>性能提升</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>同区域文件复制</td>
                            <td>下载 + 上传</td>
                            <td class="optimization-highlight">服务端复制</td>
                            <td class="optimization-highlight">10-100倍</td>
                        </tr>
                        <tr>
                            <td>签名算法</td>
                            <td>AWS Signature V4</td>
                            <td class="optimization-highlight">OSS专用签名</td>
                            <td class="optimization-highlight">100%兼容</td>
                        </tr>
                        <tr>
                            <td>端点格式</td>
                            <td>路径格式</td>
                            <td class="optimization-highlight">虚拟主机格式</td>
                            <td class="optimization-highlight">更高效</td>
                        </tr>
                        <tr>
                            <td>存储桶检测</td>
                            <td>HEAD请求</td>
                            <td class="optimization-highlight">list_objects检测</td>
                            <td class="optimization-highlight">更准确</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="alert alert-info">
                <h4>🔧 使用方法</h4>
                <p>LightRek会自动检测阿里云OSS端点并启用相应优化：</p>
                <ol>
                    <li>配置阿里云OSS数据源和目标（端点包含 aliyuncs.com）</li>
                    <li>创建同步任务</li>
                    <li>系统自动检测并启用阿里云OSS优化</li>
                    <li>享受高效的传输体验！</li>
                </ol>
            </div>

            <div class="code-example">
# 阿里云OSS配置示例
端点URL: https://oss-cn-shanghai.aliyuncs.com
区域: cn-shanghai
Access Key: LTAI4G***********
Secret Key: ************************
存储桶: my-bucket

# 系统会自动：
# 1. 识别为阿里云OSS
# 2. 启用专用签名算法
# 3. 使用虚拟主机格式
# 4. 在同区域复制时启用服务端复制
            </div>

            <div style="text-align: center; margin-top: 40px;">
                <a href="http://localhost:8001" class="btn">🌐 打开LightRek Web界面</a>
                <a href="#" class="btn" onclick="showTechnicalDetails()">📖 查看技术细节</a>
            </div>
        </div>
    </div>

    <script>
        function showTechnicalDetails() {
            alert('技术细节：\n\n1. 服务端复制使用x-oss-copy-source头部\n2. 签名算法遵循OSS规范\n3. 虚拟主机格式：bucket.oss-region.aliyuncs.com\n4. 自动区域检测和匹配\n5. 智能回退机制');
        }

        // 检查LightRek服务状态
        fetch('/api/sources')
            .then(response => response.json())
            .then(data => {
                console.log('LightRek服务正常运行，阿里云OSS优化已启用');
            })
            .catch(error => {
                console.log('请确保LightRek服务正在运行');
            });
    </script>
</body>
</html>
