"""
统一配置管理器 - 支持多种存储类型的配置管理
"""

import json
import os
from typing import Dict, List, Any, Optional, Union, Tuple
from datetime import datetime
from storage_abstraction import (
    StorageType, StorageConfig, S3StorageConfig, SFTPStorageConfig,
    SMBStorageConfig, FTPStorageConfig, LocalStorageConfig, StorageFactory,
    HighPerformanceSFTPStorageConfig
)


class UnifiedConfigManager:
    """统一配置管理器"""
    
    def __init__(self, config_file: str = "lightrek_unified_config.json"):
        self.config_file = config_file
        self.config_data = self._load_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                print(f"加载配置文件失败: {e}")
                return self._create_default_config()
        else:
            return self._create_default_config()
    
    def _create_default_config(self) -> Dict[str, Any]:
        """创建默认配置"""
        return {
            "version": "2.0",
            "sources": {},
            "targets": {},
            "tasks": {},
            "global_settings": {
                "default_max_workers": 20,
                "default_retry_times": 5,
                "default_retry_delay": 3,
                "default_chunk_size_mb": 10,
                "default_bandwidth_limit": 0
            }
        }
    
    def _save_config(self):
        """保存配置文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config_data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"保存配置文件失败: {e}")
    
    def _create_storage_config(self, storage_type: str, config_data: Dict[str, Any]) -> StorageConfig:
        """根据存储类型创建配置对象"""
        storage_type_enum = StorageType(storage_type)
        
        if storage_type_enum == StorageType.S3:
            return S3StorageConfig(
                access_key=config_data.get('access_key', ''),
                secret_key=config_data.get('secret_key', ''),
                endpoint=config_data.get('endpoint', ''),
                region=config_data.get('region', ''),
                bucket=config_data.get('bucket', ''),
                name=config_data.get('name', ''),
                description=config_data.get('description', '')
            )
        elif storage_type_enum == StorageType.SFTP:
            return SFTPStorageConfig(
                hostname=config_data.get('hostname', ''),
                port=config_data.get('port', 22),
                username=config_data.get('username', ''),
                password=config_data.get('password'),
                private_key_path=config_data.get('private_key_path'),
                private_key_passphrase=config_data.get('private_key_passphrase'),
                root_path=config_data.get('root_path', '/'),
                name=config_data.get('name', ''),
                description=config_data.get('description', '')
            )
        elif storage_type_enum == StorageType.SMB:
            return SMBStorageConfig(
                hostname=config_data.get('hostname', ''),
                port=config_data.get('port', 445),
                username=config_data.get('username', ''),
                password=config_data.get('password', ''),
                domain=config_data.get('domain', ''),
                share_name=config_data.get('share_name', ''),
                root_path=config_data.get('root_path', '/'),
                name=config_data.get('name', ''),
                description=config_data.get('description', '')
            )
        elif storage_type_enum == StorageType.FTP:
            return FTPStorageConfig(
                hostname=config_data.get('hostname', ''),
                port=config_data.get('port', 21),
                username=config_data.get('username', ''),
                password=config_data.get('password', ''),
                use_tls=config_data.get('use_tls', False),
                passive_mode=config_data.get('passive_mode', True),
                root_path=config_data.get('root_path', '/'),
                name=config_data.get('name', ''),
                description=config_data.get('description', '')
            )
        elif storage_type_enum == StorageType.LOCAL:
            return LocalStorageConfig(
                root_path=config_data.get('root_path', ''),
                name=config_data.get('name', ''),
                description=config_data.get('description', '')
            )
        elif storage_type_enum == StorageType.SFTP_HIGH_PERFORMANCE:
            return HighPerformanceSFTPStorageConfig(
                hostname=config_data.get('hostname', ''),
                port=config_data.get('port', 22),
                username=config_data.get('username', ''),
                password=config_data.get('password', ''),
                private_key_path=config_data.get('private_key_path', ''),
                private_key_passphrase=config_data.get('private_key_passphrase', ''),
                root_path=config_data.get('root_path', '/'),
                max_connections=config_data.get('max_connections', 8),
                max_concurrent_transfers=config_data.get('max_concurrent_transfers', 16),
                chunk_size=config_data.get('chunk_size', 64 * 1024),
                prefetch_size=config_data.get('prefetch_size', 1024 * 1024),
                keepalive_interval=config_data.get('keepalive_interval', 30),
                connect_timeout=config_data.get('connect_timeout', 30),
                max_retries=config_data.get('max_retries', 3),
                retry_delay=config_data.get('retry_delay', 1.0),
                compression=config_data.get('compression', True),
                compression_level=config_data.get('compression_level', 6),
                enable_stat_cache=config_data.get('enable_stat_cache', True),
                stat_cache_ttl=config_data.get('stat_cache_ttl', 300),
                performance_profile=config_data.get('performance_profile', 'balanced'),
                name=config_data.get('name', ''),
                description=config_data.get('description', '')
            )
        else:
            raise ValueError(f"不支持的存储类型: {storage_type}")
    
    def add_source(self, source_id: str, storage_type: str, config_data: Dict[str, Any]) -> bool:
        """添加数据源"""
        try:
            # 验证配置
            storage_config = self._create_storage_config(storage_type, config_data)
            
            # 保存到配置
            config_dict = config_data.copy()
            config_dict['storage_type'] = storage_type
            config_dict['created_at'] = datetime.now().isoformat()
            
            self.config_data['sources'][source_id] = config_dict
            self._save_config()
            return True
        except Exception as e:
            print(f"添加数据源失败: {e}")
            return False
    
    def add_target(self, target_id: str, storage_type: str, config_data: Dict[str, Any]) -> bool:
        """添加目标存储"""
        try:
            # 验证配置
            storage_config = self._create_storage_config(storage_type, config_data)
            
            # 保存到配置
            config_dict = config_data.copy()
            config_dict['storage_type'] = storage_type
            config_dict['created_at'] = datetime.now().isoformat()
            
            self.config_data['targets'][target_id] = config_dict
            self._save_config()
            return True
        except Exception as e:
            print(f"添加目标存储失败: {e}")
            return False
    
    def get_source(self, source_id: str) -> Optional[StorageConfig]:
        """获取数据源配置"""
        try:
            if source_id not in self.config_data['sources']:
                return None
            
            config_data = self.config_data['sources'][source_id]
            storage_type = config_data.get('storage_type')
            
            if not storage_type:
                return None
            
            return self._create_storage_config(storage_type, config_data)
        except Exception as e:
            print(f"获取数据源配置失败: {e}")
            return None
    
    def get_target(self, target_id: str) -> Optional[StorageConfig]:
        """获取目标存储配置"""
        try:
            if target_id not in self.config_data['targets']:
                return None
            
            config_data = self.config_data['targets'][target_id]
            storage_type = config_data.get('storage_type')
            
            if not storage_type:
                return None
            
            return self._create_storage_config(storage_type, config_data)
        except Exception as e:
            print(f"获取目标存储配置失败: {e}")
            return None
    
    def get_all_sources(self) -> Dict[str, Dict[str, Any]]:
        """获取所有数据源"""
        return self.config_data.get('sources', {})
    
    def get_all_targets(self) -> Dict[str, Dict[str, Any]]:
        """获取所有目标存储"""
        return self.config_data.get('targets', {})
    
    def remove_source(self, source_id: str) -> bool:
        """删除数据源"""
        try:
            if source_id in self.config_data['sources']:
                del self.config_data['sources'][source_id]
                self._save_config()
                return True
            return False
        except Exception as e:
            print(f"删除数据源失败: {e}")
            return False
    
    def remove_target(self, target_id: str) -> bool:
        """删除目标存储"""
        try:
            if target_id in self.config_data['targets']:
                del self.config_data['targets'][target_id]
                self._save_config()
                return True
            return False
        except Exception as e:
            print(f"删除目标存储失败: {e}")
            return False
    
    def test_storage_connection(self, storage_id: str, is_source: bool = True) -> Tuple[bool, str]:
        """测试存储连接"""
        try:
            if is_source:
                config = self.get_source(storage_id)
            else:
                config = self.get_target(storage_id)
            
            if not config:
                return False, "配置不存在"
            
            # 创建存储适配器并测试连接
            adapter = StorageFactory.create_adapter(config)
            return adapter.test_connection()
        except Exception as e:
            return False, f"测试连接失败: {str(e)}"
    
    def get_supported_storage_types(self) -> List[Dict[str, Any]]:
        """获取支持的存储类型"""
        return [
            {
                'type': 's3',
                'name': 'S3对象存储',
                'description': '支持AWS S3、阿里云OSS、腾讯云COS等S3兼容存储',
                'fields': [
                    {'name': 'access_key', 'label': '访问密钥', 'type': 'text', 'required': True},
                    {'name': 'secret_key', 'label': '密钥', 'type': 'password', 'required': True},
                    {'name': 'endpoint', 'label': '端点URL', 'type': 'text', 'required': True},
                    {'name': 'region', 'label': '区域', 'type': 'text', 'required': True},
                    {'name': 'bucket', 'label': '存储桶', 'type': 'text', 'required': True}
                ]
            },
            {
                'type': 'sftp',
                'name': 'SFTP',
                'description': 'SSH文件传输协议',
                'fields': [
                    {'name': 'hostname', 'label': '主机名', 'type': 'text', 'required': True},
                    {'name': 'port', 'label': '端口', 'type': 'number', 'default': 22},
                    {'name': 'username', 'label': '用户名', 'type': 'text', 'required': True},
                    {'name': 'password', 'label': '密码', 'type': 'password'},
                    {'name': 'private_key_path', 'label': '私钥文件路径', 'type': 'text'},
                    {'name': 'private_key_passphrase', 'label': '私钥密码', 'type': 'password'},
                    {'name': 'root_path', 'label': '根路径', 'type': 'text', 'default': '/'}
                ]
            },
            {
                'type': 'sftp_hp',
                'name': '高性能SFTP',
                'description': '基于AsyncSSH的高性能SFTP，支持连接池和并发传输',
                'fields': [
                    {'name': 'hostname', 'label': '主机名', 'type': 'text', 'required': True},
                    {'name': 'port', 'label': '端口', 'type': 'number', 'default': 22},
                    {'name': 'username', 'label': '用户名', 'type': 'text', 'required': True},
                    {'name': 'password', 'label': '密码', 'type': 'password'},
                    {'name': 'private_key_path', 'label': '私钥文件路径', 'type': 'text'},
                    {'name': 'private_key_passphrase', 'label': '私钥密码', 'type': 'password'},
                    {'name': 'root_path', 'label': '根路径', 'type': 'text', 'default': '/'},
                    {'name': 'max_connections', 'label': '最大连接数', 'type': 'number', 'default': 8},
                    {'name': 'max_concurrent_transfers', 'label': '最大并发传输', 'type': 'number', 'default': 16},
                    {'name': 'compression', 'label': '启用压缩', 'type': 'checkbox', 'default': True},
                    {'name': 'enable_stat_cache', 'label': '启用缓存', 'type': 'checkbox', 'default': True},
                    {'name': 'performance_profile', 'label': '性能配置', 'type': 'select', 'default': 'balanced',
                     'options': [
                         {'value': 'high_performance', 'label': '高性能'},
                         {'value': 'balanced', 'label': '平衡'},
                         {'value': 'conservative', 'label': '保守'}
                     ]}
                ]
            },
            {
                'type': 'smb',
                'name': 'SMB/CIFS',
                'description': 'Windows网络共享',
                'fields': [
                    {'name': 'hostname', 'label': '主机名', 'type': 'text', 'required': True},
                    {'name': 'port', 'label': '端口', 'type': 'number', 'default': 445},
                    {'name': 'username', 'label': '用户名', 'type': 'text', 'required': True},
                    {'name': 'password', 'label': '密码', 'type': 'password', 'required': True},
                    {'name': 'domain', 'label': '域', 'type': 'text'},
                    {'name': 'share_name', 'label': '共享名', 'type': 'text', 'required': True},
                    {'name': 'root_path', 'label': '根路径', 'type': 'text', 'default': '/'}
                ]
            },
            {
                'type': 'ftp',
                'name': 'FTP/FTPS',
                'description': '文件传输协议',
                'fields': [
                    {'name': 'hostname', 'label': '主机名', 'type': 'text', 'required': True},
                    {'name': 'port', 'label': '端口', 'type': 'number', 'default': 21},
                    {'name': 'username', 'label': '用户名', 'type': 'text', 'required': True},
                    {'name': 'password', 'label': '密码', 'type': 'password', 'required': True},
                    {'name': 'use_tls', 'label': '使用TLS加密', 'type': 'checkbox', 'default': False},
                    {'name': 'passive_mode', 'label': '被动模式', 'type': 'checkbox', 'default': True},
                    {'name': 'root_path', 'label': '根路径', 'type': 'text', 'default': '/'}
                ]
            },
            {
                'type': 'local',
                'name': '本地文件系统',
                'description': '本地磁盘或挂载的NAS设备',
                'fields': [
                    {'name': 'root_path', 'label': '根路径', 'type': 'text', 'required': True}
                ]
            }
        ]
    
    def add_task(self, task_id: str, task_config: Dict[str, Any]) -> bool:
        """添加同步任务"""
        try:
            task_config['created_at'] = datetime.now().isoformat()
            self.config_data['tasks'][task_id] = task_config
            self._save_config()
            return True
        except Exception as e:
            print(f"添加任务失败: {e}")
            return False
    
    def get_task(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取同步任务"""
        return self.config_data.get('tasks', {}).get(task_id)
    
    def get_all_tasks(self) -> Dict[str, Dict[str, Any]]:
        """获取所有同步任务"""
        return self.config_data.get('tasks', {})
    
    def remove_task(self, task_id: str) -> bool:
        """删除同步任务"""
        try:
            if task_id in self.config_data.get('tasks', {}):
                del self.config_data['tasks'][task_id]
                self._save_config()
                return True
            return False
        except Exception as e:
            print(f"删除任务失败: {e}")
            return False
    
    def update_task(self, task_id: str, task_config: Dict[str, Any]) -> bool:
        """更新同步任务"""
        try:
            if task_id in self.config_data.get('tasks', {}):
                task_config['updated_at'] = datetime.now().isoformat()
                self.config_data['tasks'][task_id].update(task_config)
                self._save_config()
                return True
            return False
        except Exception as e:
            print(f"更新任务失败: {e}")
            return False

    def get_optimization_config(self) -> Dict[str, Any]:
        """获取性能优化配置"""
        return self.config_data.get('optimization', {
            'max_workers': 20,
            'chunk_size_mb': 10,
            'retry_times': 3,
            'enable_parallel_scan': True,
            'enable_cache': True,
            'cache_ttl_hours': 24
        })

    def _validate_optimization_settings(self, settings: Dict[str, Any]) -> tuple[bool, str]:
        """验证优化设置"""
        # 验证max_workers
        if 'max_workers' in settings:
            max_workers = settings['max_workers']
            if not isinstance(max_workers, int) or max_workers <= 0 or max_workers > 1000:
                return False, "max_workers必须是1-1000之间的整数"

        # 验证chunk_size_mb
        if 'chunk_size_mb' in settings:
            chunk_size = settings['chunk_size_mb']
            if not isinstance(chunk_size, (int, float)) or chunk_size <= 0 or chunk_size > 1000:
                return False, "chunk_size_mb必须是1-1000之间的数值"

        # 验证retry_times
        if 'retry_times' in settings:
            retry_times = settings['retry_times']
            if not isinstance(retry_times, int) or retry_times < 0 or retry_times > 10:
                return False, "retry_times必须是0-10之间的整数"

        # 验证retry_delay
        if 'retry_delay' in settings:
            retry_delay = settings['retry_delay']
            if not isinstance(retry_delay, (int, float)) or retry_delay < 0 or retry_delay > 60:
                return False, "retry_delay必须是0-60之间的数值"

        # 验证cache_ttl_hours
        if 'cache_ttl_hours' in settings:
            cache_ttl = settings['cache_ttl_hours']
            if not isinstance(cache_ttl, (int, float)) or cache_ttl < 1 or cache_ttl > 168:
                return False, "cache_ttl_hours必须是1-168之间的数值"

        return True, ""

    def update_optimization_settings(self, settings: Dict[str, Any]) -> bool:
        """更新性能优化设置"""
        try:
            # 验证设置
            is_valid, error_msg = self._validate_optimization_settings(settings)
            if not is_valid:
                print(f"优化设置验证失败: {error_msg}")
                return False

            if 'optimization' not in self.config_data:
                self.config_data['optimization'] = {}

            # 过滤掉不需要的字段
            filtered_settings = {}
            valid_keys = {
                'max_workers', 'chunk_size_mb', 'retry_times', 'retry_delay',
                'enable_parallel_scan', 'enable_cache', 'cache_ttl_hours',
                'verify_integrity'
            }

            for key, value in settings.items():
                if key in valid_keys:
                    filtered_settings[key] = value

            self.config_data['optimization'].update(filtered_settings)
            self._save_config()
            return True
        except Exception as e:
            print(f"更新优化设置失败: {e}")
            return False
