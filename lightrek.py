#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LightRek 统一存储同步工具 - 简化启动脚本
"""

import sys
import os
import argparse
import signal
import time
import logging


def signal_handler(signum, frame):
    """信号处理器"""
    print(f"\n🛑 收到信号 {signum}，正在关闭程序...")
    sys.exit(0)


def check_environment():
    """检查运行环境"""
    print("🔍 检查运行环境...")
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print(f"❌ 错误: 需要Python 3.8或更高版本，当前版本: {sys.version}")
        return False
    
    # 检查必要的模块
    required_modules = [
        'unified_config_manager',
        'unified_task_manager', 
        'database_manager',
        'complete_web_interface'
    ]
    
    missing_modules = []
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        print("❌ 缺少必要的模块:")
        for module in missing_modules:
            print(f"   • {module}")
        return False
    
    print("✅ 环境检查通过")
    return True


def create_default_config():
    """创建默认配置文件"""
    import json
    
    config_file = 'lightrek_unified_config.json'
    if os.path.exists(config_file):
        return config_file
    
    print(f"⚙️ 创建默认配置文件: {config_file}")
    
    default_config = {
        "sources": {},
        "targets": {},
        "tasks": {},
        "optimization": {
            "max_workers": 20,
            "chunk_size_mb": 10,
            "retry_times": 3,
            "enable_compression": True,
            "enable_parallel_scan": True,
            "enable_cache": True,
            "cache_ttl_hours": 24
        }
    }
    
    try:
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(default_config, f, indent=2, ensure_ascii=False)
        print(f"✅ 配置文件创建成功")
        return config_file
    except Exception as e:
        print(f"❌ 创建配置文件失败: {e}")
        return None


def main():
    """主函数"""
    # 设置信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='LightRek 统一存储同步工具')
    parser.add_argument('--port', type=int, default=8001, help='Web界面端口 (默认: 8001)')
    parser.add_argument('--config', type=str, default='lightrek_unified_config.json', help='配置文件路径')
    parser.add_argument('--no-web', action='store_true', help='不启动Web界面')
    parser.add_argument('--version', action='version', version='LightRek v2.0.0')
    
    args = parser.parse_args()
    
    print("🚀 LightRek 统一存储同步工具 v2.0.0")
    print("=" * 50)

    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )
    print("📝 日志配置完成 (级别: INFO)")

    # 检查环境
    if not check_environment():
        return 1
    
    # 创建配置文件
    config_file = create_default_config()
    if not config_file:
        return 1
    
    try:
        # 导入必要的模块
        from unified_config_manager import UnifiedConfigManager
        from unified_task_manager import UnifiedTaskManager
        from database_manager import DatabaseManager
        from static_web_server import StaticWebServer

        # 导入存储适配器以确保它们被注册
        try:
            import smb_storage_adapter
            print("✅ SMB存储适配器已加载")
        except ImportError as e:
            print(f"⚠️ SMB存储适配器加载失败: {e}")

        try:
            import ftp_storage_adapter
            print("✅ FTP存储适配器已加载")
        except ImportError as e:
            print(f"⚠️ FTP存储适配器加载失败: {e}")

        try:
            import local_storage_adapter
            print("✅ 本地存储适配器已加载")
        except ImportError as e:
            print(f"⚠️ 本地存储适配器加载失败: {e}")
        
        # 初始化管理器
        print("⚙️ 初始化配置管理器...")
        config_manager = UnifiedConfigManager(config_file=config_file)
        
        print("📋 初始化任务管理器...")
        task_manager = UnifiedTaskManager(config_manager)
        
        print("💾 初始化数据库管理器...")
        db_manager = DatabaseManager()
        
        # 显示系统状态
        print("\n📊 系统状态:")
        sources = config_manager.get_all_sources()
        targets = config_manager.get_all_targets()
        tasks = config_manager.get_all_tasks()
        
        print(f"   📁 数据源: {len(sources)} 个")
        print(f"   🎯 目标存储: {len(targets)} 个")
        print(f"   📋 同步任务: {len(tasks)} 个")
        
        if tasks:
            print("\n📋 已配置的任务:")
            for task_id, task in tasks.items():
                print(f"   • {task.get('name', task_id)}")
        
        # 启动Web界面（如果需要）
        web_server = None
        if not args.no_web:
            print(f"\n🌐 启动Web界面 (端口: {args.port})...")
            try:
                web_server = StaticWebServer(config_manager, task_manager, db_manager, args.port)
                if web_server.start():
                    print(f"✅ Web界面启动成功!")
                    print(f"🏠 本地访问: {web_server.get_url()}")
                    print(f"🌍 外部访问: {web_server.get_external_url()}")
                    print(f"📖 用户手册: {web_server.get_url()}/manual.html")
                else:
                    print("❌ Web界面启动失败")
                    return 1
            except Exception as e:
                print(f"❌ Web界面启动失败: {e}")
                return 1
        
        print("\n✅ LightRek 启动完成!")

        if not args.no_web:
            print(f"🌐 Web界面访问地址:")
            print(f"   🏠 本地: {web_server.get_url()}")
            print(f"   🌍 外部: {web_server.get_external_url()}")
        
        print("按 Ctrl+C 退出程序")
        
        # 主循环
        try:
            while True:
                time.sleep(1)
                
        except KeyboardInterrupt:
            print("\n🛑 收到停止信号")
        
        # 清理资源
        print("🧹 清理资源...")
        if web_server:
            web_server.stop()
        
        if task_manager:
            # 停止所有任务（如果有这个方法）
            if hasattr(task_manager, 'stop_all_tasks'):
                task_manager.stop_all_tasks()
        
        print("👋 LightRek 已退出")
        return 0
        
    except ImportError as e:
        print(f"❌ 导入模块失败: {e}")
        print("请确保所有依赖模块都已正确安装")
        return 1
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


def show_help():
    """显示帮助信息"""
    help_text = """
🚀 LightRek 统一存储同步工具 v2.0.0

用法:
    python lightrek.py [选项]

选项:
    --port PORT         Web界面端口 (默认: 8001)
    --config FILE       配置文件路径 (默认: lightrek_unified_config.json)
    --no-web           不启动Web界面
    --version          显示版本信息
    --help             显示此帮助信息

示例:
    python lightrek.py                          # 使用默认设置启动
    python lightrek.py --port 8080              # 使用8080端口启动Web界面
    python lightrek.py --no-web                 # 只启动后台服务，不启动Web界面

功能特性:
    ✅ 支持多种存储类型 (S3, SFTP, FTP, SMB, 本地存储)
    ✅ 实时任务监控和日志查看
    ✅ 增量同步和压缩传输
    ✅ 并发处理和性能优化
    ✅ 友好的Web管理界面
    ✅ 完整的配置管理功能

更多信息请访问: http://localhost:8001/manual
"""
    print(help_text)


if __name__ == "__main__":
    # 检查是否请求帮助
    if len(sys.argv) > 1 and sys.argv[1] in ['--help', '-h', 'help']:
        show_help()
        sys.exit(0)
    
    # 运行主程序
    exit_code = main()
    
    if exit_code != 0:
        print("\n" + "=" * 50)
        print("💡 提示:")
        print("   • 运行 'python lightrek.py --help' 查看帮助")
        print("   • 检查Python版本是否为3.8+")
        print("   • 确保所有依赖文件都在当前目录")
        print("   • 查看上方的错误信息进行排查")
    
    sys.exit(exit_code)
