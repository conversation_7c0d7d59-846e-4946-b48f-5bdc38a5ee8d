#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LightRek 综合功能测试脚本
"""

import requests
import json
import time
import tempfile
import os
from pathlib import Path

class LightRekTester:
    """LightRek综合测试器"""
    
    def __init__(self, base_url="http://localhost:8001"):
        self.base_url = base_url
        self.test_results = {}
        
    def run_comprehensive_test(self):
        """运行综合测试"""
        print("🧪 LightRek 综合功能测试开始")
        print("=" * 60)
        
        # 1. 基础连接测试
        if not self.test_basic_connectivity():
            print("❌ 基础连接失败，停止测试")
            return False
        
        # 2. API端点测试
        self.test_api_endpoints()
        
        # 3. 存储配置测试
        self.test_storage_configuration()
        
        # 4. 任务管理测试
        self.test_task_management()
        
        # 5. 压缩传输测试
        self.test_compressed_transfer()
        
        # 6. 文件树API测试
        self.test_file_tree_api()
        
        # 7. 生成测试报告
        self.generate_test_report()
        
        return True
    
    def test_basic_connectivity(self):
        """测试基础连接"""
        print("\n🔌 1. 基础连接测试")
        print("-" * 30)
        
        try:
            response = requests.get(self.base_url, timeout=10)
            if response.status_code == 200:
                print("✅ Web界面连接成功")
                self.test_results['connectivity'] = {'passed': True}
                return True
            else:
                print(f"❌ Web界面响应异常: HTTP {response.status_code}")
                self.test_results['connectivity'] = {
                    'passed': False,
                    'error': f'HTTP {response.status_code}'
                }
                return False
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            self.test_results['connectivity'] = {
                'passed': False,
                'error': str(e)
            }
            return False
    
    def test_api_endpoints(self):
        """测试API端点"""
        print("\n🔌 2. API端点测试")
        print("-" * 30)
        
        endpoints = {
            '/api/sources': '数据源API',
            '/api/targets': '目标API', 
            '/api/tasks': '任务API',
            '/api/file-tree?storage_id=test&path=': '文件树API'
        }
        
        api_results = {}
        for endpoint, description in endpoints.items():
            try:
                response = requests.get(f"{self.base_url}{endpoint}", timeout=5)
                
                # 检查响应是否为有效JSON
                try:
                    json_data = response.json()
                    is_valid_json = True
                except:
                    is_valid_json = False
                
                passed = response.status_code == 200 and is_valid_json
                api_results[endpoint] = {
                    'description': description,
                    'status_code': response.status_code,
                    'valid_json': is_valid_json,
                    'passed': passed
                }
                
                status = "✅" if passed else "❌"
                print(f"{status} {description}: HTTP {response.status_code}, JSON: {is_valid_json}")
                
            except Exception as e:
                api_results[endpoint] = {
                    'description': description,
                    'passed': False,
                    'error': str(e)
                }
                print(f"❌ {description}: {e}")
        
        self.test_results['api_endpoints'] = api_results
    
    def test_storage_configuration(self):
        """测试存储配置功能"""
        print("\n💾 3. 存储配置测试")
        print("-" * 30)
        
        # 测试添加S3存储配置
        s3_config = {
            "name": "测试S3存储",
            "description": "自动化测试用S3存储",
            "storage_type": "s3",
            "endpoint": "https://s3.amazonaws.com",
            "region": "us-east-1",
            "access_key": "test_access_key",
            "secret_key": "test_secret_key",
            "bucket": "test-bucket"
        }
        
        try:
            # 添加数据源
            response = requests.post(
                f"{self.base_url}/api/sources",
                json=s3_config,
                timeout=10
            )
            
            if response.status_code == 200:
                print("✅ S3存储配置添加成功")
                
                # 验证配置是否保存
                get_response = requests.get(f"{self.base_url}/api/sources", timeout=5)
                if get_response.status_code == 200:
                    sources = get_response.json()
                    if sources and len(sources) > 0:
                        print("✅ 存储配置保存验证成功")
                        self.test_results['storage_config'] = {'passed': True}
                    else:
                        print("❌ 存储配置保存验证失败")
                        self.test_results['storage_config'] = {
                            'passed': False,
                            'error': '配置未保存'
                        }
                else:
                    print("❌ 无法验证存储配置")
                    self.test_results['storage_config'] = {
                        'passed': False,
                        'error': '验证请求失败'
                    }
            else:
                print(f"❌ 存储配置添加失败: HTTP {response.status_code}")
                self.test_results['storage_config'] = {
                    'passed': False,
                    'error': f'HTTP {response.status_code}'
                }
                
        except Exception as e:
            print(f"❌ 存储配置测试异常: {e}")
            self.test_results['storage_config'] = {
                'passed': False,
                'error': str(e)
            }
    
    def test_task_management(self):
        """测试任务管理功能"""
        print("\n📋 4. 任务管理测试")
        print("-" * 30)
        
        # 获取现有任务
        try:
            response = requests.get(f"{self.base_url}/api/tasks", timeout=5)
            if response.status_code == 200:
                tasks = response.json()
                print(f"✅ 任务列表获取成功，当前任务数: {len(tasks)}")
                self.test_results['task_management'] = {'passed': True}
            else:
                print(f"❌ 任务列表获取失败: HTTP {response.status_code}")
                self.test_results['task_management'] = {
                    'passed': False,
                    'error': f'HTTP {response.status_code}'
                }
        except Exception as e:
            print(f"❌ 任务管理测试异常: {e}")
            self.test_results['task_management'] = {
                'passed': False,
                'error': str(e)
            }
    
    def test_compressed_transfer(self):
        """测试压缩传输功能"""
        print("\n📦 5. 压缩传输测试")
        print("-" * 30)
        
        # 测试压缩传输API端点
        try:
            # 这里只测试API端点是否存在，不执行实际传输
            test_data = {
                "source_id": "test_source",
                "target_id": "test_target",
                "selected_paths": ["/test/path"],
                "format": "zip",
                "compression_level": 6
            }
            
            response = requests.post(
                f"{self.base_url}/api/compressed-transfer",
                json=test_data,
                timeout=5
            )
            
            # 预期会失败（因为存储不存在），但API应该响应
            if response.status_code in [200, 400, 404]:
                print("✅ 压缩传输API端点正常")
                self.test_results['compressed_transfer'] = {'passed': True}
            else:
                print(f"❌ 压缩传输API异常: HTTP {response.status_code}")
                self.test_results['compressed_transfer'] = {
                    'passed': False,
                    'error': f'HTTP {response.status_code}'
                }
                
        except Exception as e:
            print(f"❌ 压缩传输测试异常: {e}")
            self.test_results['compressed_transfer'] = {
                'passed': False,
                'error': str(e)
            }
    
    def test_file_tree_api(self):
        """测试文件树API"""
        print("\n🌳 6. 文件树API测试")
        print("-" * 30)
        
        test_cases = [
            {
                'storage_id': 'test',
                'path': '',
                'expected_error': '存储配置不存在'
            },
            {
                'storage_id': 'nonexistent',
                'path': '/some/path',
                'expected_error': '存储配置不存在'
            }
        ]
        
        passed_tests = 0
        for i, test_case in enumerate(test_cases):
            try:
                url = f"{self.base_url}/api/file-tree?storage_id={test_case['storage_id']}&path={test_case['path']}"
                response = requests.get(url, timeout=5)
                
                if response.status_code == 200:
                    data = response.json()
                    if not data.get('success') and test_case['expected_error'] in data.get('error', ''):
                        print(f"✅ 文件树测试 {i+1}: 预期错误正确返回")
                        passed_tests += 1
                    else:
                        print(f"❌ 文件树测试 {i+1}: 响应内容不符合预期")
                else:
                    print(f"❌ 文件树测试 {i+1}: HTTP {response.status_code}")
                    
            except Exception as e:
                print(f"❌ 文件树测试 {i+1}: {e}")
        
        self.test_results['file_tree_api'] = {
            'passed': passed_tests == len(test_cases),
            'passed_tests': passed_tests,
            'total_tests': len(test_cases)
        }
    
    def generate_test_report(self):
        """生成测试报告"""
        print("\n📊 7. 测试报告")
        print("-" * 30)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() 
                          if isinstance(result, dict) and result.get('passed', False))
        
        print(f"总测试模块: {total_tests}")
        print(f"通过模块: {passed_tests}")
        print(f"成功率: {(passed_tests/total_tests*100):.1f}%")
        
        print("\n详细结果:")
        for test_name, result in self.test_results.items():
            status = "✅" if result.get('passed', False) else "❌"
            print(f"  {status} {test_name}")
            if not result.get('passed', False) and 'error' in result:
                print(f"    错误: {result['error']}")
        
        # 保存详细报告
        report = {
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'summary': {
                'total_tests': total_tests,
                'passed_tests': passed_tests,
                'success_rate': f"{(passed_tests/total_tests*100):.1f}%"
            },
            'detailed_results': self.test_results
        }
        
        report_file = Path(__file__).parent / 'comprehensive_test_report.json'
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 详细报告已保存: {report_file}")
        
        if passed_tests == total_tests:
            print("\n🎉 所有测试通过！LightRek功能完全正常。")
        else:
            print(f"\n⚠️ {total_tests - passed_tests} 个测试失败，请检查详细报告。")


if __name__ == "__main__":
    tester = LightRekTester()
    tester.run_comprehensive_test()
