#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试SMB串行下载机制
"""

import smb_storage_adapter
from storage_abstraction import StorageFactory, SMBStorageConfig, StorageType
import threading
import time

def test_serial_download():
    """测试串行下载机制"""
    print("🧪 测试SMB串行下载机制")

    config = SMBStorageConfig(
        storage_type=StorageType.SMB,
        hostname='<PERSON><PERSON>',
        port=445,
        username='smb',
        password='smbsmb',
        domain='WORKGROUP',
        share_name='hlmj',
        root_path=''
    )

    # 使用预定义的文件列表，避免list_files的潜在影响
    test_files = [
        {'key': '398.xml', 'size': 3605},
        {'key': '398_MonitorData.ini', 'size': 9148},
        {'key': 'BugCrashReporter.exe', 'size': 100864}
    ]

    print(f"🎯 测试串行下载 {len(test_files)} 个文件:")
    for i, f in enumerate(test_files):
        print(f"  {i+1}. {f['key']} ({f['size']} bytes)")

    # 为每个测试创建新的适配器实例
    def create_adapter():
        return StorageFactory.create_adapter(config)
    
    # 单线程下载测试
    print("\n📥 单线程下载测试:")
    start_time = time.time()
    success_count = 0

    for i, file_info in enumerate(test_files):
        print(f"  下载 {i+1}/{len(test_files)}: {file_info['key']}")
        try:
            adapter = create_adapter()  # 每次创建新的适配器
            data = adapter.get_file(file_info['key'])
            if data:
                print(f"    ✅ 成功: {len(data)} bytes")
                success_count += 1
            else:
                print(f"    ❌ 失败: 返回None")
        except Exception as e:
            print(f"    ❌ 异常: {e}")
    
    single_thread_time = time.time() - start_time
    print(f"  单线程总耗时: {single_thread_time:.2f}s")
    print(f"  成功率: {success_count}/{len(test_files)}")
    
    # 多线程下载测试（应该被串行化）
    print("\n🔄 多线程下载测试（应该被串行化）:")
    results = {}
    threads = []
    
    def download_worker(file_info, thread_id):
        """下载工作线程"""
        print(f"    线程{thread_id} 开始下载: {file_info['key']}")
        start = time.time()
        try:
            adapter = create_adapter()  # 每个线程创建新的适配器
            data = adapter.get_file(file_info['key'])
            end = time.time()
            if data:
                results[thread_id] = {
                    'success': True,
                    'size': len(data),
                    'time': end - start,
                    'file': file_info['key']
                }
                print(f"    线程{thread_id} ✅ 成功: {len(data)} bytes, 耗时: {end-start:.2f}s")
            else:
                results[thread_id] = {
                    'success': False,
                    'error': 'None returned',
                    'time': end - start,
                    'file': file_info['key']
                }
                print(f"    线程{thread_id} ❌ 失败: 返回None")
        except Exception as e:
            end = time.time()
            results[thread_id] = {
                'success': False,
                'error': str(e),
                'time': end - start,
                'file': file_info['key']
            }
            print(f"    线程{thread_id} ❌ 异常: {e}")
    
    # 启动多个线程
    start_time = time.time()
    for i, file_info in enumerate(test_files):
        thread = threading.Thread(
            target=download_worker,
            args=(file_info, i+1)
        )
        threads.append(thread)
        thread.start()
    
    # 等待所有线程完成
    for thread in threads:
        thread.join()
    
    multi_thread_time = time.time() - start_time
    print(f"  多线程总耗时: {multi_thread_time:.2f}s")
    
    # 分析结果
    successful_downloads = sum(1 for r in results.values() if r['success'])
    print(f"  成功率: {successful_downloads}/{len(test_files)}")
    
    if successful_downloads > 0:
        avg_time = sum(r['time'] for r in results.values() if r['success']) / successful_downloads
        print(f"  平均下载时间: {avg_time:.2f}s")
    
    # 验证串行化效果
    if multi_thread_time >= single_thread_time * 0.8:
        print("  ✅ 串行化生效：多线程时间接近单线程时间")
        serial_effective = True
    else:
        print("  ⚠️ 串行化可能未生效：多线程时间明显短于单线程时间")
        serial_effective = False
    
    return successful_downloads >= len(test_files) * 0.8 and serial_effective

def test_connection_stability():
    """测试连接稳定性"""
    print("\n🧪 测试连接稳定性")
    
    config = SMBStorageConfig(
        storage_type=StorageType.SMB,
        hostname='Jayce',
        port=445,
        username='smb',
        password='smbsmb',
        domain='WORKGROUP',
        share_name='hlmj',
        root_path=''
    )
    
    adapter = StorageFactory.create_adapter(config)
    
    # 连续连接测试
    success_count = 0
    test_count = 5
    
    for i in range(test_count):
        print(f"  连接测试 {i+1}/{test_count}")
        try:
            success, message = adapter.test_connection()
            if success:
                print(f"    ✅ 成功: {message}")
                success_count += 1
            else:
                print(f"    ❌ 失败: {message}")
        except Exception as e:
            print(f"    ❌ 异常: {e}")
        
        # 短暂延迟
        time.sleep(0.5)
    
    print(f"  连接稳定性: {success_count}/{test_count}")
    return success_count >= test_count * 0.8

def main():
    """主函数"""
    print("🧪 SMB串行下载机制测试")
    print("=" * 60)
    
    tests = [
        ("连接稳定性测试", test_connection_stability),
        ("串行下载机制测试", test_serial_download),
    ]
    
    passed_tests = 0
    
    for test_name, test_func in tests:
        print(f"\n📋 执行测试: {test_name}")
        
        try:
            if test_func():
                print(f"✅ {test_name} 通过")
                passed_tests += 1
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
            import traceback
            traceback.print_exc()
    
    print(f"\n📊 测试结果: {passed_tests}/{len(tests)} 通过")
    
    if passed_tests >= len(tests):
        print("🎉 SMB串行下载机制工作正常！")
        print("")
        print("📋 修复内容:")
        print("  ✅ 实现了SMB连接管理器")
        print("  ✅ 添加了串行下载锁机制")
        print("  ✅ 改进了连接重试和错误处理")
        print("  ✅ 减少了并发连接冲突")
        print("")
        print("🚀 现在可以重新运行同步任务:")
        print("  - SMB下载应该更稳定")
        print("  - 减少连接错误和资源冲突")
        print("  - 避免'credits'和套接字错误")
        
        return 0
    else:
        print("❌ SMB串行下载机制仍有问题，需要进一步调试")
        return 1

if __name__ == "__main__":
    exit(main())
