#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试所有存储类型的配置功能
"""

import os
import tempfile
from unified_config_manager import UnifiedConfigManager

def test_all_storage_configs():
    """测试所有存储类型的配置"""
    print('=== 所有存储类型配置测试 ===')

    # 创建临时配置文件
    temp_config = tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False)
    temp_config.close()
    config_file = temp_config.name

    try:
        config_manager = UnifiedConfigManager(config_file)

        print('\n--- 测试S3存储配置 ---')
        s3_config = {
            'name': 'AWS S3存储',
            'description': 'Amazon S3对象存储',
            'access_key': 'AKIAIOSFODNN7EXAMPLE',
            'secret_key': 'wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY',
            'endpoint': 'https://s3.amazonaws.com',
            'region': 'us-east-1',
            'bucket': 'my-test-bucket'
        }
        success = config_manager.add_source('s3_source', 's3', s3_config)
        print(f'添加S3源: {success}')
        
        s3_retrieved = config_manager.get_source('s3_source')
        if s3_retrieved:
            print(f'S3配置验证: type={s3_retrieved.storage_type}, bucket={s3_retrieved.bucket}, region={s3_retrieved.region}')

        print('\n--- 测试SFTP存储配置 ---')
        sftp_config = {
            'name': 'SFTP服务器',
            'description': 'SSH文件传输服务器',
            'hostname': 'sftp.example.com',
            'port': 22,
            'username': 'testuser',
            'password': 'testpass',
            'root_path': '/home/<USER>/data'
        }
        success = config_manager.add_source('sftp_source', 'sftp', sftp_config)
        print(f'添加SFTP源: {success}')
        
        sftp_retrieved = config_manager.get_source('sftp_source')
        if sftp_retrieved:
            print(f'SFTP配置验证: type={sftp_retrieved.storage_type}, host={sftp_retrieved.hostname}, port={sftp_retrieved.port}')

        print('\n--- 测试SMB存储配置 ---')
        smb_config = {
            'name': 'Windows共享',
            'description': 'Windows网络共享文件夹',
            'hostname': 'fileserver.company.com',
            'port': 445,
            'username': 'domain\\user',
            'password': 'password123',
            'domain': 'COMPANY',
            'share_name': 'shared_data',
            'root_path': '/backup'
        }
        success = config_manager.add_target('smb_target', 'smb', smb_config)
        print(f'添加SMB目标: {success}')
        
        smb_retrieved = config_manager.get_target('smb_target')
        if smb_retrieved:
            print(f'SMB配置验证: type={smb_retrieved.storage_type}, host={smb_retrieved.hostname}, share={smb_retrieved.share_name}')

        print('\n--- 测试FTP存储配置 ---')
        ftp_config = {
            'name': 'FTP服务器',
            'description': 'FTP文件传输服务器',
            'hostname': 'ftp.example.com',
            'port': 21,
            'username': 'ftpuser',
            'password': 'ftppass',
            'use_tls': True,
            'passive_mode': True,
            'root_path': '/uploads'
        }
        success = config_manager.add_target('ftp_target', 'ftp', ftp_config)
        print(f'添加FTP目标: {success}')
        
        ftp_retrieved = config_manager.get_target('ftp_target')
        if ftp_retrieved:
            print(f'FTP配置验证: type={ftp_retrieved.storage_type}, host={ftp_retrieved.hostname}, tls={ftp_retrieved.use_tls}')

        print('\n--- 测试本地存储配置 ---')
        local_config = {
            'name': '本地备份目录',
            'description': '本地文件系统备份位置',
            'root_path': 'D:/backup/data'
        }
        success = config_manager.add_target('local_target', 'local', local_config)
        print(f'添加本地目标: {success}')
        
        local_retrieved = config_manager.get_target('local_target')
        if local_retrieved:
            print(f'本地配置验证: type={local_retrieved.storage_type}, path={local_retrieved.root_path}')

        print('\n--- 测试阿里云OSS配置 ---')
        oss_config = {
            'name': '阿里云OSS',
            'description': '阿里云对象存储服务',
            'access_key': 'LTAI4G...',
            'secret_key': 'abc123...',
            'endpoint': 'oss-cn-hangzhou.aliyuncs.com',
            'region': 'cn-hangzhou',
            'bucket': 'my-oss-bucket'
        }
        success = config_manager.add_source('oss_source', 's3', oss_config)
        print(f'添加阿里云OSS源: {success}')
        
        oss_retrieved = config_manager.get_source('oss_source')
        if oss_retrieved:
            print(f'OSS配置验证: type={oss_retrieved.storage_type}, endpoint={oss_retrieved.endpoint}')
            print(f'OSS端点处理: {oss_retrieved.endpoint}')  # 应该自动添加https://

        print('\n--- 测试配置统计 ---')
        all_sources = config_manager.get_all_sources()
        all_targets = config_manager.get_all_targets()
        
        print(f'总数据源数量: {len(all_sources)}')
        for source_id, source_data in all_sources.items():
            print(f'  - {source_id}: {source_data.get("name")} ({source_data.get("storage_type")})')
        
        print(f'总目标存储数量: {len(all_targets)}')
        for target_id, target_data in all_targets.items():
            print(f'  - {target_id}: {target_data.get("name")} ({target_data.get("storage_type")})')

        print('\n--- 测试复杂任务配置 ---')
        complex_task_config = {
            'name': '多类型存储同步任务',
            'description': '从SFTP同步到本地和SMB',
            'source_id': 'sftp_source',
            'target_id': 'local_target',
            'sync_mode': 'incremental',
            'schedule': 'daily',
            'schedule_time': '02:00',
            'filters': {
                'include_patterns': ['*.txt', '*.pdf'],
                'exclude_patterns': ['temp/*', '*.tmp'],
                'max_file_size_mb': 100
            },
            'options': {
                'enable_compression': True,
                'verify_integrity': True,
                'preserve_permissions': False,
                'max_workers': 10
            }
        }
        
        success = config_manager.add_task('complex_sync_task', complex_task_config)
        print(f'添加复杂任务: {success}')
        
        complex_task = config_manager.get_task('complex_sync_task')
        if complex_task:
            print(f'复杂任务验证: name={complex_task.get("name")}')
            print(f'  过滤器: {complex_task.get("filters")}')
            print(f'  选项: {complex_task.get("options")}')

        print('\n--- 测试错误处理 ---')
        # 测试无效存储类型
        try:
            invalid_config = {'name': '无效存储', 'some_param': 'value'}
            success = config_manager.add_source('invalid_source', 'invalid_type', invalid_config)
            print(f'添加无效存储类型: {success}')
        except Exception as e:
            print(f'无效存储类型异常（预期）: {e}')

        # 测试缺少必需参数
        try:
            incomplete_s3_config = {
                'name': '不完整S3配置',
                'access_key': 'key',
                # 缺少secret_key, endpoint等
            }
            success = config_manager.add_source('incomplete_s3', 's3', incomplete_s3_config)
            print(f'添加不完整S3配置: {success}')
        except Exception as e:
            print(f'不完整配置异常（预期）: {e}')

    finally:
        # 清理临时文件
        if os.path.exists(config_file):
            os.unlink(config_file)
            print(f'\n清理临时配置文件: {config_file}')

    print('所有存储类型配置测试完成')

if __name__ == '__main__':
    test_all_storage_configs()
