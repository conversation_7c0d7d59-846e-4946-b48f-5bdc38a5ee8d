#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LightRek 统一存储同步工具 - Web界面HTML模板
"""

import json
import base64
import os


class WebTemplates:
    """Web界面HTML模板类"""
    
    def __init__(self):
        self.logo_base64 = self._load_logo()
    
    def _load_logo(self):
        """加载logo文件并转换为base64"""
        logo_base64 = ""
        try:
            logo_paths = [
                "lightrek logo 64px.png",
                "./lightrek logo 64px.png",
                os.path.join(os.path.dirname(__file__), "lightrek logo 64px.png"),
                os.path.join(os.getcwd(), "lightrek logo 64px.png")
            ]
            
            for logo_path in logo_paths:
                if os.path.exists(logo_path):
                    with open(logo_path, "rb") as f:
                        logo_data = f.read()
                        logo_base64 = base64.b64encode(logo_data).decode('utf-8')
                    print(f"成功加载logo: {logo_path}")
                    break
            else:
                print("警告: 找不到logo文件，将使用默认图标")
        except Exception as e:
            print(f"无法加载logo: {e}")
        
        return logo_base64
    
    def get_dashboard_html(self, stats):
        """生成仪表盘页面HTML"""
        js_stats = json.dumps(stats)
        
        return f"""
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Lightrek S3 同步工具 - 仪表盘</title>
            <style>
                {self._get_common_styles()}
            </style>
        </head>
        <body>
            <div class="sidebar">
                {self._get_sidebar_html()}
            </div>
            
            <div class="main-content">
                <div class="page-header">
                    <h2>📊 系统仪表盘</h2>
                    <p>实时监控同步任务状态和系统性能</p>
                </div>
                
                <div class="stats-grid" id="stats-grid">
                    <!-- 统计卡片将通过JavaScript动态生成 -->
                </div>
                
                <div class="chart-section">
                    <div class="chart-card">
                        <div class="chart-title">📈 任务执行趋势</div>
                        <div id="execution-chart">暂无数据</div>
                    </div>
                    <div class="chart-card">
                        <div class="chart-title">💾 存储使用情况</div>
                        <div id="storage-chart">暂无数据</div>
                    </div>
                </div>
                
                <div class="recent-section">
                    <div class="section-title">
                        <span>🕒 最近执行记录</span>
                    </div>
                    <div id="recent-executions">
                        <!-- 最近执行记录将通过JavaScript动态加载 -->
                    </div>
                </div>
            </div>
            
            <script>
                {self._get_dashboard_javascript(js_stats)}
            </script>
        </body>
        </html>
        """
    
    def get_manual_html(self):
        """生成用户手册页面HTML"""
        return f"""
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Lightrek S3 同步工具 - 用户手册</title>
            <style>
                {self._get_common_styles()}
            </style>
        </head>
        <body>
            <div class="sidebar">
                {self._get_sidebar_html()}
            </div>
            
            <div class="main-content">
                <div class="page-header">
                    <h2>📖 用户手册</h2>
                    <p>详细的使用说明和配置指南</p>
                </div>
                
                <div class="manual-toc">
                    <h3>📋 目录</h3>
                    <ul>
                        <li><a href="#overview">系统概述</a></li>
                        <li><a href="#storage-config">存储配置</a></li>
                        <li><a href="#task-config">任务配置</a></li>
                        <li><a href="#optimization">性能优化</a></li>
                        <li><a href="#troubleshooting">故障排除</a></li>
                    </ul>
                </div>
                
                <div class="manual-content">
                    {self._get_manual_content()}
                </div>
            </div>
        </body>
        </html>
        """
    
    def _get_sidebar_html(self):
        """生成侧边栏HTML"""
        logo_img = f'<img src="data:image/png;base64,{self.logo_base64}" class="logo-img" alt="Lightrek Logo">' if self.logo_base64 else '🚀'
        
        return f"""
        <div class="logo">
            {logo_img}
            <h1>Lightrek</h1>
            <p>统一存储同步工具</p>
        </div>
        
        <nav>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="#dashboard" class="nav-link active" onclick="showPage('dashboard')">
                        <span class="nav-icon">📊</span>
                        仪表盘
                    </a>
                </li>
                
                <li class="nav-item">
                    <div class="nav-link nav-group-header" onclick="toggleSubmenu('config-menu')">
                        <span class="nav-icon">⚙️</span>
                        配置管理
                        <span class="nav-arrow">▼</span>
                    </div>
                    <ul class="nav-submenu" id="config-menu">
                        <li class="nav-subitem">
                            <a href="#sources" class="nav-sublink" onclick="showPage('sources')">
                                <span class="nav-icon">📁</span>
                                数据源
                            </a>
                        </li>
                        <li class="nav-subitem">
                            <a href="#targets" class="nav-sublink" onclick="showPage('targets')">
                                <span class="nav-icon">🎯</span>
                                目标存储
                            </a>
                        </li>
                        <li class="nav-subitem">
                            <a href="#tasks" class="nav-sublink" onclick="showPage('tasks')">
                                <span class="nav-icon">📋</span>
                                同步任务
                            </a>
                        </li>
                    </ul>
                </li>
                
                <li class="nav-item">
                    <a href="#logs" class="nav-link" onclick="showPage('logs')">
                        <span class="nav-icon">📄</span>
                        任务日志
                    </a>
                </li>
                
                <li class="nav-item">
                    <a href="#optimization" class="nav-link" onclick="showPage('optimization')">
                        <span class="nav-icon">🚀</span>
                        性能优化
                    </a>
                </li>
                
                <li class="nav-item">
                    <a href="#manual" class="nav-link" onclick="showPage('manual')">
                        <span class="nav-icon">📖</span>
                        用户手册
                    </a>
                </li>
            </ul>
        </nav>
        """
    
    def _get_common_styles(self):
        """获取通用CSS样式"""
        return """
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            min-height: 100vh;
            display: flex;
            color: #333333;
        }
        
        .sidebar {
            width: 280px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 2px 0 20px rgba(0, 0, 0, 0.1);
            padding: 20px;
            overflow-y: auto;
            border-right: 1px solid #e0e0e0;
        }
        
        .logo {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e0e0e0;
        }
        
        .logo-img {
            width: 48px;
            height: 48px;
            margin-bottom: 10px;
        }
        
        .logo h1 {
            color: #ff6b35;
            font-size: 1.8rem;
            margin-bottom: 5px;
            font-weight: 700;
        }
        
        .logo p {
            color: #666;
            font-size: 0.9rem;
        }
        
        .nav-menu {
            list-style: none;
        }
        
        .nav-item {
            margin-bottom: 8px;
        }
        
        .nav-link {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            color: #555;
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-weight: 500;
            cursor: pointer;
        }
        
        .nav-link:hover {
            background: rgba(255, 107, 53, 0.1);
            color: #ff6b35;
            transform: translateX(5px);
        }
        
        .nav-link.active {
            background: linear-gradient(135deg, #ff6b35, #ff8c42);
            color: white;
            box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
        }
        
        .nav-icon {
            margin-right: 12px;
            font-size: 1.2rem;
        }
        
        .main-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }
        
        .page-header {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        
        .page-header h2 {
            font-size: 2rem;
            margin-bottom: 10px;
            color: #ff6b35;
        }
        
        .page-header p {
            font-size: 1rem;
            opacity: 0.8;
            color: #666;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid #e0e0e0;
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            border-color: #ff6b35;
        }

        .stat-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 15px;
        }

        .stat-icon {
            font-size: 2rem;
            width: 50px;
            height: 50px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .stat-icon.sources { background: linear-gradient(45deg, #4CAF50, #45a049); }
        .stat-icon.targets { background: linear-gradient(45deg, #2196F3, #1976D2); }
        .stat-icon.tasks { background: linear-gradient(45deg, #FF9800, #F57C00); }
        .stat-icon.executions { background: linear-gradient(45deg, #9C27B0, #7B1FA2); }

        .stat-title {
            font-size: 1.1rem;
            color: #333;
            font-weight: 600;
        }

        .stat-value {
            font-size: 2.5rem;
            font-weight: bold;
            color: #ff6b35;
            margin-bottom: 8px;
        }

        .stat-description {
            color: #666;
            font-size: 0.9rem;
        }

        .recent-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid #e0e0e0;
        }

        .section-title {
            font-size: 1.4rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .nav-group-header {
            position: relative;
        }

        .nav-arrow {
            position: absolute;
            right: 16px;
            transition: transform 0.3s ease;
            font-size: 0.8rem;
        }

        .nav-arrow.expanded {
            transform: rotate(180deg);
        }

        .nav-submenu {
            list-style: none;
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
            background: rgba(0, 0, 0, 0.05);
            border-radius: 0 0 8px 8px;
            margin-top: 4px;
        }

        .nav-submenu.expanded {
            max-height: 200px;
        }

        .nav-subitem {
            margin-bottom: 4px;
        }

        .nav-sublink {
            display: flex;
            align-items: center;
            padding: 8px 16px 8px 32px;
            color: #666;
            text-decoration: none;
            border-radius: 6px;
            transition: all 0.3s ease;
            font-weight: 400;
            font-size: 0.9rem;
            cursor: pointer;
        }

        .nav-sublink:hover {
            background: rgba(255, 107, 53, 0.15);
            color: #ff6b35;
            transform: translateX(5px);
        }

        .nav-sublink.active {
            background: linear-gradient(135deg, #ff6b35, #ff8c42);
            color: white;
            box-shadow: 0 2px 8px rgba(255, 107, 53, 0.3);
        }

        .nav-sublink .nav-icon {
            font-size: 1rem;
            margin-right: 8px;
        }

        .chart-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }

        .chart-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid #e0e0e0;
        }

        .chart-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
            text-align: center;
        }

        .recent-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 10px;
            background: rgba(0, 0, 0, 0.02);
            border-left: 4px solid #ff6b35;
            border: 1px solid #e0e0e0;
            transition: all 0.3s ease;
        }

        .recent-item:hover {
            background: #f8f9fa;
            border-color: #ff6b35;
            transform: translateX(5px);
            box-shadow: 0 4px 15px rgba(255, 107, 53, 0.2);
        }

        .recent-info {
            flex: 1;
        }

        .recent-name {
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }

        .recent-details {
            color: #666;
            font-size: 0.9rem;
        }

        .recent-status {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-completed { background: #d4edda; color: #155724; }
        .status-running { background: #d1ecf1; color: #0c5460; }
        .status-failed { background: #f8d7da; color: #721c24; }
        .status-idle { background: #e2e3e5; color: #383d41; }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }

        .empty-state h3 {
            margin-bottom: 10px;
            color: #999;
        }

        /* 用户手册专用样式 */
        .manual-toc {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
            border-left: 4px solid #ff6b35;
        }

        .manual-toc h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.2rem;
        }

        .manual-toc ul {
            list-style: none;
            padding-left: 0;
        }

        .manual-toc li {
            margin-bottom: 8px;
        }

        .manual-toc a {
            color: #ff6b35;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .manual-toc a:hover {
            color: #e55a2b;
            text-decoration: underline;
        }

        .manual-section {
            margin-bottom: 40px;
            padding: 25px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid #e0e0e0;
        }

        .manual-section h3 {
            color: #333;
            font-size: 1.5rem;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #ff6b35;
        }

        .manual-section h4 {
            color: #555;
            font-size: 1.2rem;
            margin: 25px 0 15px 0;
            font-weight: 600;
        }

        .manual-section p {
            line-height: 1.6;
            margin-bottom: 15px;
            color: #666;
        }

        .manual-section ul {
            margin-bottom: 20px;
            padding-left: 20px;
        }

        .manual-section li {
            margin-bottom: 8px;
            line-height: 1.5;
            color: #666;
        }

        .config-table-wrapper {
            overflow-x: auto;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .config-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
        }

        .config-table th {
            background: #ff6b35;
            color: white;
            padding: 12px 15px;
            text-align: left;
            font-weight: 600;
            font-size: 0.9rem;
        }

        .config-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #e0e0e0;
            color: #666;
            font-size: 0.9rem;
        }

        .config-table tr:nth-child(even) {
            background: #f8f9fa;
        }

        .config-table tr:hover {
            background: #e8f4f8;
        }

        .tip-box {
            background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
            border-left: 4px solid #2196f3;
            padding: 15px 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .tip-box strong {
            color: #1976d2;
            display: block;
            margin-bottom: 8px;
        }

        .tip-box ul {
            margin: 10px 0 0 0;
            padding-left: 20px;
        }

        .tip-box li {
            margin-bottom: 5px;
            color: #555;
        }

        .troubleshooting-item {
            background: #fff5f5;
            border-left: 4px solid #f56565;
            padding: 15px 20px;
            margin: 15px 0;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .troubleshooting-item strong {
            color: #c53030;
            display: block;
            margin-bottom: 10px;
            font-size: 1rem;
        }

        .troubleshooting-item ul {
            margin: 0;
            padding-left: 20px;
        }

        .troubleshooting-item li {
            margin-bottom: 6px;
            color: #666;
            line-height: 1.4;
        }
        """

    def _get_dashboard_javascript(self, js_stats):
        """获取仪表盘JavaScript代码"""
        return f"""
        // 统计数据
        const stats = {js_stats};

        // 页面切换功能
        function showPage(page) {{
            console.log('切换到页面:', page);

            // 移除所有导航链接的active类
            document.querySelectorAll('.nav-link, .nav-sublink').forEach(link => {{
                link.classList.remove('active');
            }});

            // 添加active类到当前页面
            const currentLink = document.querySelector(`[onclick*="showPage('${{page}}')"]`);
            if (currentLink) {{
                currentLink.classList.add('active');
            }}

            // 根据页面类型加载内容
            if (page === 'dashboard') {{
                loadDashboard();
            }} else if (page === 'sources') {{
                window.location.href = '/#sources';
                loadSourcesPage();
            }} else if (page === 'targets') {{
                window.location.href = '/#targets';
                loadTargetsPage();
            }} else if (page === 'tasks') {{
                window.location.href = '/#tasks';
                loadTasksPage();
            }} else if (page === 'logs') {{
                window.location.href = '/#logs';
                loadLogsPage();
            }} else if (page === 'optimization') {{
                window.location.href = '/#optimization';
                loadOptimizationPage();
            }} else if (page === 'manual') {{
                window.location.href = '/manual';
            }} else {{
                // 其他页面通过重新加载处理
                window.location.hash = page;
                window.location.reload();
            }}
        }}

        // 子菜单切换
        function toggleSubmenu(menuId) {{
            console.log('切换子菜单:', menuId);
            const submenu = document.getElementById(menuId);
            const arrow = submenu.previousElementSibling.querySelector('.nav-arrow');

            if (submenu && arrow) {{
                if (submenu.classList.contains('expanded')) {{
                    submenu.classList.remove('expanded');
                    arrow.classList.remove('expanded');
                }} else {{
                    submenu.classList.add('expanded');
                    arrow.classList.add('expanded');
                }}
            }}
        }}

        // 加载仪表盘数据
        function loadDashboard() {{
            console.log('加载仪表盘数据');
            generateStatsCards();
            loadRecentExecutions();
        }}

        // 生成统计卡片
        function generateStatsCards() {{
            const statsGrid = document.getElementById('stats-grid');
            if (!statsGrid) {{
                console.error('找不到stats-grid元素');
                return;
            }}

            const cards = [
                {{
                    icon: '📁',
                    iconClass: 'sources',
                    title: '数据源',
                    value: stats.sources || 0,
                    description: '已配置的数据源数量'
                }},
                {{
                    icon: '🎯',
                    iconClass: 'targets',
                    title: '目标存储',
                    value: stats.targets || 0,
                    description: '已配置的目标存储数量'
                }},
                {{
                    icon: '📋',
                    iconClass: 'tasks',
                    title: '同步任务',
                    value: stats.tasks || 0,
                    description: '已创建的同步任务数量'
                }},
                {{
                    icon: '🔄',
                    iconClass: 'executions',
                    title: '执行次数',
                    value: stats.executions || 0,
                    description: '总任务执行次数'
                }}
            ];

            statsGrid.innerHTML = cards.map(card => `
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-icon ${{card.iconClass}}">${{card.icon}}</div>
                        <div class="stat-title">${{card.title}}</div>
                    </div>
                    <div class="stat-value">${{card.value}}</div>
                    <div class="stat-description">${{card.description}}</div>
                </div>
            `).join('');

            console.log('统计卡片已生成');
        }}

        // 加载最近执行记录
        function loadRecentExecutions() {{
            console.log('加载最近执行记录');
            fetch('/api/task-executions')
                .then(response => response.json())
                .then(data => {{
                    console.log('执行记录数据:', data);
                    const container = document.getElementById('recent-executions');
                    if (!container) {{
                        console.error('找不到recent-executions元素');
                        return;
                    }}

                    if (data.success && data.executions && data.executions.length > 0) {{
                        container.innerHTML = data.executions.slice(0, 5).map(exec => `
                            <div class="recent-item">
                                <div class="recent-info">
                                    <div class="recent-name">${{exec.task_name || '未知任务'}}</div>
                                    <div class="recent-details">
                                        执行时间: ${{exec.start_time ? new Date(exec.start_time).toLocaleString() : '未知'}} |
                                        耗时: ${{exec.duration || '未知'}}
                                    </div>
                                </div>
                                <div class="recent-status status-${{exec.status || 'idle'}}">${{exec.status || 'idle'}}</div>
                            </div>
                        `).join('');
                    }} else {{
                        container.innerHTML = '<div class="empty-state"><h3>暂无执行记录</h3><p>还没有任务执行记录</p></div>';
                    }}
                }})
                .catch(error => {{
                    console.error('加载执行记录失败:', error);
                    const container = document.getElementById('recent-executions');
                    if (container) {{
                        container.innerHTML = '<div class="empty-state"><h3>加载失败</h3><p>无法获取执行记录</p></div>';
                    }}
                }});
        }}

        // 加载其他页面的占位函数
        function loadSourcesPage() {{
            console.log('加载数据源页面');
            alert('数据源管理页面开发中...');
        }}

        function loadTargetsPage() {{
            console.log('加载目标存储页面');
            alert('目标存储管理页面开发中...');
        }}

        function loadTasksPage() {{
            console.log('加载任务页面');
            alert('任务管理页面开发中...');
        }}

        function loadLogsPage() {{
            console.log('加载日志页面');
            alert('日志查看页面开发中...');
        }}

        function loadOptimizationPage() {{
            console.log('加载优化页面');
            alert('性能优化页面开发中...');
        }}

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {{
            console.log('页面加载完成，初始化仪表盘');
            loadDashboard();

            // 初始化子菜单状态
            const configMenu = document.getElementById('config-menu');
            if (configMenu) {{
                configMenu.classList.add('expanded');
                const arrow = document.querySelector('.nav-group-header .nav-arrow');
                if (arrow) {{
                    arrow.classList.add('expanded');
                }}
            }}
        }});

        // 全局错误处理
        window.addEventListener('error', function(e) {{
            console.error('JavaScript错误:', e.error);
        }});
        """

    def _get_manual_content(self):
        """获取用户手册内容"""
        return """
        <div class="manual-section" id="overview">
            <h3>🔍 系统概述</h3>
            <p>LightRek v2.0.0 是一个统一的存储同步工具，支持多种存储类型之间的高效数据同步，包括：</p>
            <ul>
                <li><strong>S3对象存储</strong>：Amazon S3、阿里云OSS、腾讯云COS、MinIO等</li>
                <li><strong>高性能SFTP</strong>：基于AsyncSSH的高并发SFTP传输</li>
                <li><strong>FTP</strong>：标准文件传输协议</li>
                <li><strong>SMB/CIFS</strong>：Windows网络文件共享</li>
                <li><strong>本地存储</strong>：本地文件系统</li>
            </ul>

            <h4>✨ 核心特性</h4>
            <ul>
                <li><strong>🚀 高性能传输</strong>：AsyncSSH连接池，支持高并发文件传输</li>
                <li><strong>🔄 智能增量同步</strong>：只同步变更文件，大幅提升效率</li>
                <li><strong>📊 实时监控</strong>：Web界面实时显示同步进度和状态</li>
                <li><strong>⏰ 定时调度</strong>：支持分钟、小时、天级别的定时同步</li>
                <li><strong>🛡️ 数据完整性</strong>：文件大小和修改时间校验</li>
                <li><strong>📁 文件浏览</strong>：支持在线浏览和管理存储文件</li>
            </ul>
        </div>

        <div class="manual-section" id="storage-config">
            <h3>💾 存储配置</h3>

            <h4>🗂️ S3对象存储配置</h4>
            <p>支持所有S3兼容的对象存储服务，包括阿里云OSS、腾讯云COS、AWS S3、MinIO等。</p>
            <div class="config-table-wrapper">
                <table class="config-table">
                    <thead>
                        <tr>
                            <th>参数</th>
                            <th>说明</th>
                            <th>示例</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>名称</td>
                            <td>存储配置的显示名称</td>
                            <td>阿里云OSS-生产环境</td>
                        </tr>
                        <tr>
                            <td>endpoint</td>
                            <td>S3服务端点URL</td>
                            <td>https://oss-cn-hangzhou.aliyuncs.com</td>
                        </tr>
                        <tr>
                            <td>access_key</td>
                            <td>访问密钥ID</td>
                            <td>LTAI4G...</td>
                        </tr>
                        <tr>
                            <td>secret_key</td>
                            <td>访问密钥Secret</td>
                            <td>xxx...</td>
                        </tr>
                        <tr>
                            <td>bucket</td>
                            <td>存储桶名称</td>
                            <td>my-production-bucket</td>
                        </tr>
                        <tr>
                            <td>prefix</td>
                            <td>对象前缀（可选）</td>
                            <td>backup/2024/</td>
                        </tr>
                        <tr>
                            <td>region</td>
                            <td>存储区域</td>
                            <td>cn-hangzhou</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="tip-box">
                <strong>💡 提示：</strong>配置完成后，点击"测试连接"按钮验证配置是否正确，系统会自动检测存储桶的可访问性。
            </div>

            <h4>🚀 高性能SFTP配置</h4>
            <p>基于AsyncSSH的高性能SFTP适配器，支持连接池和并发传输，传输效率比传统SFTP提升数倍。</p>
            <div class="config-table-wrapper">
                <table class="config-table">
                    <thead>
                        <tr>
                            <th>参数</th>
                            <th>说明</th>
                            <th>示例</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>名称</td>
                            <td>SFTP配置的显示名称</td>
                            <td>生产服务器SFTP</td>
                        </tr>
                        <tr>
                            <td>hostname</td>
                            <td>SFTP服务器地址</td>
                            <td>************* 或 sftp.example.com</td>
                        </tr>
                        <tr>
                            <td>port</td>
                            <td>SSH端口号</td>
                            <td>22（默认）</td>
                        </tr>
                        <tr>
                            <td>username</td>
                            <td>SSH用户名</td>
                            <td>root 或 admin</td>
                        </tr>
                        <tr>
                            <td>password</td>
                            <td>SSH密码</td>
                            <td>password123</td>
                        </tr>
                        <tr>
                            <td>private_key_path</td>
                            <td>私钥文件路径（可选）</td>
                            <td>/path/to/id_rsa</td>
                        </tr>
                        <tr>
                            <td>root_path</td>
                            <td>远程根目录</td>
                            <td>/opt/ 或 /home/<USER>/data/</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="tip-box">
                <strong>🔥 性能特性：</strong>
                <ul>
                    <li>连接池管理：自动维护8个并发连接</li>
                    <li>智能重连：连接断开时自动重连</li>
                    <li>并行传输：小文件并行处理，大文件分块传输</li>
                    <li>文件浏览：支持在线浏览SFTP目录结构</li>
                </ul>
            </div>
        </div>

        <div class="manual-section" id="task-config">
            <h3>📋 任务配置</h3>
            <p>LightRek支持灵活的同步任务配置，满足各种数据同步需求。</p>

            <h4>🎯 基本配置</h4>
            <ul>
                <li><strong>任务名称</strong>：任务的唯一标识，建议使用有意义的名称</li>
                <li><strong>数据源</strong>：选择已配置的源存储（支持所有存储类型）</li>
                <li><strong>目标存储</strong>：选择已配置的目标存储（支持所有存储类型）</li>
                <li><strong>源前缀</strong>：限制同步的源文件范围（可选）</li>
                <li><strong>目标前缀</strong>：指定目标存储的路径前缀（可选）</li>
            </ul>

            <h4>⏰ 调度配置</h4>
            <ul>
                <li><strong>手动执行</strong>：需要手动点击运行按钮</li>
                <li><strong>分钟级调度</strong>：每N分钟执行一次（1-59分钟）</li>
                <li><strong>小时级调度</strong>：每N小时执行一次（1-23小时）</li>
                <li><strong>天级调度</strong>：每天指定时间执行</li>
            </ul>

            <h4>🔧 高级选项</h4>
            <ul>
                <li><strong>增量同步</strong>：只同步新增或修改的文件（推荐）</li>
                <li><strong>删除多余文件</strong>：删除目标中源不存在的文件</li>
                <li><strong>文件过滤</strong>：支持文件名模式匹配</li>
                <li><strong>并发控制</strong>：调整同步的并发线程数</li>
            </ul>

            <div class="tip-box">
                <strong>💡 最佳实践：</strong>
                <ul>
                    <li>首次同步建议先手动执行，确认无误后再启用定时调度</li>
                    <li>大量文件同步时，建议使用增量模式以提高效率</li>
                    <li>跨网络同步时，适当调整并发数以平衡速度和稳定性</li>
                </ul>
            </div>
        </div>

        <div class="manual-section" id="optimization">
            <h3>🚀 性能优化</h3>
            <p>LightRek内置多种性能优化技术，确保高效的数据同步。</p>

            <h4>⚡ 自动优化特性</h4>
            <ul>
                <li><strong>智能文件分组</strong>：自动将文件按大小分为小、中、大文件，采用不同传输策略</li>
                <li><strong>并发传输</strong>：小文件并行传输，大文件分块传输</li>
                <li><strong>连接池管理</strong>：SFTP连接池复用，减少连接开销</li>
                <li><strong>增量同步</strong>：基于文件大小和修改时间的智能增量检测</li>
                <li><strong>断点续传</strong>：大文件传输支持断点续传</li>
            </ul>

            <h4>🔧 可调优参数</h4>
            <div class="config-table-wrapper">
                <table class="config-table">
                    <thead>
                        <tr>
                            <th>参数</th>
                            <th>说明</th>
                            <th>推荐值</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>小文件并发数</td>
                            <td>同时传输的小文件数量</td>
                            <td>8-16（根据网络条件）</td>
                        </tr>
                        <tr>
                            <td>SFTP连接池大小</td>
                            <td>SFTP连接池的连接数</td>
                            <td>8（默认，自动管理）</td>
                        </tr>
                        <tr>
                            <td>文件分组阈值</td>
                            <td>小文件(<10MB)、中文件(<100MB)、大文件(>=100MB)</td>
                            <td>系统自动分组</td>
                        </tr>
                        <tr>
                            <td>传输超时</td>
                            <td>单个文件传输的超时时间</td>
                            <td>300秒（5分钟）</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <h4>📊 性能监控</h4>
            <ul>
                <li><strong>实时进度</strong>：Web界面实时显示传输进度和速度</li>
                <li><strong>详细日志</strong>：记录每个文件的传输状态和耗时</li>
                <li><strong>错误统计</strong>：统计传输成功率和失败原因</li>
                <li><strong>性能指标</strong>：显示传输速度、并发数等关键指标</li>
            </ul>
        </div>

        <div class="manual-section" id="troubleshooting">
            <h3>🔧 故障排除</h3>

            <h4>🔍 常见问题解决</h4>

            <div class="troubleshooting-item">
                <strong>❌ S3连接测试失败</strong>
                <ul>
                    <li>检查endpoint URL是否正确（如：https://oss-cn-hangzhou.aliyuncs.com）</li>
                    <li>验证access_key和secret_key是否有效</li>
                    <li>确认存储桶名称是否存在且有访问权限</li>
                    <li>检查网络连接和防火墙设置</li>
                </ul>
            </div>

            <div class="troubleshooting-item">
                <strong>❌ SFTP连接失败</strong>
                <ul>
                    <li>确认SSH服务器地址和端口是否正确</li>
                    <li>验证用户名和密码是否正确</li>
                    <li>检查SSH服务是否启用（端口22）</li>
                    <li>确认防火墙是否允许SSH连接</li>
                    <li>如使用私钥，检查私钥文件路径和权限</li>
                </ul>
            </div>

            <div class="troubleshooting-item">
                <strong>⚠️ 同步任务执行失败</strong>
                <ul>
                    <li>查看任务执行日志获取详细错误信息</li>
                    <li>检查源和目标存储的连接状态</li>
                    <li>确认文件权限和路径访问权限</li>
                    <li>检查磁盘空间是否充足</li>
                </ul>
            </div>

            <div class="troubleshooting-item">
                <strong>🐌 同步速度慢</strong>
                <ul>
                    <li>网络带宽限制：检查网络连接质量</li>
                    <li>文件数量过多：考虑使用文件过滤减少同步范围</li>
                    <li>SFTP性能：系统已自动启用高性能SFTP适配器</li>
                    <li>并发调优：系统自动优化并发数，无需手动调整</li>
                </ul>
            </div>

            <div class="troubleshooting-item">
                <strong>📁 文件浏览失败</strong>
                <ul>
                    <li>确认存储连接正常</li>
                    <li>检查目录路径是否存在</li>
                    <li>验证用户是否有目录访问权限</li>
                    <li>对于SFTP，确认root_path配置正确</li>
                </ul>
            </div>

            <h4>📋 日志查看</h4>
            <p>系统提供详细的日志记录，帮助诊断问题：</p>
            <ul>
                <li><strong>任务日志</strong>：在Web界面的"任务执行"页面查看</li>
                <li><strong>系统日志</strong>：程序运行时在终端显示</li>
                <li><strong>错误详情</strong>：每个失败的操作都有详细的错误描述</li>
            </ul>

            <div class="tip-box">
                <strong>🆘 获取帮助：</strong>如果问题仍未解决，请记录错误信息和操作步骤，便于技术支持分析。
            </div>
        </div>
        """
