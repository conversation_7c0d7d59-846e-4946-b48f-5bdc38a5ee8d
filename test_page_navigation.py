#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试页面导航功能
"""

import requests
import json

def test_page_navigation():
    """测试页面导航功能"""
    base_url = 'http://localhost:8006'
    
    print('=== 测试页面导航功能 ===')
    
    # 首先获取主页内容
    try:
        response = requests.get(f'{base_url}/')
        if response.status_code == 200:
            content = response.text
            
            # 检查页面元素是否存在
            pages_to_check = [
                ('dashboard-page', '仪表盘页面'),
                ('sources-page', '数据源页面'),
                ('targets-page', '目标存储页面'),
                ('tasks-page', '任务页面'),
                ('logs-page', '任务日志页面'),
                ('optimization-page', '性能优化页面')
            ]
            
            print('检查页面元素:')
            for page_id, page_name in pages_to_check:
                found = f'id="{page_id}"' in content
                print(f'  {page_name} ({page_id}): {"✅" if found else "❌"}')
            
            # 检查导航链接
            nav_links = [
                ("showPage('dashboard')", '仪表盘导航'),
                ("showPage('sources')", '数据源导航'),
                ("showPage('targets')", '目标存储导航'),
                ("showPage('tasks')", '任务导航'),
                ("showPage('logs')", '任务日志导航'),
                ("showPage('optimization')", '性能优化导航')
            ]
            
            print('\\n检查导航链接:')
            for nav_call, nav_name in nav_links:
                found = nav_call in content
                print(f'  {nav_name}: {"✅" if found else "❌"}')
            
            # 检查JavaScript函数
            js_functions = [
                ('function showPage(', 'showPage函数'),
                ('function loadTaskLogs(', 'loadTaskLogs函数'),
                ('function loadOptimizationConfig(', 'loadOptimizationConfig函数'),
                ('function saveOptimizationConfig(', 'saveOptimizationConfig函数')
            ]
            
            print('\\n检查JavaScript函数:')
            for func_sig, func_name in js_functions:
                found = func_sig in content
                print(f'  {func_name}: {"✅" if found else "❌"}')
            
            # 检查CSS样式
            css_classes = [
                ('.page-content', 'page-content样式'),
                ('.page-content.active', 'active页面样式'),
                ('.nav-link.active', 'active导航样式')
            ]
            
            print('\\n检查CSS样式:')
            for css_class, css_name in css_classes:
                found = css_class in content
                print(f'  {css_name}: {"✅" if found else "❌"}')
            
        else:
            print(f'❌ 无法获取主页: {response.status_code}')
            return
            
    except Exception as e:
        print(f'❌ 获取主页时出错: {e}')
        return
    
    # 测试API端点
    print('\\n=== 测试相关API端点 ===')
    
    api_tests = [
        ('/api/optimization-config', 'GET', None, '获取优化配置'),
        ('/api/task-executions', 'GET', None, '获取任务执行历史'),
        ('/api/statistics', 'GET', None, '获取统计信息'),
        ('/api/optimization-config', 'POST', {
            'max_workers': 8,
            'chunk_size_mb': 10,
            'enable_parallel_scan': True
        }, '保存优化配置')
    ]
    
    for endpoint, method, data, description in api_tests:
        try:
            if method == 'GET':
                response = requests.get(f'{base_url}{endpoint}')
            elif method == 'POST':
                response = requests.post(f'{base_url}{endpoint}', 
                                       json=data,
                                       headers={'Content-Type': 'application/json'})
            
            success = response.status_code == 200
            print(f'  {description}: {"✅" if success else "❌"} ({response.status_code})')
            
            if success and response.headers.get('content-type', '').startswith('application/json'):
                result = response.json()
                if 'success' in result:
                    print(f'    API结果: {result.get("success")}')
                elif isinstance(result, dict):
                    print(f'    返回数据: {len(result)} 个字段')
                    
        except Exception as e:
            print(f'  {description}: ❌ 异常 - {e}')
    
    # 测试静态资源
    print('\\n=== 测试静态资源 ===')
    
    static_resources = [
        ('/style.css', 'CSS样式文件'),
        ('/app.js', 'JavaScript文件'),
        ('/manual.html', '用户手册页面')
    ]
    
    for resource, description in static_resources:
        try:
            response = requests.get(f'{base_url}{resource}')
            success = response.status_code == 200
            print(f'  {description}: {"✅" if success else "❌"} ({response.status_code})')
            
            if success:
                content_length = len(response.text)
                print(f'    文件大小: {content_length:,} 字符')
                
        except Exception as e:
            print(f'  {description}: ❌ 异常 - {e}')
    
    print('\\n页面导航功能测试完成')

if __name__ == '__main__':
    test_page_navigation()
